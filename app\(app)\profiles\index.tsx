import React, { useState, useCallback, useMemo } from 'react'; // Added useMemo
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  SafeAreaView,
  Platform,
} from 'react-native';
import { router, Stack, useFocusEffect } from 'expo-router';
import { Feather } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import Animated, {
  FadeIn,
  FadeInDown,
  // FadeOutUp, // Not explicitly used in provided snippet, review if needed
  // SlideInRight, // Not explicitly used in provided snippet, review if needed
  // Layout, // Implicitly used by Reanimated FlatList items if they animate layout
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  // withTiming, // Not directly used in provided snippet for shared values, but common
  // Easing, // Not directly used in provided snippet for shared values
} from 'react-native-reanimated';
import { useAuth } from '@/contexts/AuthContext';
import { SignificantOtherProfile } from '@/functions/src/types/firestore';
import LoadingIndicator from '@/components/ui/LoadingIndicator';
import useProfileData from '@/hooks/useProfileData';
// import Card from '@/components/ui/Card'; // Not directly used, ProfileListItem likely encapsulates Card
import Button from '@/components/ui/Button';
import { useColorScheme } from 'nativewind';
import ProfileListItem from '@/components/profile/ProfileListItem';

// --- Theme Color Placeholder (Replace with your actual theme integration) ---
const AppColors = {
  primary: '#E87900', // Example primary orange
  primaryDark: '#D96D00', // Example dark primary orange
  background: '#F9FAFB',
  backgroundDark: '#111827',
  textPrimary: '#1F2937',
  textPrimaryDark: '#F9FAFB',
  textSecondary: '#6B7280',
  textSecondaryDark: '#9CA3AF',
  error: '#DC2626',
  errorDark: '#F87171',
  white: '#FFFFFF',
  black: '#000000',
  // Add other theme colors as needed
};

const AnimatedTouchableOpacity =
  Animated.createAnimatedComponent(TouchableOpacity);

// MEDIUM 3: Enhanced getInitials function for robustness
const getInitials = (name: string | undefined | null): string => {
  if (!name || typeof name !== 'string' || name.trim() === '') {
    return '?'; // Placeholder for invalid/empty names
  }
  const names = name.trim().split(/\s+/).filter(Boolean); // Filter out empty strings from split
  if (names.length === 0) {
    return '?';
  }

  let initials = names[0][0].toUpperCase();

  if (names.length > 1 && names[names.length - 1].length > 0) {
    initials += names[names.length - 1][0].toUpperCase();
  }
  return initials;
};
// Note: `getInitials` is defined here but primarily used within `ProfileListItem`.
// Its robustness benefits `ProfileListItem`.

const ProfilesListScreen = () => {
  const { user, isLoading: isAuthLoading } = useAuth();
  const {
    profiles,
    isLoading: loading,
    error,
    refreshProfiles,
    loadMoreProfiles,
    hasMore
  } = useProfileData();
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const { colorScheme } = useColorScheme();

  const isDark = colorScheme === 'dark';

  // --- Themed Colors (Derived from AppColors and isDark) ---
  const themedColors = useMemo(() => ({
    primary: isDark ? AppColors.primaryDark : AppColors.primary,
    background: isDark ? AppColors.backgroundDark : AppColors.background,
    textPrimary: isDark ? AppColors.textPrimaryDark : AppColors.textPrimary,
    textSecondary: isDark ? AppColors.textSecondaryDark : AppColors.textSecondary,
    error: isDark ? AppColors.errorDark : AppColors.error,
    // Colors for specific UI elements, ensuring contrast
    headerTitle: isDark ? AppColors.primaryDark : AppColors.primary,
    headerBackground: isDark ? AppColors.backgroundDark : AppColors.background, // LOW 2: Use themed background
    addButtonIcon: isDark ? AppColors.primaryDark : AppColors.primary,
    emptyStateIcon: isDark ? AppColors.primaryDark : AppColors.primary,
    buttonPrimaryIcon: isDark ? AppColors.black : AppColors.white, // LOW 3: Ensure contrast
  }), [isDark]);


  const headerAddButtonScale = useSharedValue(1);
  const pressedCardId = useSharedValue<string | null>(null);

  const fetchProfiles = useCallback(async (isRefresh = false) => {
    await refreshProfiles(isRefresh);
  }, [refreshProfiles]);

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await fetchProfiles(true);
    setRefreshing(false);
  }, [fetchProfiles]);

  useFocusEffect(
    useCallback(() => {
      if (user?.uid && profiles.length === 0) {
        fetchProfiles();
      }
    }, [user?.uid, profiles.length, fetchProfiles])
  );

  const handleProfilePress = useCallback(
    (profileId: string) => {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      pressedCardId.value = profileId;
      // MEDIUM 2: setTimeout for perceived animation. Consider animation callbacks if feasible.
      setTimeout(() => {
        router.push(`/profiles/${profileId}`);
        if (pressedCardId.value === profileId) { // Check if still the same card pressed
          pressedCardId.value = null;
        }
      }, 150);
    },
    [pressedCardId] // router is stable
  );

  const handleAddProfile = useCallback(() => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
    router.push('/profiles/add');
  }, []); // router is stable

  const headerAddButtonAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: headerAddButtonScale.value }],
      opacity: withSpring(headerAddButtonScale.value < 1 ? 0.7 : 1),
    };
  });

  const handleAddButtonPressIn = () => {
    headerAddButtonScale.value = withSpring(0.9, { damping: 15, stiffness: 300 });
  };
  const handleAddButtonPressOut = () => {
    headerAddButtonScale.value = withSpring(1, { damping: 15, stiffness: 300 });
  };

  const renderProfileItem = useCallback(
    ({ item, index }: { item: SignificantOtherProfile; index: number }) => {
      return (
        <ProfileListItem
          item={item}
          index={index}
          onPress={handleProfilePress}
          pressedCardId={pressedCardId}
          // getInitials can be passed if ProfileListItem doesn't define it, or use globally defined one.
          // For this audit, assuming ProfileListItem uses its own or a shared util.
        />
      );
    },
    [handleProfilePress, pressedCardId]
  );

  const renderEmptyListComponent = useCallback(
    () => (
      <Animated.View
        entering={FadeIn.duration(800).delay(200)}
        className="flex-1 justify-center items-center p-6 -mt-10"
      >
        <View
          className={`items-center p-8 shadow-xl rounded-3xl w-full max-w-sm
            ${isDark ? 'border bg-card-dark/80 border-border-dark/20' : 'border bg-card/80 border-border/10'}`}
          style={{ backdropFilter: Platform.OS === 'ios' ? 'blur(10px)' : undefined }}
        >
          <Animated.View entering={FadeInDown.duration(600).delay(400).springify()}>
            <Feather
              name="gift" // LOW 1: Changed to a more thematic icon
              size={56}
              color={themedColors.emptyStateIcon} // Use themed color
            />
          </Animated.View>
          <Text
            className="mt-5 mb-2 text-2xl font-bold text-center"
            style={{ color: themedColors.textPrimary }}
          >
            Your Gift Circle
          </Text>
          <Text
            className="mb-8 text-base leading-relaxed text-center"
            style={{ color: themedColors.textSecondary }}
          >
            Add your loved ones here. We'll help you remember every special occasion and find the perfect gifts.
          </Text>
          <Animated.View entering={FadeInDown.duration(600).delay(700).springify()} className="w-full">
            <Button
              title="Add Your First Profile"
              variant="primary"
              onPress={handleAddProfile}
              leftIcon={<Feather name="plus-circle" size={20} color={themedColors.buttonPrimaryIcon} />}
              className="w-full shadow-lg"
            />
          </Animated.View>
        </View>
      </Animated.View>
    ),
    [handleAddProfile, isDark, themedColors]
  );

  const renderHeaderComponent = useCallback(
    () => (
      <Animated.View entering={FadeIn.duration(600).delay(100)} className="px-1 mb-5">
        <Text className="text-lg font-medium" style={{ color: themedColors.textSecondary }}>
          Your Loved Ones ✨
        </Text>
        <Text className="text-sm" style={{ color: isDark ? AppColors.textSecondaryDark+'B3' : AppColors.textSecondary+'B3' /* 80% opacity */}}>
          Keep track of your loved ones and their gift preferences.
        </Text>
      </Animated.View>
    ),
    [isDark, themedColors]
  );

  // MEDIUM 1: Refined Loading / Unauthenticated States
  if (isAuthLoading) { // Auth state is still being determined
    return (
      <SafeAreaView className="flex-1 justify-center items-center" style={{ backgroundColor: themedColors.background }}>
        <LoadingIndicator  size="large" />
        <Text className="mt-4 text-lg font-semibold" style={{ color: themedColors.textPrimary }}>
          Connecting...
        </Text>
      </SafeAreaView>
    );
  }

  // This handles initial load before user.uid is available OR if fetchProfiles explicitly sets loading
  if (loading && !refreshing && !error) { // Error state will render its own UI
    return (
      <SafeAreaView className="flex-1 justify-center items-center" style={{ backgroundColor: themedColors.background }}>
        <LoadingIndicator  size="large" />
        <Text className="mt-4 text-lg font-semibold" style={{ color: themedColors.textPrimary }}>
          Loading profiles...
        </Text>
      </SafeAreaView>
    );
  }
  
  // If not auth loading, not profiles loading, and no user -> implies unauthenticated state for this screen's purpose
  // Error state takes precedence if an error occurred during fetch attempt.
  if (!user?.uid && !error && !loading) {
     // Render a specific "Please log in" or a version of EmptyList that prompts login
     // For now, re-using EmptyListComponent with a modified message or a new component would be ideal.
     // This example assumes that if no user, the `EmptyListComponent` is acceptable,
     // but it ideally should guide to login.
     // If `profiles.length` is 0 and `!user.uid`, `ListEmptyComponent` will render anyway.
     // The logic in `useFocusEffect` already clears profiles if `!user.uid`.
  }


  if (error) {
    return (
      <SafeAreaView className="flex-1 justify-center items-center p-6" style={{ backgroundColor: themedColors.background }}>
        <Feather name="alert-circle" size={48} color={themedColors.error} />
        <Text className="mt-4 mb-2 text-xl font-semibold text-center" style={{ color: themedColors.textPrimary }}>
          Oops! Something went wrong.
        </Text>
        <Text className="mb-6 text-center" style={{ color: themedColors.error }}>
          {error}
        </Text>
        <Button
          title="Try Again"
          variant="primary"
          onPress={() => fetchProfiles(true)}
          leftIcon={<Feather name="refresh-cw" size={18} color={themedColors.buttonPrimaryIcon} />}
        />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className="flex-1" style={{ backgroundColor: themedColors.background }}>
      {/* LOW 4: Removed commented-out style from SafeAreaView */}
      <Stack.Screen
        options={{
          title: 'Gift Profiles',
          headerLargeTitle: true,
          headerLargeTitleStyle: { color: "#A3002B" },
          headerTitleStyle: { color: "#A3002B", fontWeight: '600' },
          headerStyle: { backgroundColor: themedColors.headerBackground },
          headerShadowVisible: false,
          headerRight: () => (
            <AnimatedTouchableOpacity
              style={headerAddButtonAnimatedStyle}
              onPressIn={handleAddButtonPressIn}
              onPressOut={handleAddButtonPressOut}
              onPress={handleAddProfile}
              className="flex-row justify-center items-center p-2 mr-1 rounded-full"
              accessibilityLabel="Add new profile"
              accessibilityRole="button"
            >
              <Feather name="plus-circle" size={28} color={"#A3002B"} />
            </AnimatedTouchableOpacity>
          ),
        }}
      />
      <View className="flex-1 pt-3">
        <FlatList
          data={profiles}
          renderItem={renderProfileItem}
          keyExtractor={(item) => item.profileId}
          ListEmptyComponent={renderEmptyListComponent} // Will render if profiles is empty, covers unauth case too
          ListHeaderComponent={profiles.length > 0 ? renderHeaderComponent : null}
          contentContainerStyle={
            profiles.length === 0
              ? { flexGrow: 1, justifyContent: 'center' }
              : { paddingHorizontal: 12, paddingTop: 8, paddingBottom: 30 }
          }
          refreshing={refreshing}
          onRefresh={handleRefresh}
          key={'two-columns'}
          numColumns={2}
          columnWrapperStyle={{ gap: 12 }}
          showsVerticalScrollIndicator={false}
          onEndReached={loadMoreProfiles}
          onEndReachedThreshold={0.5}
          ListFooterComponent={loading && hasMore ? <LoadingIndicator /> : null}
        />
      </View>
    </SafeAreaView>
  );
};

export default ProfilesListScreen;