import { useState, useEffect, useRef } from 'react';
import { Platform, Alert } from 'react-native';
import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import Constants from 'expo-constants';
import { useAuth } from '../contexts/AuthContext'; // Import useAuth
import { useRouter } from 'expo-router'; // Import useRouter
import { savePushToken } from '../services/firestoreService'; // Import savePushToken

export type PushNotificationStatus = 'idle' | 'loading' | 'granted' | 'denied' | 'error' | 'device_not_supported' | 'config_error';

export interface PushNotificationState {
  expoPushToken?: Notifications.ExpoPushToken;
  notification?: Notifications.Notification;
  status: PushNotificationStatus;
}

// Set foreground notification handler - Called once when the module is imported
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true, // Show alert banner even if app is open
    shouldPlaySound: true,
    shouldSetBadge: false, // Or true if you manage badges
  }),
});

export default function usePushNotifications(): PushNotificationState {
  const { user } = useAuth(); // Get user from useAuth
  const router = useRouter(); // Initialize useRouter
  const [expoPushToken, setExpoPushToken] = useState<Notifications.ExpoPushToken | undefined>(undefined);
  const [notification, setNotification] = useState<Notifications.Notification | undefined>(undefined);
  const [status, setStatus] = useState<PushNotificationStatus>('idle');
  const notificationListener = useRef<Notifications.Subscription>();
  const responseListener = useRef<Notifications.Subscription>();

  useEffect(() => {
    if (user) { // Only register if user is logged in
      setStatus('loading');
      registerForPushNotificationsAsync(setStatus, user.uid).then(token => {
        setExpoPushToken(token);
      });
    } else {
      // Optionally reset status or token if user logs out
      setStatus('idle');
      setExpoPushToken(undefined);
    }

    // This listener is fired whenever a notification is received while the app is foregrounded
    notificationListener.current = Notifications.addNotificationReceivedListener(notification => {
      console.log('Foreground Notification Received:', notification);
      setNotification(notification);
      // Removed redundant Alert.alert as setNotificationHandler handles showing the system alert
    });

    // This listener is fired whenever a user taps on or interacts with a notification (works when app is foregrounded, backgrounded, or killed)
    responseListener.current = Notifications.addNotificationResponseReceivedListener(response => {
      console.log('Notification Response Received:', response);
      const data = response.notification.request.content.data;
      console.log('Notification Data:', data);

      // Handle navigation based on notification data
      if (data && typeof data === 'object') {
        if (data.type === 'birthday' && data.profileId) {
          router.push(`/profiles/${data.profileId}`);
        } else if (data.type === 'holiday') {
          router.push('/calendar');
        }
        // Add more navigation cases as needed based on your notification types
      }
    });

    return () => {
      if (notificationListener.current) {
        Notifications.removeNotificationSubscription(notificationListener.current);
      }
      if (responseListener.current) {
        Notifications.removeNotificationSubscription(responseListener.current);
      }
    };
  }, [user, router, setStatus]); // Added setStatus to dependency array

  return {
    expoPushToken,
    notification,
    status,
  };
}


async function registerForPushNotificationsAsync(setStatus: (status: PushNotificationStatus) => void, userId?: string): Promise<Notifications.ExpoPushToken | undefined> {
  if (Platform.OS === 'android') {
    await Notifications.setNotificationChannelAsync('default', {
      name: 'default',
      importance: Notifications.AndroidImportance.MAX,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: '#FF231F7C',
    });
  }

  if (Device.isDevice) {
    const { status: existingStatus } = await Notifications.getPermissionsAsync();
    let finalStatus = existingStatus;
    if (existingStatus !== 'granted') {
      const { status } = await Notifications.requestPermissionsAsync();
      finalStatus = status;
    }
    if (finalStatus !== 'granted') {
      console.log('Failed to get push token for push notification!');
      setStatus('denied');
      return;
    }
    try {
      const projectId = Constants.expoConfig?.extra?.eas?.projectId;

      if (!projectId) {
        console.error('EAS project ID not found in Constants.expoConfig.extra.eas.projectId. Please ensure it is set in app.json under "extra.eas.projectId"');
        setStatus('config_error');
        return;
      }

      const token = (await Notifications.getExpoPushTokenAsync({ projectId })).data;
      console.log('Expo Push Token:', token);
      setStatus('granted'); // Set status to granted after successfully getting the token

      // Save the token to Firestore
      if (userId && token) {
        try {
          await savePushToken(userId, token, Platform.OS);
          console.log('Successfully saved push token to Firestore.');
        } catch (firestoreError) {
          console.error('Error saving push token to Firestore:', firestoreError);
          // Decide how to handle this error - maybe set a specific status or log it
        }
      } else {
        console.warn('Cannot save push token: User not logged in or token not available.');
      }

      return { data: token, type: 'expo' }; // Return ExpoPushToken object
    } catch (error) {
      console.error('Error getting Expo push token:', error);
      setStatus('error');
      return;
    }
  } else {
    console.log('Must use a physical device for Push Notifications');
    setStatus('device_not_supported');
  }
}