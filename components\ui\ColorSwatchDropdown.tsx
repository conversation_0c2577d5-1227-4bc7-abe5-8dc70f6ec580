import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Picker } from '@react-native-picker/picker';

interface ColorOption {
  label: string;
  value: string;
  color: string;
}

interface ColorSwatchDropdownProps {
  options: ColorOption[];
  selectedValue?: string;
  onValueChange: (value: string, index: number) => void;
  placeholder?: string;
  label?: string;
  error?: string;
  accessibilityLabel?: string;
}

const ColorSwatchDropdown: React.FC<ColorSwatchDropdownProps> = ({
  options,
  selectedValue,
  onValueChange,
  placeholder,
  label,
  error,
  accessibilityLabel,
}) => {
  const baseContainerClasses = 'mb-4';
  const baseLabelClasses = 'text-sm font-medium mb-1 text-text-secondary';
  const baseErrorClasses = 'text-sm text-feedback-error mt-1';
  const pickerClasses = 'border border-border bg-gray-50 text-text-primary rounded-md';

  return (
    <View className={baseContainerClasses}>
      {label && (
        <Text className={baseLabelClasses}>
          {label}
        </Text>
      )}
      <View className={pickerClasses}>
        <Picker
          selectedValue={selectedValue}
          onValueChange={onValueChange}
          accessibilityLabel={accessibilityLabel}
          dropdownIconColor="text-secondary"
          className="bg-background"
        >
          {placeholder && (
            <Picker.Item 
              label={placeholder} 
              value="" 
              enabled={false}
            />
          )}
          {options.map((option) => (
            <Picker.Item
              key={option.value}
              label={`◉ ${option.label}`}
              value={option.value}
              color={option.color}
            />
          ))}
        </Picker>
      </View>
      {error && (
        <Text className={baseErrorClasses}>
          {error}
        </Text>
      )}
    </View>
  );
};

export default ColorSwatchDropdown;