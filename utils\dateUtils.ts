// utils/dateUtils.ts
import { Timestamp } from 'firebase/firestore';

// Centralized time period constants
export const TIME_PERIODS = {
  RECENT_DAYS: 30,        // Standardized "recent" period
  UPCOMING_DAYS: 60,      // For upcoming dates
  ACTIVITY_DAYS: 30,      // For activity statistics
  WEEK_DAYS: 7,           // For weekly filters if needed
} as const;

export const formatDateForDisplay = (dateInput: Date | Timestamp | null | undefined): string => {
  if (!dateInput) {
    return "Select Date";
  }

  try {
    const dateObject = (dateInput instanceof Timestamp) ? dateInput.toDate() : dateInput;

    // Check if the date is valid
    if (!(dateObject instanceof Date) || isNaN(dateObject.getTime())) {
      return "Invalid Date";
    }

    return dateObject.toLocaleDateString(undefined, {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  } catch (error) {
    console.warn('Error formatting date for display:', dateInput, error);
    return "Invalid Date";
  }
};

// Specific function for formatting feedback timestamps
export const formatFeedbackTimestamp = (timestamp: any): string => {
  if (!timestamp) return 'Unknown date';
  
  try {
    let date: Date;
    
    // Handle Firestore Timestamp
    if (timestamp && typeof timestamp.toDate === 'function') {
      date = timestamp.toDate();
    } 
    // Handle Date object
    else if (timestamp instanceof Date) {
      date = timestamp;
    }
    // Handle string or number timestamps
    else {
      date = new Date(timestamp);
    }
    
    // Check if the date is valid
    if (isNaN(date.getTime())) {
      return 'Unknown date';
    }
    
    return date.toLocaleDateString();
  } catch (error) {
    console.warn('Error formatting feedback timestamp:', error);
    return 'Unknown date';
  }
};

// Helper function to safely convert timestamp to Date for sorting
export const timestampToDate = (timestamp: any): Date => {
  if (!timestamp) return new Date(0); // Return epoch for invalid timestamps
  
  try {
    // Handle Firestore Timestamp
    if (timestamp && typeof timestamp.toDate === 'function') {
      return timestamp.toDate();
    }
    // Handle Date object
    else if (timestamp instanceof Date) {
      return timestamp;
    }
    // Handle string or number timestamps
    else {
      const date = new Date(timestamp);
      return isNaN(date.getTime()) ? new Date(0) : date;
    }
  } catch (error) {
    console.warn('Error converting timestamp to date:', error);
    return new Date(0);
  }
};

// Safe date filtering utility for profile page filters
export const safeFilterByDate = <T extends { date?: any }>(
  items: T[], 
  daysAgo: number, 
  comparison: 'after' | 'before'
): T[] => {
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - daysAgo);
  
  return items.filter(item => {
    try {
      if (!item.date) {
        // Include undated items in "older" category for better UX
        return comparison === 'before';
      }
      
      const itemDate = timestampToDate(item.date);
      return comparison === 'after' 
        ? itemDate > cutoffDate 
        : itemDate <= cutoffDate;
    } catch (error) {
      console.warn('Error filtering item by date:', error);
      // Default to "older" category on error to avoid empty results
      return comparison === 'before';
    }
  });
};

// Safe date statistics calculator
export const calculateDateStats = <T extends { date?: any }>(
  items: T[], 
  daysForRecent: number = TIME_PERIODS.RECENT_DAYS
) => {
  try {
    const recentItems = safeFilterByDate(items, daysForRecent, 'after');
    const itemsWithDates = items.filter(item => {
      try {
        return item.date && !isNaN(timestampToDate(item.date).getTime());
      } catch {
        return false;
      }
    });

    const dates = itemsWithDates.map(item => timestampToDate(item.date));
    const oldestDate = dates.length > 0 
      ? dates.reduce((oldest, date) => date < oldest ? date : oldest)
      : null;
    const newestDate = dates.length > 0 
      ? dates.reduce((newest, date) => date > newest ? date : newest)
      : null;

    return {
      recentActivity: recentItems.length,
      totalWithDates: itemsWithDates.length,
      oldestDate,
      newestDate,
    };
  } catch (error) {
    console.warn('Error calculating date stats:', error);
    return {
      recentActivity: 0,
      totalWithDates: 0,
      oldestDate: null,
      newestDate: null,
    };
  }
};

// Enhanced greeting functionality
export interface GreetingData {
  text: string;
  icon: string; // Feather icon name
  timeContext: 'early-morning' | 'morning' | 'afternoon' | 'evening' | 'night' | 'late-night';
}

export const getEnhancedGreeting = (userName?: string): GreetingData => {
  const hour = new Date().getHours();
  
  let greetingData: GreetingData;
  
  if (hour >= 5 && hour < 7) {
    // Early morning (5-7 AM)
    greetingData = {
      text: `Rise and shine${userName ? `, ${userName}` : ''}!`,
      icon: 'sunrise',
      timeContext: 'early-morning'
    };
  } else if (hour >= 7 && hour < 12) {
    // Morning (7-12 PM)
    greetingData = {
      text: `Good morning${userName ? `, ${userName}` : ''}!`,
      icon: 'sun',
      timeContext: 'morning'
    };
  } else if (hour >= 12 && hour < 17) {
    // Afternoon (12-5 PM)
    greetingData = {
      text: `Good afternoon${userName ? `, ${userName}` : ''}!`,
      icon: 'sun',
      timeContext: 'afternoon'
    };
  } else if (hour >= 17 && hour < 20) {
    // Evening (5-8 PM)
    greetingData = {
      text: `Good evening${userName ? `, ${userName}` : ''}!`,
      icon: 'sunset',
      timeContext: 'evening'
    };
  } else if (hour >= 20 && hour < 23) {
    // Night (8-11 PM)
    greetingData = {
      text: `Hope your day was wonderful${userName ? `, ${userName}` : ''}!`,
      icon: 'moon',
      timeContext: 'night'
    };
  } else {
    // Late night (11 PM-5 AM)
    greetingData = {
      text: `Working late on something special${userName ? `, ${userName}` : ''}?`,
      icon: 'moon',
      timeContext: 'late-night'
    };
  }
  
  return greetingData;
};

// Get time-based background gradient (for future use)
export const getTimeBasedGradient = (timeContext: string): { from: string; to: string } => {
  switch (timeContext) {
    case 'early-morning':
      return { from: 'from-orange-100', to: 'to-pink-100' };
    case 'morning':
      return { from: 'from-yellow-50', to: 'to-orange-100' };
    case 'afternoon':
      return { from: 'from-blue-50', to: 'to-indigo-100' };
    case 'evening':
      return { from: 'from-orange-200', to: 'to-red-200' };
    case 'night':
      return { from: 'from-indigo-200', to: 'to-purple-200' };
    case 'late-night':
      return { from: 'from-slate-200', to: 'to-indigo-300' };
    default:
      return { from: 'from-primary/5', to: 'to-accent/5' };
  }
};