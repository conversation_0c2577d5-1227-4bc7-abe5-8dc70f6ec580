import { useState, useEffect, useCallback } from 'react';
import { useFocusEffect } from 'expo-router';
import { SignificantOtherProfile } from '@/functions/src/types/firestore';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { 
  SELECTED_PROFILE_KEY,
  PROFILES_LAST_UPDATED_KEY
} from '@/constants/storageKeys';
import { AppError, createAppError, safeAsync, ErrorSeverity, handleStorageError } from '@/utils/errorHandling';

interface UseProfileSelectionReturn {
  selectedProfileId: string | null;
  selectedProfile: SignificantOtherProfile | null;
  error: AppError | null;
  handleProfileSelect: (profileId: string) => Promise<void>;
  initializeSelection: (profiles: SignificantOtherProfile[]) => Promise<void>;
  syncSelectionFromStorage: (profiles: SignificantOtherProfile[]) => Promise<boolean>;
  clearError: () => void;
}

/**
 * Hook for managing profile selection - selection logic and persistence
 * Enhanced with comprehensive error handling (CQA-002)
 * Extracted from useCalendarData god object for single responsibility
 */
const useProfileSelection = (
  profiles: SignificantOtherProfile[],
  lastFetchTimestamp: number
): UseProfileSelectionReturn => {
  const [selectedProfileId, setSelectedProfileId] = useState<string | null>(null);
  const [error, setError] = useState<AppError | null>(null);

  // Get the currently selected profile object
  const selectedProfile = selectedProfileId 
    ? profiles.find(p => p.profileId === selectedProfileId) || null
    : null;

  // Enhanced initialize profile selection with error handling
  const initializeSelection = useCallback(async (availableProfiles: SignificantOtherProfile[]) => {
    setError(null);
    
    if (availableProfiles.length === 0) {
      setSelectedProfileId(null);
      return;
    }

    const { data, error: initError } = await safeAsync(
      async () => {
        // Try to load saved profile ID
        const { data: savedProfileId, error: storageError } = await safeAsync(
          () => AsyncStorage.getItem(SELECTED_PROFILE_KEY),
          'ProfileSelection.initializeSelection.getStoredProfile',
          { defaultValue: null, severity: ErrorSeverity.LOW }
        );

        if (storageError) {
          console.warn('PROFILE SELECTION: Failed to load saved profile, using first available');
        }
        
        if (savedProfileId) {
          const savedProfile = availableProfiles.find(p => p.profileId === savedProfileId);
          if (savedProfile) {
            console.log('PROFILE SELECTION: Restored saved profile:', savedProfile.name);
            return { profileId: savedProfile.profileId, profileName: savedProfile.name };
          }
        }

        // If no saved profile or it wasn't found, use the first one
        const firstProfile = availableProfiles[0];
        
        // Try to save the first profile as selected
        const { error: saveError } = await safeAsync(
          () => AsyncStorage.setItem(SELECTED_PROFILE_KEY, firstProfile.profileId),
          'ProfileSelection.initializeSelection.saveFirstProfile',
          { severity: ErrorSeverity.LOW }
        );

        if (saveError) {
          console.warn('PROFILE SELECTION: Failed to save first profile selection, proceeding anyway');
        }

        console.log('PROFILE SELECTION: Selected first profile:', firstProfile.name);
        return { profileId: firstProfile.profileId, profileName: firstProfile.name };
      },
      'ProfileSelection.initializeSelection',
      { severity: ErrorSeverity.MEDIUM }
    );

    if (initError) {
      setError(initError);
      // Fallback to first profile without saving
      if (availableProfiles.length > 0) {
        setSelectedProfileId(availableProfiles[0].profileId);
      }
    } else if (data) {
      setSelectedProfileId(data.profileId);
    }
  }, []);

  // Enhanced sync selection from AsyncStorage with error handling
  const syncSelectionFromStorage = useCallback(async (availableProfiles: SignificantOtherProfile[]): Promise<boolean> => {
    const { data, error: syncError } = await safeAsync(
      async () => {
        const { data: savedProfileId, error: storageError } = await safeAsync(
          () => AsyncStorage.getItem(SELECTED_PROFILE_KEY),
          'ProfileSelection.syncSelectionFromStorage.getStoredProfile',
          { defaultValue: null, severity: ErrorSeverity.LOW }
        );

        if (storageError) {
          console.warn('PROFILE SELECTION: Failed to sync from storage:', storageError.message);
          return { changed: false };
        }
        
        if (savedProfileId && savedProfileId !== selectedProfileId) {
          // Check if the saved profile exists in available profiles
          const savedProfile = availableProfiles.find(p => p.profileId === savedProfileId);
          if (savedProfile) {
            console.log('PROFILE SELECTION: Syncing from storage to:', savedProfile.name);
            return { profileId: savedProfileId, changed: true };
          } else {
            console.log('PROFILE SELECTION: Saved profile not found in available profiles');
            // Saved profile doesn't exist anymore, clear it and select first available
            await initializeSelection(availableProfiles);
            return { changed: true };
          }
        }
        
        return { changed: false };
      },
      'ProfileSelection.syncSelectionFromStorage',
      { severity: ErrorSeverity.LOW }
    );

    if (syncError) {
      console.error('PROFILE SELECTION: Error syncing selection from storage:', syncError);
      setError(syncError);
      return false;
    }

    if (data?.changed && data.profileId) {
      setSelectedProfileId(data.profileId);
      return true;
    }

    return data?.changed || false;
  }, [selectedProfileId, initializeSelection]);

  // Enhanced manual profile selection with error handling
  const handleProfileSelect = useCallback(async (profileId: string) => {
    if (profileId === selectedProfileId) {
      return; // No change needed
    }

    const { error: selectError } = await safeAsync(
      async () => {
        console.log('PROFILE SELECTION: Manually selecting profile:', profileId);
        
        // Update local state immediately for responsive UI
        setSelectedProfileId(profileId);

        // Try to save to AsyncStorage
        const { error: saveError } = await safeAsync(
          () => AsyncStorage.setItem(SELECTED_PROFILE_KEY, profileId),
          'ProfileSelection.handleProfileSelect.saveSelection',
          { severity: ErrorSeverity.LOW }
        );

        if (saveError) {
          console.warn('PROFILE SELECTION: Failed to save selection to storage:', saveError.message);
          // Don't revert UI state since the local state change is still valid
          // User will just lose persistence across app restarts
        }
      },
      'ProfileSelection.handleProfileSelect',
      { severity: ErrorSeverity.LOW }
    );

    if (selectError) {
      console.error('PROFILE SELECTION: Error selecting profile:', selectError);
      setError(selectError);
    } else {
      setError(null); // Clear any previous errors on successful selection
    }
  }, [selectedProfileId]);

  // Enhanced focus effect monitoring with error handling
  useFocusEffect(
    useCallback(() => {
      const checkForUpdatesAndSync = async () => {
        const { error: focusError } = await safeAsync(
          async () => {
            // Check if profiles were updated by other screens
            const { data: lastUpdatedTimestamp, error: timestampError } = await safeAsync(
              async () => {
                const lastUpdatedString = await AsyncStorage.getItem(PROFILES_LAST_UPDATED_KEY);
                return lastUpdatedString ? parseInt(lastUpdatedString, 10) : 0;
              },
              'ProfileSelection.checkForUpdatesAndSync.getTimestamp',
              { defaultValue: 0, severity: ErrorSeverity.LOW }
            );

            if (timestampError) {
              console.warn('PROFILE SELECTION: Failed to check update timestamp:', timestampError.message);
            }

            // If profiles were updated or selection needs sync
            if ((lastUpdatedTimestamp || 0) > lastFetchTimestamp) {
              console.log('PROFILE SELECTION: Profile updates detected, syncing selection');
              await syncSelectionFromStorage(profiles);
            } else {
              // Just sync selection in case it changed in another screen
              await syncSelectionFromStorage(profiles);
            }

            // Ensure we have a selection if profiles exist but no profile is selected
            if (!selectedProfileId && profiles.length > 0) {
              console.log('PROFILE SELECTION: No selection but profiles exist, initializing');
              await initializeSelection(profiles);
            }
          },
          'ProfileSelection.checkForUpdatesAndSync',
          { severity: ErrorSeverity.LOW }
        );

        if (focusError) {
          console.error('PROFILE SELECTION: Error in focus effect:', focusError);
          setError(focusError);
        }
      };

      if (profiles.length > 0) {
        checkForUpdatesAndSync();
      }
    }, [profiles, selectedProfileId, lastFetchTimestamp, syncSelectionFromStorage, initializeSelection])
  );

  // Initialize selection when profiles first load
  useEffect(() => {
    if (profiles.length > 0 && !selectedProfileId) {
      initializeSelection(profiles);
    }
  }, [profiles, selectedProfileId, initializeSelection]);

  // Clear error state
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    selectedProfileId,
    selectedProfile,
    error,
    handleProfileSelect,
    initializeSelection,
    syncSelectionFromStorage,
    clearError
  };
};

export default useProfileSelection; 