import React from 'react';
import { View } from 'react-native';
import { Control, FieldErrors, useWatch } from 'react-hook-form';
import { Feather } from '@expo/vector-icons';
import { z } from 'zod';
import { Timestamp } from 'firebase/firestore'; // Keep Timestamp for schema
import Accordion from '@/components/ui/Accordion';
import BasicInfoSection from './BasicInfoSection';
import InterestsDislikesSection from './InterestsDislikesSection';
import PreferencesSection from './PreferencesSection';
import SizesSection from './SizesSection';
import WishlistSection from './WishlistSection';
import PastGiftsSection from './PastGiftsSection';
import GeneralNotesSection from './GeneralNotesSection';
import CustomDatesSection from './CustomDatesSection';
import { WishlistItem, PastGiftGiven, GeneralNote } from '@/functions/src/types/firestore'; // Keep types for schema

// Moved profileSchema definition here as per instructions
export const profileSchema = z.object({
  name: z.string().min(1, { message: "Name is required" }),
  relationship: z.string().min(1, { message: "Relationship is required" }),
  birthday: z.date().nullable().optional(),
  anniversary: z.date().nullable().optional(),
  interestsInput: z.string().optional(),
  dislikesInput: z.string().optional(),
  // Removed redundant top-level preference fields
  clothingSize: z.string().optional(),
  shoeSize: z.string().optional(),
  wishlistItems: z.array(z.object({
    item: z.string().min(1),
    notes: z.string().optional(),
    link: z.string().url().optional().or(z.literal('')),
    dateAdded: z.date().nullable().optional() // Changed to Date | null
  })).optional().default([]),
  pastGiftsGiven: z.array(z.object({
    item: z.string().min(1),
    occasion: z.string().optional(),
    date: z.date().nullable().optional(), // Changed to Date | null
    reaction: z.string().optional()
  })).optional().default([]),
  generalNotes: z.array(z.object({
    note: z.string().min(1),
    date: z.date().nullable().optional() // Changed to Date | null
  })).optional().default([]),
  customDates: z.array(z.object({ id: z.string(), name: z.string().min(1), date: z.date().nullable() })).optional().default([]),
  preferences: z.object({
    favoriteColor: z.string().optional(),
    preferredStyle: z.string().optional(),
    favoriteBrands: z.array(z.string()).optional().default([]), // Changed to string[]
    budgetMin: z.preprocess(
      (val) => (val === "" || val === null || val === undefined ? undefined : parseFloat(String(val))),
      z.number({ invalid_type_error: 'Min budget must be a number.' }).positive({ message: 'Min budget must be positive.' }).optional()
    ),
    budgetMax: z.preprocess(
      (val) => (val === "" || val === null || val === undefined ? undefined : parseFloat(String(val))),
      z.number({ invalid_type_error: 'Max budget must be a number.' }).positive({ message: 'Max budget must be positive.' }).optional()
    ),
  }).optional()
}).refine(
  (data) => {
    if (data.preferences && typeof data.preferences.budgetMin === 'number' && typeof data.preferences.budgetMax === 'number') {
      return data.preferences.budgetMax >= data.preferences.budgetMin;
    }
    return true;
  },
  {
    message: "Max budget must be greater than or equal to min budget.",
    path: ["preferences", "budgetMax"],
  }
);

export type ProfileFormData = z.infer<typeof profileSchema>;



interface ProfileFormProps {
  control: Control<ProfileFormData>;
  errors: FieldErrors<ProfileFormData>;
  showDatePicker: (field: 'birthday' | 'anniversary') => void;
  birthdayValue: Date | null | undefined;
  anniversaryValue: Date | null | undefined;
  isNewProfile?: boolean; // Added isNewProfile prop
  profileId?: string; // Add profileId prop
}

const ProfileForm: React.FC<ProfileFormProps> = ({
  control,
  errors,
  showDatePicker,
  birthdayValue,
  anniversaryValue,
  isNewProfile, // Destructure the new prop
  profileId, // Destructure profileId
}) => {
  // Watch form values to determine completion status
  const watchedValues = useWatch({ control });

  // Helper function to determine completion status
  const getCompletionStatus = (fieldNames: string[], arrayFields?: string[]): 'empty' | 'partial' | 'complete' => {
    const fieldValues = fieldNames.map(field => {
      const keys = field.split('.');
      let value: any = watchedValues;
      for (const key of keys) {
        value = value?.[key];
      }
      return value;
    });

    const arrayValues = arrayFields?.map(field => {
      const keys = field.split('.');
      let value: any = watchedValues;
      for (const key of keys) {
        value = value?.[key];
      }
      return Array.isArray(value) ? value.length > 0 : false;
    }) || [];

    const hasBasicFields = fieldValues.some(val => val && val !== '' && val !== null && val !== undefined);
    const hasArrayFields = arrayValues.some(val => val === true);
    const allBasicFields = fieldValues.filter(val => val && val !== '' && val !== null && val !== undefined).length === fieldValues.length;
    const allArrayFields = arrayValues.length === 0 || arrayValues.every(val => val === true);

    if ((hasBasicFields || hasArrayFields) && (allBasicFields && allArrayFields)) {
      return 'complete';
    } else if (hasBasicFields || hasArrayFields) {
      return 'partial';
    }
    return 'empty';
  };

  // Helper function to generate summaries
  const generateSummary = (fields: any[] | undefined, type: string): string => {
    if (!fields || fields.length === 0) return '';
    if (fields.length === 1) return `1 ${type}`;
    if (fields.length > 3) return `${fields.length} ${type}s`;
    return `${fields.length} ${type}s`;
  };

  const basicInfoStatus = getCompletionStatus(['name', 'relationship']);
  const interestsStatus = getCompletionStatus(['interestsInput', 'dislikesInput']);
  const preferencesStatus = getCompletionStatus(['preferences.favoriteColor', 'preferences.preferredStyle'], ['preferences.favoriteBrands']);
  const sizesStatus = getCompletionStatus(['clothingSize', 'shoeSize']);
  const wishlistStatus = getCompletionStatus([], ['wishlistItems']);
  const pastGiftsStatus = getCompletionStatus([], ['pastGiftsGiven']);
  const notesStatus = getCompletionStatus([], ['generalNotes']);
  const customDatesStatus = getCompletionStatus([], ['customDates']);

  return (
    <View className="gap-4">
      {/* Basic Info - Always high priority and should be open */}
      <Accordion 
        title="Basic Information" 
        description="Essential details to get started with personalized gift ideas"
        initialOpen={true} 
        icon={<Feather color="#A3002B" name="user" size={24} />}
        completionStatus={basicInfoStatus}
        priority="high"
        summary={watchedValues?.name ? `${watchedValues.name}${watchedValues?.relationship ? ` (${watchedValues.relationship})` : ''}` : ''}
      >
        <BasicInfoSection
          control={control}
          errors={errors}
          showDatePicker={showDatePicker}
          birthdayValue={birthdayValue}
          anniversaryValue={anniversaryValue}
        />
      </Accordion>

      {/* Interests & Dislikes - High priority for gift recommendations */}
      <Accordion 
        title="Interests & Dislikes" 
        description="What they love and what to avoid - crucial for great gift ideas"
        initialOpen={!isNewProfile} 
        icon={<Feather color="#E5355F" name="heart" size={24} />}
        completionStatus={interestsStatus}
        priority="high"
        summary={watchedValues?.interestsInput || watchedValues?.dislikesInput ? 
          `${watchedValues.interestsInput?.split(',').filter(Boolean).length || 0} interests, ${watchedValues.dislikesInput?.split(',').filter(Boolean).length || 0} dislikes` : 
          ''}
      >
        <InterestsDislikesSection
          control={control}
          errors={errors}
        />
      </Accordion>

      {/* Preferences - Medium priority but very valuable */}
      <Accordion 
        title="Style & Preferences" 
        description="Personal style and brand preferences for targeted gift selection"
        initialOpen={!isNewProfile} 
        icon={<Feather color="#C70039" name="sliders" size={24} />}
        completionStatus={preferencesStatus}
        priority="medium"
        summary={watchedValues?.preferences?.favoriteColor || watchedValues?.preferences?.preferredStyle ? 
          `${watchedValues.preferences.favoriteColor || 'No color'}, ${watchedValues.preferences.preferredStyle || 'No style'}` : 
          ''}
      >
        <PreferencesSection
          control={control}
          errors={errors}
        />
      </Accordion>

      {/* Sizes - Helpful for clothing/accessory gifts */}
      <Accordion 
        title="Sizes" 
        description="Clothing and shoe sizes for perfectly fitting gifts"
        initialOpen={!isNewProfile} 
        icon={<Feather color="#7D003F" name="tag" size={24} />}
        completionStatus={sizesStatus}
        priority="medium"
        summary={watchedValues?.clothingSize || watchedValues?.shoeSize ? 
          `${watchedValues.clothingSize ? `Clothing: ${watchedValues.clothingSize}` : ''}${watchedValues.clothingSize && watchedValues.shoeSize ? ', ' : ''}${watchedValues.shoeSize ? `Shoe: ${watchedValues.shoeSize}` : ''}` : 
          ''}
      >
        <SizesSection
          control={control}
          errors={errors}
        />
      </Accordion>

      {/* Wishlist - Very valuable for gift ideas */}
      <Accordion 
        title="Wishlist" 
        description="Items they've specifically mentioned wanting"
        initialOpen={!isNewProfile} 
        icon={<Feather color="#E5355F" name="star" size={24} />}
        completionStatus={wishlistStatus}
        priority="high"
        summary={generateSummary(watchedValues?.wishlistItems, 'item')}
      >
        <WishlistSection
          control={control}
          errors={errors}
        />
      </Accordion>

      {/* Past Gifts - Helpful to avoid repeats */}
      <Accordion 
        title="Past Gifts" 
        description="Previous gifts and their reactions to avoid repeats and find patterns"
        initialOpen={!isNewProfile} 
        icon={<Feather color="#5C002E" name="archive" size={24} />}
        completionStatus={pastGiftsStatus}
        priority="medium"
        summary={generateSummary(watchedValues?.pastGiftsGiven, 'gift')}
      >
        <PastGiftsSection
          control={control}
          errors={errors}
        />
      </Accordion>

      {/* General Notes - Good for context */}
      <Accordion 
        title="General Notes" 
        description="Additional insights and observations about their preferences"
        initialOpen={!isNewProfile} 
        icon={<Feather color="#7A3E4F" name="file-text" size={24} />}
        completionStatus={notesStatus}
        priority="low"
        summary={generateSummary(watchedValues?.generalNotes, 'note')}
      >
        <GeneralNotesSection
          control={control}
          errors={errors}
        />
      </Accordion>

      {/* Custom Dates - Nice to have */}
      <Accordion 
        title="Special Dates" 
        description="Other important dates for gift-giving opportunities"
        initialOpen={!isNewProfile} 
        icon={<Feather color="#C22A4F" name="calendar" size={24} />}
        completionStatus={customDatesStatus}
        priority="low"
        summary={generateSummary(watchedValues?.customDates, 'date')}
      >
        <CustomDatesSection
          control={control}
          errors={errors}
          profileId={profileId || null}
        />
      </Accordion>
    </View>
  );
};

export default ProfileForm;