import { useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { 
  GiftRecommendation, 
  callGetGenericRecommendations,
  callGetProfileAwareRecommendations 
} from '../services/recommendationService';
import { SEARCH_MODE_PREFERENCE_KEY } from '../constants/storageKeys';

export type SearchMode = 'generic' | 'personalized';

interface UseSearchRecommendations {
  recommendations: GiftRecommendation[] | null;
  isGenerating: boolean;
  recommendationError: Error | null;
  searchMode: SearchMode;
  setSearchMode: (mode: SearchMode) => void;
  fetchRecommendations: (query: string, profileId?: string | null) => Promise<void>;
}

export const useSearchRecommendations = (): UseSearchRecommendations => {
  const [recommendations, setRecommendations] = useState<GiftRecommendation[] | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [recommendationError, setRecommendationError] = useState<Error | null>(null);
  const [searchMode, setSearchModeState] = useState<SearchMode>('personalized');

  // Load saved search mode preference on mount
  useEffect(() => {
    const loadSearchMode = async () => {
      try {
        const savedMode = await AsyncStorage.getItem(SEARCH_MODE_PREFERENCE_KEY);
        if (savedMode === 'generic' || savedMode === 'personalized') {
          setSearchModeState(savedMode);
        }
      } catch (error) {
        console.error('Error loading search mode preference:', error);
      }
    };
    loadSearchMode();
  }, []);

  // Save search mode preference when changed
  const setSearchMode = async (mode: SearchMode) => {
    try {
      setSearchModeState(mode);
      await AsyncStorage.setItem(SEARCH_MODE_PREFERENCE_KEY, mode);
    } catch (error) {
      console.error('Error saving search mode preference:', error);
      // Still update state even if storage fails
      setSearchModeState(mode);
    }
  };

  const fetchRecommendations = async (query: string, profileId?: string | null) => {
    setIsGenerating(true);
    setRecommendationError(null);
    setRecommendations(null); // Clear previous recommendations

    try {
      let fetchedRecommendations: GiftRecommendation[];

      if (searchMode === 'personalized' && profileId) {
        // Use profile-aware search when personalized mode is on and profile is available
        fetchedRecommendations = await callGetProfileAwareRecommendations(query, profileId);
      } else {
        // Use generic search as fallback or when in generic mode
        fetchedRecommendations = await callGetGenericRecommendations(query);
      }

      setRecommendations(fetchedRecommendations);
    } catch (error: any) {
      console.error('Error fetching search recommendations:', error);
      setRecommendationError(error);
    } finally {
      setIsGenerating(false);
    }
  };

  return {
    recommendations,
    isGenerating,
    recommendationError,
    searchMode,
    setSearchMode,
    fetchRecommendations,
  };
};