/**
 * Centralized Error Handling System
 * Provides consistent error management, retry mechanisms, and user-friendly messages
 * 
 * Part of CQA-002: Comprehensive Error Handling implementation
 */

// Error types for better categorization
export enum ErrorType {
  NETWORK = 'network',
  AUTHENTICATION = 'authentication', 
  PERMISSION = 'permission',
  VALIDATION = 'validation',
  NOT_FOUND = 'not_found',
  STORAGE = 'storage',
  UNKNOWN = 'unknown'
}

// Error severity levels
export enum ErrorSeverity {
  LOW = 'low',           // Minor issues, app continues normally
  MEDIUM = 'medium',     // Some functionality affected
  HIGH = 'high',         // Major functionality broken
  CRITICAL = 'critical'  // App may crash or be unusable
}

// Structured error interface
export interface AppError {
  type: ErrorType;
  severity: ErrorSeverity;
  message: string;
  userMessage: string;
  details?: any;
  retryable: boolean;
  timestamp: Date;
  context?: string;
}

// Retry configuration
export interface RetryConfig {
  maxAttempts: number;
  baseDelay: number;
  maxDelay: number;
  backoffMultiplier: number;
}

const DEFAULT_RETRY_CONFIG: RetryConfig = {
  maxAttempts: 3,
  baseDelay: 1000,      // 1 second
  maxDelay: 10000,      // 10 seconds
  backoffMultiplier: 2
};

/**
 * Creates a standardized AppError from various error sources
 */
export const createAppError = (
  error: unknown,
  context?: string,
  severity: ErrorSeverity = ErrorSeverity.MEDIUM
): AppError => {
  // Handle different error types
  if (error instanceof Error) {
    // Firebase/Firestore errors
    if (error.message.includes('permission-denied')) {
      return {
        type: ErrorType.PERMISSION,
        severity: ErrorSeverity.HIGH,
        message: error.message,
        userMessage: 'You don\'t have permission to perform this action.',
        retryable: false,
        timestamp: new Date(),
        context
      };
    }

    if (error.message.includes('not-found')) {
      return {
        type: ErrorType.NOT_FOUND,
        severity: ErrorSeverity.MEDIUM,
        message: error.message,
        userMessage: 'The requested item could not be found.',
        retryable: false,
        timestamp: new Date(),
        context
      };
    }

    if (error.message.includes('network') || error.message.includes('fetch')) {
      return {
        type: ErrorType.NETWORK,
        severity: ErrorSeverity.MEDIUM,
        message: error.message,
        userMessage: 'Network connection issue. Please check your internet and try again.',
        retryable: true,
        timestamp: new Date(),
        context
      };
    }

    if (error.message.includes('auth') || error.message.includes('unauthorized')) {
      return {
        type: ErrorType.AUTHENTICATION,
        severity: ErrorSeverity.HIGH,
        message: error.message,
        userMessage: 'Authentication required. Please log in and try again.',
        retryable: false,
        timestamp: new Date(),
        context
      };
    }

    // Generic Error object
    return {
      type: ErrorType.UNKNOWN,
      severity,
      message: error.message,
      userMessage: 'An unexpected error occurred. Please try again.',
      retryable: true,
      timestamp: new Date(),
      context,
      details: error
    };
  }

  // String errors
  if (typeof error === 'string') {
    return {
      type: ErrorType.UNKNOWN,
      severity,
      message: error,
      userMessage: error,
      retryable: true,
      timestamp: new Date(),
      context
    };
  }

  // Unknown error type
  return {
    type: ErrorType.UNKNOWN,
    severity: ErrorSeverity.CRITICAL,
    message: 'Unknown error occurred',
    userMessage: 'Something went wrong. Please restart the app and try again.',
    retryable: false,
    timestamp: new Date(),
    context,
    details: error
  };
};

/**
 * Retry mechanism with exponential backoff
 */
export const withRetry = async <T>(
  operation: () => Promise<T>,
  config: Partial<RetryConfig> = {},
  context?: string
): Promise<T> => {
  const finalConfig = { ...DEFAULT_RETRY_CONFIG, ...config };
  let lastError: unknown;

  for (let attempt = 1; attempt <= finalConfig.maxAttempts; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;
      
      const appError = createAppError(error, context);
      
      // Don't retry if error is not retryable
      if (!appError.retryable) {
        throw appError;
      }

      // Don't retry on last attempt
      if (attempt === finalConfig.maxAttempts) {
        throw appError;
      }

      // Calculate delay with exponential backoff
      const delay = Math.min(
        finalConfig.baseDelay * Math.pow(finalConfig.backoffMultiplier, attempt - 1),
        finalConfig.maxDelay
      );

      console.log(`Retry attempt ${attempt}/${finalConfig.maxAttempts} for ${context} after ${delay}ms`);
      
      // Wait before retry
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  // This should never be reached, but TypeScript requires it
  throw createAppError(lastError, context);
};

/**
 * Safe async operation wrapper
 * Provides consistent error handling and logging
 */
export const safeAsync = async <T>(
  operation: () => Promise<T>,
  context: string,
  options: {
    defaultValue?: T;
    logErrors?: boolean;
    severity?: ErrorSeverity;
  } = {}
): Promise<{ data: T | null; error: AppError | null }> => {
  const { defaultValue = null, logErrors = true, severity = ErrorSeverity.MEDIUM } = options;

  try {
    const data = await operation();
    return { data, error: null };
  } catch (error) {
    const appError = createAppError(error, context, severity);
    
    if (logErrors) {
      console.error(`[${context}] Error:`, {
        type: appError.type,
        severity: appError.severity,
        message: appError.message,
        retryable: appError.retryable,
        timestamp: appError.timestamp
      });
    }

    return { 
      data: defaultValue as T | null, 
      error: appError 
    };
  }
};

/**
 * Storage error helpers
 */
export const handleStorageError = (error: unknown, operation: string): AppError => {
  return createAppError(
    new Error(`Storage ${operation} failed: ${error}`),
    `AsyncStorage.${operation}`,
    ErrorSeverity.LOW
  );
};

/**
 * Network error helpers
 */
export const handleNetworkError = (error: unknown, endpoint: string): AppError => {
  return createAppError(
    new Error(`Network request to ${endpoint} failed: ${error}`),
    `Network.${endpoint}`,
    ErrorSeverity.MEDIUM
  );
};

/**
 * User-friendly error messages for common scenarios
 */
export const getErrorMessage = (error: AppError): string => {
  switch (error.type) {
    case ErrorType.NETWORK:
      return 'Connection issue. Please check your internet and try again.';
    case ErrorType.AUTHENTICATION:
      return 'Please log in to continue.';
    case ErrorType.PERMISSION:
      return 'You don\'t have permission for this action.';
    case ErrorType.NOT_FOUND:
      return 'The requested item could not be found.';
    case ErrorType.STORAGE:
      return 'Local storage issue. Please restart the app.';
    case ErrorType.VALIDATION:
      return error.userMessage || 'Please check your input and try again.';
    default:
      return error.userMessage || 'Something went wrong. Please try again.';
  }
}; 