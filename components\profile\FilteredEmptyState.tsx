import React from 'react';
import { View, Text, TouchableOpacity, Image } from 'react-native';
import { Feather, MaterialCommunityIcons } from '@expo/vector-icons';
import Animated, { FadeIn, SlideInUp } from 'react-native-reanimated';
import { useColorScheme } from 'nativewind';
import Card from '../ui/Card';
import Button from '../ui/Button';
import { TIME_PERIODS } from '../../utils/dateUtils';

export type FilterType = 'recent' | 'older' | 'upcoming' | 'with-reaction' | 'no-reaction' | 'basic' | 'custom';
export type ItemType = 'gifts' | 'notes' | 'dates' | 'feedback';

interface FilteredEmptyStateProps {
  filterType: FilterType;
  itemType: ItemType;
  totalCount: number;
  onAddAction: () => void;
  onClearFilter: () => void;
  searchQuery?: string;
}

const FilteredEmptyState: React.FC<FilteredEmptyStateProps> = ({
  filterType,
  itemType,
  totalCount,
  onAddAction,
  onClearFilter,
  searchQuery,
}) => {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  // Get filter-specific messaging with better logic
  const getFilterMessage = () => {
    const itemLabel = itemType === 'gifts' ? 'gifts' : 
                     itemType === 'notes' ? 'notes' : 
                     itemType === 'dates' ? 'dates' : 'feedback';
    
    switch (filterType) {
      case 'recent':
        return {
          title: `No ${itemLabel} in the past ${TIME_PERIODS.RECENT_DAYS} days`,
          subtitle: totalCount > 0 
            ? `You have ${totalCount} total ${itemLabel}, but none from the last ${TIME_PERIODS.RECENT_DAYS} days. Add something recent!`
            : `Start building your ${itemLabel} history!`,
          icon: 'calendar-clock' as keyof typeof MaterialCommunityIcons.glyphMap,
          actionText: `Add Recent ${itemType === 'gifts' ? 'Gift' : 
                                  itemType === 'notes' ? 'Note' : 
                                  itemType === 'dates' ? 'Date' : 'Feedback'}`,
          motivationalText: totalCount > 0 
            ? "Adding recent entries helps track what's working!" 
            : "Start tracking your gift-giving journey!",
        };
      
      case 'older':
        return {
          title: `No ${itemLabel} older than ${TIME_PERIODS.RECENT_DAYS} days`,
          subtitle: totalCount > 0 
            ? `You have ${totalCount} total ${itemLabel}. Looks like you've been active recently!`
            : `Start building your ${itemLabel} archive!`,
          icon: 'archive' as keyof typeof MaterialCommunityIcons.glyphMap,
          actionText: `Add ${itemType === 'gifts' ? 'Gift' : 
                            itemType === 'notes' ? 'Note' : 
                            itemType === 'dates' ? 'Date' : 'Feedback'}`,
          motivationalText: totalCount > 0 
            ? "Your recent activity is great! Building history helps spot patterns."
            : "Building a longer history helps spot patterns!",
        };
      
      case 'upcoming':
        return {
          title: `No upcoming ${itemLabel} in the next ${TIME_PERIODS.UPCOMING_DAYS} days`,
          subtitle: totalCount > 0 
            ? `You have ${totalCount} total ${itemLabel}. Plan ahead for success!`
            : `Start planning important dates!`,
          icon: 'calendar-plus' as keyof typeof MaterialCommunityIcons.glyphMap,
          actionText: 'Add Important Date',
          motivationalText: "Planning ahead makes gift-giving effortless!",
        };
      
      case 'with-reaction':
        return {
          title: `No ${itemLabel} with reactions recorded`,
          subtitle: totalCount > 0 
            ? `You have ${totalCount} total ${itemLabel}. Track reactions for insights!`
            : `Start recording gift reactions!`,
          icon: 'heart-plus' as keyof typeof MaterialCommunityIcons.glyphMap,
          actionText: 'Add Gift with Reaction',
          motivationalText: "Reactions help you understand what works best!",
        };
      
      case 'no-reaction':
        return {
          title: `No ${itemLabel} without reactions`,
          subtitle: totalCount > 0 
            ? `Great! You're tracking reactions for most ${itemLabel}.`
            : `Start adding ${itemLabel} to build your database!`,
          icon: 'clipboard-list' as keyof typeof MaterialCommunityIcons.glyphMap,
          actionText: `Add ${itemType === 'gifts' ? 'Gift' : 'Item'}`,
          motivationalText: "Keep building your comprehensive gift database!",
        };
      
      case 'basic':
        return {
          title: `No basic ${itemLabel} found`,
          subtitle: totalCount > 0 
            ? `You have ${totalCount} total ${itemLabel}. Your custom entries are impressive!`
            : `Add some essential dates to get started!`,
          icon: 'calendar' as keyof typeof MaterialCommunityIcons.glyphMap,
          actionText: 'Add Basic Date',
          motivationalText: "Basic dates like birthdays are the foundation!",
        };
      
      case 'custom':
        return {
          title: `No custom ${itemLabel} created yet`,
          subtitle: totalCount > 0 
            ? `You have ${totalCount} total ${itemLabel}. Add personal touches!`
            : `Create meaningful custom dates!`,
          icon: 'calendar-star' as keyof typeof MaterialCommunityIcons.glyphMap,
          actionText: 'Add Custom Date',
          motivationalText: "Custom dates make gift-giving more thoughtful!",
        };
      
      default:
        return {
          title: `No ${itemLabel} found`,
          subtitle: `Start building your ${itemLabel} collection!`,
          icon: 'plus-circle' as keyof typeof MaterialCommunityIcons.glyphMap,
          actionText: `Add ${itemType === 'gifts' ? 'Gift' : 
                            itemType === 'notes' ? 'Note' : 
                            itemType === 'dates' ? 'Date' : 'Item'}`,
          motivationalText: "Every expert gift-giver started with one entry!",
        };
    }
  };

  const message = getFilterMessage();
  const showProgress = totalCount > 0;

  return (
    <Animated.View 
      entering={FadeIn.duration(600)} 
      className="p-6 mx-4 bg-gray-50 rounded-xl border-2 border-gray-200 border-dashed dark:bg-gray-800/50 dark:border-gray-700"
    >
      {/* Icon */}
      <View className="items-center mb-6">
        <View 
          className="p-4 mb-4 rounded-full"
          style={{ 
            backgroundColor: isDark ? '#C7003920' : '#A3002B20' 
          }}
        >
          <MaterialCommunityIcons 
            name={message.icon} 
            size={28} 
            color={isDark ? '#C70039' : '#A3002B'} 
          />
        </View>

        {/* Title */}
        <Text className="mb-2 text-lg font-semibold text-center text-text-primary dark:text-text-primary-dark">
          {message.title}
        </Text>

        {/* Subtitle */}
        <Text className="text-sm text-center text-text-secondary dark:text-text-secondary-dark">
          {message.subtitle}
        </Text>
      </View>

      {/* Progress Indicator */}
      {showProgress && (
        <View className="mb-6">
          <View className="flex-row justify-between items-center mb-2">
            <Text className="text-xs font-medium text-text-secondary dark:text-text-secondary-dark">
              Collection Progress
            </Text>
            <Text className="text-xs font-bold text-primary dark:text-primary-dark">
              {totalCount} {totalCount === 1 ? 'item' : 'items'}
            </Text>
          </View>
          <View className="w-full h-2 bg-gray-200 rounded-full dark:bg-gray-700">
            <View 
              className="h-2 rounded-full"
              style={{ 
                width: `${Math.min((totalCount / 20) * 100, 100)}%`,
                backgroundColor: isDark ? '#C70039' : '#A3002B'
              }}
            />
          </View>
          <Text className="mt-1 text-xs text-center text-text-secondary dark:text-text-secondary-dark">
            {totalCount < 20 ? `${20 - totalCount} more to unlock insights!` : 'Collection complete! 🎉'}
          </Text>
        </View>
      )}

      {/* Action Buttons */}
      <View className="gap-3">
        {/* Primary Action */}
        <TouchableOpacity
          onPress={onAddAction}
          className="flex-row justify-center items-center p-4 rounded-lg"
          style={{ backgroundColor: isDark ? '#C70039' : '#A3002B' }}
          activeOpacity={0.8}
        >
          <Feather name="plus" size={16} color="white" />
          <Text className="ml-2 font-medium text-white">
            {message.actionText}
          </Text>
        </TouchableOpacity>
        
        {/* Clear Filter Action */}
        <TouchableOpacity
          onPress={onClearFilter}
          className="flex-row justify-center items-center px-4 py-3 bg-gray-100 rounded-lg dark:bg-gray-700"
          activeOpacity={0.7}
        >
          <Feather 
            name="x" 
            size={14} 
            color={isDark ? '#9CA3AF' : '#6B7280'} 
          />
          <Text className="ml-2 text-sm font-medium text-text-secondary dark:text-text-secondary-dark">
            Show All {itemType === 'gifts' ? 'Gifts' : 
                     itemType === 'notes' ? 'Notes' : 
                     itemType === 'dates' ? 'Dates' : 'Items'}
          </Text>
        </TouchableOpacity>
      </View>

      {/* Motivational Text */}
      <View className="mt-4">
        <Text className="text-xs italic text-center text-text-secondary dark:text-text-secondary-dark">
          💡 {message.motivationalText}
        </Text>
      </View>

      {/* Search Query Notice */}
      {searchQuery && (
        <View className="mt-3">
          <Text className="text-xs text-center text-text-secondary dark:text-text-secondary-dark">
            Searching for: "{searchQuery}"
          </Text>
        </View>
      )}

      {/* Search Image */}
      <View className="items-center mt-6">
        <Image 
          source={require('../../assets/images/search.png')} 
          className="w-20 h-20 opacity-60"
          resizeMode="contain"
        />
      </View>
    </Animated.View>
  );
};

export default FilteredEmptyState; 