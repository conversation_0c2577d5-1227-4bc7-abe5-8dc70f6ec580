import { View, Text, TouchableOpacity } from 'react-native';
import { Feather } from '@expo/vector-icons';
import { GeneralNote } from '@/functions/src/types/firestore';
import { Timestamp } from 'firebase/firestore'; // Import Timestamp

export interface GeneralNoteFormInputNote {
  note: string; // Include properties from GeneralNote
  date: Timestamp | null | undefined; // Allow null or undefined for form state
}

interface GeneralNoteFormInputProps {
  note: GeneralNoteFormInputNote; // Use the new interface
  onRemove: () => void;
  index: number;
}

export const GeneralNoteFormInput = ({
  note,
  onRemove,
  index,
}: GeneralNoteFormInputProps) => {
  const formatDate = (timestamp: any) => {
    if (!timestamp) return '';
    const date = timestamp.toDate();
    return date.toLocaleDateString();
  };

  return (
    <View className="p-3 mb-2 border rounded-md border-border bg-background">
      <View className="flex-row justify-between">
        <View className="flex-1">
          <Text className="font-semibold text-foreground" numberOfLines={2}>
            {note.note}
          </Text>
          {note.date ? (
            <Text className="text-xs text-muted-foreground">
              {formatDate(note.date)}
            </Text>
          ) : null}
        </View>
        <TouchableOpacity
          onPress={onRemove}
          accessibilityLabel="Remove note"
        >
          <Feather name="trash-2" size={20} color="text-destructive" />
        </TouchableOpacity>
      </View>
    </View>
  );
};