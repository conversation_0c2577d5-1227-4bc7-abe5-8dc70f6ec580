import React, { useState } from 'react';
import { View } from 'react-native';
import { Control, Controller, FieldErrors } from 'react-hook-form';
import { Timestamp } from 'firebase/firestore';
import { WishlistItem } from '../../types/firestore'; // Use client-side WishlistItem
import { WishlistItemFormInput } from '@/components/profile/WishlistItemFormInput';
import Button from '@/components/ui/Button';
import AddWishlistItemModal from './AddWishlistItemModal';
import EmptyState from './EmptyState';
import { ProfileFormData } from './ProfileForm'; // Assuming ProfileFormData is exported from ProfileForm

interface WishlistSectionProps {
  control: Control<ProfileFormData>;
  errors: FieldErrors<ProfileFormData>;
}

// Helper function for removing wishlist items
const handleRemoveWishlistItem = (
  indexToRemove: number,
  // Update onChange and currentItems types to match react-hook-form data structure
  onChange: (value: Array<{ item: string; notes?: string | undefined; link?: string | undefined; dateAdded?: Date | null | undefined; }>) => void,
  currentItems: Array<{ item: string; notes?: string | undefined; link?: string | undefined; dateAdded?: Date | null | undefined; }>
) => {
  const updatedItems = currentItems.filter((_, index) => index !== indexToRemove);
  onChange(updatedItems);
};

const WishlistSection: React.FC<WishlistSectionProps> = ({
  control,
  errors,
}) => {
  const [isWishlistModalVisible, setIsWishlistModalVisible] = useState(false);

  return (
    <View className="gap-4">
      <Controller
        control={control}
        name="wishlistItems"
        render={({ field: { onChange, value } }) => {
          const currentItems = value || [];
          
          // Handler for adding a new wishlist item
          // newItemData comes from the modal
          const handleAddNewWishlistItem = (newItemData: Omit<WishlistItem, 'dateAdded'>) => {
            // Create the item with the new data and a new Timestamp
            const itemWithDate: WishlistItem = {
              ...newItemData,
              dateAdded: Timestamp.now(), // Add current timestamp
            };
            // Add the new item to the current items and update the form state
            onChange([...currentItems, itemWithDate]);
          };

          // Show empty state if no items
          if (currentItems.length === 0) {
            return (
              <>
                <EmptyState
                  icon="star"
                  title="No Wishlist Items Yet"
                  description="Add items they've mentioned wanting to get perfect gift ideas"
                  actionText="Add First Item"
                  onAction={() => setIsWishlistModalVisible(true)}
                  examples={["New headphones", "Favorite book series", "Kitchen gadget"]}
                  benefit="Having their wishlist means you'll never run out of gift ideas they actually want!"
                />
                
                <AddWishlistItemModal
                  isVisible={isWishlistModalVisible}
                  onClose={() => setIsWishlistModalVisible(false)}
                  onAddItem={(newItemData: Omit<WishlistItem, 'dateAdded'>) => {
                    handleAddNewWishlistItem(newItemData);
                    setIsWishlistModalVisible(false);
                  }}
                />
              </>
            );
          }

          return (
            <View className="gap-4">
              {currentItems.map((itemData, index) => {
                // Convert itemData from form type to WishlistItem type for the input component
                const item: WishlistItem = {
                  ...itemData,
                  // Convert Date | null | undefined from form to Timestamp | null for the prop
                  dateAdded: itemData.dateAdded instanceof Date ? Timestamp.fromDate(itemData.dateAdded) : null,
                };
                return (
                  <WishlistItemFormInput
                    key={index}
                    item={item}
                    index={index}
                    onRemove={() => handleRemoveWishlistItem(index, onChange, currentItems)}
                  />
                );
              })}
              
              <View className="mt-2">
                <Button
                  onPress={() => setIsWishlistModalVisible(true)}
                  title="Add Another Item"
                  accessibilityLabel="Add wishlist item button"
                />
              </View>

              <AddWishlistItemModal
                isVisible={isWishlistModalVisible}
                onClose={() => setIsWishlistModalVisible(false)}
                onAddItem={(newItemData: Omit<WishlistItem, 'dateAdded'>) => {
                  handleAddNewWishlistItem(newItemData);
                  setIsWishlistModalVisible(false);
                }}
              />
            </View>
          );
        }}
      />
    </View>
  );
};

export default WishlistSection;