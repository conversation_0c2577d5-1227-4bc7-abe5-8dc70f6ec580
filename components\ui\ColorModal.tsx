import React from 'react';
import { View, Text, TouchableOpacity, ScrollView, Modal, Dimensions } from 'react-native';
import { Feather } from '@expo/vector-icons';
import Animated, { 
  useAnimatedStyle, 
  useSharedValue, 
  withSpring,
  FadeIn,
  FadeOut
} from 'react-native-reanimated';
import { useColorScheme } from 'nativewind';

interface ColorOption {
  label: string;
  value: string;
  color: string;
}

interface ColorModalProps {
  isVisible: boolean;
  onClose: () => void;
  options: ColorOption[];
  selectedValue?: string;
  onValueChange: (value: string) => void;
  title?: string;
}

const AnimatedTouchableOpacity = Animated.createAnimatedComponent(TouchableOpacity);
const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

const ColorModal: React.FC<ColorModalProps> = ({
  isVisible,
  onClose,
  options,
  selectedValue,
  onValueChange,
  title = "Choose Favorite Color"
}) => {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  // Theme colors
  const themedColors = {
    primary: isDark ? '#D96D00' : '#A3002B',
    textPrimary: isDark ? '#F9FAFB' : '#1F2937',
    textSecondary: isDark ? '#9CA3AF' : '#6B7280',
    border: isDark ? '#374151' : '#E5E7EB',
    background: isDark ? '#111827' : '#F9FAFB',
    card: isDark ? '#1F2937' : '#FFFFFF',
    backdrop: isDark ? 'rgba(0, 0, 0, 0.7)' : 'rgba(0, 0, 0, 0.5)',
  };

  const handleColorSelect = (value: string) => {
    onValueChange(value);
    onClose();
  };

  const ColorSwatch = ({ option }: { option: ColorOption }) => {
    const isSelected = selectedValue === option.value;
    const scaleValue = useSharedValue(1);

    const animatedStyle = useAnimatedStyle(() => ({
      transform: [{ scale: scaleValue.value }],
    }));

    const handlePress = () => {
      scaleValue.value = withSpring(0.95, { duration: 100 });
      setTimeout(() => {
        scaleValue.value = withSpring(1, { duration: 100 });
      }, 100);
      handleColorSelect(option.value);
    };

    // Handle white color visibility
    const displayColor = option.color === '#f8fafc' && !isDark ? '#f1f5f9' : option.color;
    const borderColor = option.color === '#f8fafc' || option.label === 'White' 
      ? themedColors.border 
      : 'transparent';

    return (
      <AnimatedTouchableOpacity
        style={animatedStyle}
        onPress={handlePress}
        className="flex-1 items-center p-4"
        accessibilityRole="button"
        accessibilityLabel={`Select ${option.label} color`}
        accessibilityState={{ selected: isSelected }}
      >
        <View className="items-center">
          {/* Color Swatch */}
          <View
            className={`w-16 h-16 rounded-full mb-3 border-2 ${
              isSelected ? 'border-4' : 'border-2'
            }`}
            style={{
              backgroundColor: displayColor,
              borderColor: isSelected ? themedColors.primary : borderColor,
              shadowColor: isSelected ? themedColors.primary : '#000000',
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: isSelected ? 0.4 : 0.15,
              shadowRadius: isSelected ? 8 : 4,
              elevation: isSelected ? 8 : 4,
            }}
          >
            {/* Selected Check Mark */}
            {isSelected && (
              <View className="flex-1 justify-center items-center">
                <Feather
                  name="check"
                  size={28}
                  color={
                    option.label === 'White' || 
                    option.label === 'Cream' || 
                    option.label === 'Beige' ||
                    option.label === 'Baby Blue' ||
                    option.label === 'Dusty Pink' ||
                    option.label === 'Mint'
                      ? '#374151' 
                      : '#FFFFFF'
                  }
                />
              </View>
            )}
          </View>

          {/* Color Label */}
          <Text
            className={`text-base text-center font-medium ${
              isSelected ? 'font-bold' : ''}`}
            style={{
              color: isSelected ? themedColors.primary : themedColors.textSecondary,
              lineHeight: 20,
            }}
            numberOfLines={2}
          >
            {option.label}
          </Text>
        </View>
      </AnimatedTouchableOpacity>
    );
  };

  return (
    <Modal
      visible={isVisible}
      transparent={true}
      animationType="fade"
      statusBarTranslucent={true}
      onRequestClose={onClose}
    >
      <TouchableOpacity
        activeOpacity={1}
        onPress={onClose}
        className="flex-1 justify-center items-center"
        style={{ backgroundColor: themedColors.backdrop }}
      >
        <Animated.View
          entering={FadeIn.duration(200)}
          exiting={FadeOut.duration(150)}
          className="overflow-hidden mx-6 rounded-2xl"
          style={{
            backgroundColor: themedColors.card,
            maxWidth: screenWidth * 0.9,
            maxHeight: screenHeight * 0.8,
            minWidth: screenWidth * 0.8,
          }}
          onStartShouldSetResponder={() => true}
        >
          {/* Header */}
          <View 
            className="flex-row justify-between items-center px-6 py-4 border-b"
            style={{ borderBottomColor: themedColors.border }}
          >
            <Text
              className="text-lg font-bold"
              style={{ color: themedColors.textPrimary }}
            >
              {title}
            </Text>
            <TouchableOpacity
              onPress={onClose}
              className="justify-center items-center w-8 h-8 rounded-full"
              style={{ backgroundColor: themedColors.background }}
              accessibilityRole="button"
              accessibilityLabel="Close color selection"
            >
              <Feather name="x" size={18} color={themedColors.textSecondary} />
            </TouchableOpacity>
          </View>

          {/* Color Grid */}
          <ScrollView
            showsVerticalScrollIndicator={false}
            className="px-8 py-6"
            style={{ maxHeight: screenHeight * 0.6 }}
          >
            <View className="flex-row flex-wrap -mx-2">
              {options.map((option) => (
                <View key={option.value} className="w-24">
                  <ColorSwatch option={option} />
                </View>
              ))}
            </View>
          </ScrollView>
        </Animated.View>
      </TouchableOpacity>
    </Modal>
  );
};

export default ColorModal; 