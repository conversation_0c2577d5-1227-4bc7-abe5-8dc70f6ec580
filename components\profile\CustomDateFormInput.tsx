import React from 'react';
import { Timestamp } from 'firebase/firestore';
import { View, Text, TouchableOpacity } from 'react-native';
import { Feather } from '@expo/vector-icons'; // Assuming Feather icons from Expo
import { CustomDate } from '../../types/firestore'; // Use client-side types

// Helper function to safely format dates
const formatDate = (date: Date | Timestamp | null): string => {
  if (!date) {
    return "No Date";
  }

  try {
    let jsDate: Date;
    if (date instanceof Date) {
      jsDate = date;
    } else {
      // Assuming date is a Firestore Timestamp
      jsDate = date.toDate();
    }

    // Check if the date is valid
    if (isNaN(jsDate.getTime())) {
      return "Invalid Date";
    }

    return jsDate.toDateString();
  } catch (error) {
    console.warn('Error formatting date in CustomDateFormInput:', date, error);
    return "Invalid Date";
  }
};

interface CustomDateFormInputProps {
  customDate: CustomDate;
  onRemove: () => void;
  index: number;
}

const CustomDateFormInput: React.FC<CustomDateFormInputProps> = ({ customDate, onRemove, index }) => {
  return (
    <View className="p-3 mb-2 border rounded-md border-border bg-background">
      <View className="flex-row justify-between">
        <View className="flex-1">
          <Text className="font-semibold text-foreground">{customDate.name}</Text>
          <Text className="text-xs text-muted-foreground">{formatDate(customDate.date)}</Text>
        </View>
        <TouchableOpacity onPress={onRemove} accessibilityLabel="Remove custom date">
          <Feather name="trash-2" size={20} color="text-destructive" />
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default CustomDateFormInput;