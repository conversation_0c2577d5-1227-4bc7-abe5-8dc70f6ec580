import React, { useState, useEffect, useCallback, useMemo, memo } from 'react';
import {
  View,
  Text,
  SafeAreaView,
  FlatList,
  TouchableOpacity,
  Alert,
  RefreshControl,
  TextInput,
  Dimensions,
} from 'react-native';
import { useLocalSearchPara<PERSON>, useRouter, Stack } from 'expo-router';
import { Feather, MaterialCommunityIcons } from '@expo/vector-icons';
import Animated, { 
  FadeIn, 
  SlideInRight, 
  SlideInUp, 
  SlideOutLeft,
  FadeInDown,
  Layout,
  withSpring,
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  runOnJS
} from 'react-native-reanimated';
import { useColorScheme } from 'nativewind';
import { Swipeable } from 'react-native-gesture-handler';
import { format, differenceInDays } from 'date-fns';
import * as Haptics from 'expo-haptics';

import { useAuth } from '../../../../contexts/AuthContext';
import {
  getSignificantOtherById,
  updateSignificantOther
} from '../../../../services/profileService';
import { SignificantOtherProfile, GeneralNote } from '../../../../functions/src/types/firestore';
import { Timestamp } from 'firebase/firestore';
import LoadingIndicator from '../../../../components/ui/LoadingIndicator';
import Card from '../../../../components/ui/Card';
import Button from '../../../../components/ui/Button';
import EmptyState from '../../../../components/profile/EmptyState';
import { AddGeneralNoteModal } from '../../../../components/profile/AddGeneralNoteModal';
import FilteredEmptyState from '../../../../components/profile/FilteredEmptyState';
import { 
  TIME_PERIODS, 
  safeFilterByDate, 
  calculateDateStats, 
  timestampToDate 
} from '../../../../utils/dateUtils';

const { width } = Dimensions.get('window');

const AnimatedTouchableOpacity = Animated.createAnimatedComponent(TouchableOpacity);

interface NotesStats {
  totalNotes: number;
  recentActivity: number;
  averageLength: number;
  oldestNote: Date | null;
  newestNote: Date | null;
}

type SortOption = 'recent' | 'oldest' | 'longest' | 'shortest';
type FilterOption = 'all' | 'recent' | 'older';

// Enhanced filter button labels
const FILTER_LABELS = {
  'all': 'All',
  'recent': `Last ${TIME_PERIODS.RECENT_DAYS} Days`,
  'older': `Older than ${TIME_PERIODS.RECENT_DAYS} Days`,
} as const;

// Memoized Statistics Section Component
const StatisticsSection = memo(({ stats, isDark }: { stats: NotesStats; isDark: boolean }) => {
  const StatCard = memo(({ 
    title, 
    value, 
    subtitle, 
    icon, 
    color,
    delay = 0 
  }: {
    title: string;
    value: string | number;
    subtitle?: string;
    icon: keyof typeof MaterialCommunityIcons.glyphMap;
    color: string;
    delay?: number;
  }) => (
    <Animated.View
      entering={SlideInUp.delay(delay).duration(300)}
      className="flex-1 mx-1"
    >
      <Card className="items-center p-4">
        <View 
          className="justify-center items-center mb-2 w-12 h-12 rounded-full"
          style={{ backgroundColor: color + '20' }}
        >
          <MaterialCommunityIcons name={icon} size={24} color={color} />
        </View>
        <Text className="text-2xl font-bold text-text-primary dark:text-text-primary-dark">
          {value}
        </Text>
        <Text className="text-sm font-medium text-center text-text-secondary dark:text-text-secondary-dark">
          {title}
        </Text>
        {subtitle && (
          <Text className="mt-1 text-xs text-center text-text-secondary dark:text-text-secondary-dark">
            {subtitle}
          </Text>
        )}
      </Card>
    </Animated.View>
  ));

  return (
    <View className="mb-6">
      <View className="flex-row mb-4">
        <StatCard
          title="Total Notes"
          value={stats.totalNotes}
          icon="note-text"
          color={isDark ? '#C70039' : '#A3002B'}
          delay={0}
        />
        <StatCard
          title="Recent Activity"
          value={stats.recentActivity}
          subtitle="Last 30 days"
          icon="calendar-clock"
          color="#16A34A"
          delay={100}
        />
        <StatCard
          title="Avg Length"
          value={stats.averageLength}
          subtitle="Characters"
          icon="text-box"
          color="#F59E0B"
          delay={200}
        />
      </View>
      <Animated.View entering={FadeInDown.delay(300).duration(400)}>
        <Card className="p-4 bg-accent/5 dark:bg-accent-dark/5 border-accent/20 dark:border-accent-dark/20">
          <View className="flex-row items-center">
            <MaterialCommunityIcons
              name="lightbulb-on"
              size={20}
              color={isDark ? '#FF507B' : '#E5355F'}
            />
            <Text className="ml-2 text-sm text-text-secondary dark:text-text-secondary-dark">
              {stats.totalNotes > 0 && stats.oldestNote 
                ? `First note added ${differenceInDays(new Date(), stats.oldestNote)} days ago`
                : 'Start building your note collection'
              }
            </Text>
          </View>
        </Card>
      </Animated.View>
    </View>
  );
});

const NotesScreen = () => {
  const { profileId } = useLocalSearchParams();
  const router = useRouter();
  const { user } = useAuth();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const [profile, setProfile] = useState<SignificantOtherProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [deletingIds, setDeletingIds] = useState<Set<string>>(new Set());
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<SortOption>('recent');
  const [filterBy, setFilterBy] = useState<FilterOption>('all');
  const [showFilters, setShowFilters] = useState(false);
  const [expandedNotes, setExpandedNotes] = useState<Set<number>>(new Set());
  const [showAddNoteModal, setShowAddNoteModal] = useState(false);

  const id = Array.isArray(profileId) ? profileId[0] : profileId;

  // Header animation values
  const headerAddButtonScale = useSharedValue(1);

  const headerAddButtonAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: headerAddButtonScale.value }],
      opacity: withSpring(headerAddButtonScale.value < 1 ? 0.7 : 1),
    };
  });

  const handleAddButtonPressIn = () => {
    headerAddButtonScale.value = withSpring(0.9, { damping: 15, stiffness: 300 });
  };

  const handleAddButtonPressOut = () => {
    headerAddButtonScale.value = withSpring(1, { damping: 15, stiffness: 300 });
  };

  // Memoized filter handlers to prevent unnecessary re-renders
  const handleFilterChange = useCallback((filter: FilterOption) => {
    setFilterBy(filter);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  }, []);

  const handleSortChange = useCallback((sort: SortOption) => {
    setSortBy(sort);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  }, []);

  const toggleFilters = useCallback(() => {
    setShowFilters(!showFilters);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  }, [showFilters]);

  const handleSearchChange = useCallback((text: string) => {
    setSearchQuery(text);
  }, []);

  const clearSearch = useCallback(() => {
    setSearchQuery('');
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  }, []);

  const toggleNoteExpansion = useCallback((index: number) => {
    setExpandedNotes(prev => {
      const newSet = new Set(prev);
      if (newSet.has(index)) {
        newSet.delete(index);
      } else {
        newSet.add(index);
      }
      return newSet;
    });
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  }, []);

  const handleAddNote = useCallback(() => {
    setShowAddNoteModal(true);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  }, []);

  const handleCloseAddNoteModal = useCallback(() => {
    setShowAddNoteModal(false);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  }, []);

  const handleSaveNote = useCallback(async (noteText: string) => {
    if (!profile || !user?.uid) return;

    try {
      const newNote: GeneralNote = {
        note: noteText,
        date: Timestamp.now(),
      };

      const updatedNotes = [...(profile.generalNotes || []), newNote];
      await updateSignificantOther(user.uid, id, {
        generalNotes: updatedNotes,
      });

      setProfile(prev => prev ? { ...prev, generalNotes: updatedNotes } : null);
      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    } catch (err) {
      console.error('Error saving note:', err);
      Alert.alert('Error', 'Failed to save note. Please try again.');
      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
    }
  }, [profile, user?.uid, id]);

  const fetchData = useCallback(async () => {
    if (!user?.uid || !id) return;

    try {
      setLoading(true);
      setError(null);

      const profileData = await getSignificantOtherById(user.uid, id);
      setProfile(profileData);
    } catch (err) {
      setError('Failed to load notes. Please try again.');
      console.error('Error fetching notes:', err);
    } finally {
      setLoading(false);
    }
  }, [user?.uid, id]);

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await fetchData();
    setRefreshing(false);
  }, [fetchData]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const handleDeleteNote = async (noteIndex: number) => {
    if (!profile || !user?.uid) return;

    const note = profile.generalNotes?.[noteIndex];
    if (!note) return;

    // Haptic feedback
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

    Alert.alert(
      'Delete Note',
      'Are you sure you want to delete this note?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            const noteId = `note-${noteIndex}`;
            setDeletingIds(prev => new Set(prev).add(noteId));

            try {
              const updatedNotes = profile.generalNotes?.filter((_, index) => index !== noteIndex) || [];
              await updateSignificantOther(user.uid, id, {
                generalNotes: updatedNotes,
              });

              setProfile(prev => prev ? { ...prev, generalNotes: updatedNotes } : null);
              await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
            } catch (err) {
              Alert.alert('Error', 'Failed to delete note. Please try again.');
              await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
            } finally {
              setDeletingIds(prev => {
                const newSet = new Set(prev);
                newSet.delete(noteId);
                return newSet;
              });
            }
          },
        },
      ]
    );
  };

  // Statistics calculation using safe utilities
  const stats = useMemo((): NotesStats => {
    const notes = profile?.generalNotes || [];
    const totalNotes = notes.length;
    
    // Use safe date statistics calculator
    const dateStats = calculateDateStats(notes, TIME_PERIODS.ACTIVITY_DAYS);
    const recentActivity = dateStats.recentActivity;

    // Average length
    const averageLength = totalNotes > 0 
      ? Math.round(notes.reduce((acc, note) => acc + note.note.length, 0) / totalNotes)
      : 0;

    // Use safer date handling for oldest/newest
    const oldestNote = dateStats.oldestDate;
    const newestNote = dateStats.newestDate;

    return {
      totalNotes,
      recentActivity,
      averageLength,
      oldestNote,
      newestNote,
    };
  }, [profile?.generalNotes]);

  // Filtered and sorted notes with safer date handling
  const filteredAndSortedNotes = useMemo(() => {
    try {
      let notes = profile?.generalNotes || [];

      // Apply search filter
      if (searchQuery.trim()) {
        notes = notes.filter(note => 
          note.note.toLowerCase().includes(searchQuery.toLowerCase())
        );
      }

      // Apply date filter using safe utilities
      if (filterBy !== 'all') {
        if (filterBy === 'recent') {
          // Use safe filtering with 30-day period
          notes = safeFilterByDate(notes, TIME_PERIODS.RECENT_DAYS, 'after');
        } else if (filterBy === 'older') {
          // Use safe filtering with 30-day period
          notes = safeFilterByDate(notes, TIME_PERIODS.RECENT_DAYS, 'before');
        }
      }

      // Apply sorting with safer date handling
      notes = [...notes].sort((a, b) => {
        switch (sortBy) {
          case 'recent':
            const aTime = timestampToDate(a.date);
            const bTime = timestampToDate(b.date);
            return bTime.getTime() - aTime.getTime();
          case 'oldest':
            const aTimeOld = timestampToDate(a.date);
            const bTimeOld = timestampToDate(b.date);
            return aTimeOld.getTime() - bTimeOld.getTime();
          case 'longest':
            return b.note.length - a.note.length;
          case 'shortest':
            return a.note.length - b.note.length;
          default:
            return 0;
        }
      });

      return notes;
    } catch (error) {
      console.warn('Error filtering notes, returning unfiltered list:', error);
      // Graceful fallback to prevent empty state on error
      return profile?.generalNotes || [];
    }
  }, [profile?.generalNotes, searchQuery, filterBy, sortBy]);

  const FilterButton = memo(({ 
    label, 
    isActive, 
    onPress 
  }: { 
    label: string; 
    isActive: boolean; 
    onPress: () => void; 
  }) => (
    <TouchableOpacity
      onPress={onPress}
      className={`
        px-4 py-2 rounded-full mr-2 border
        ${isActive 
          ? 'bg-primary dark:bg-primary-dark border-primary dark:border-primary-dark' 
          : 'bg-card dark:bg-card-dark border-border dark:border-border-dark'
        }
      `}
      activeOpacity={0.7}
    >
      <Text className={`
        text-sm font-medium
        ${isActive 
          ? 'text-white' 
          : 'text-text-secondary dark:text-text-secondary-dark'
        }
      `}>
        {label}
      </Text>
    </TouchableOpacity>
  ));

  const renderRightActions = useCallback((noteIndex: number) => {
    const noteId = `note-${noteIndex}`;
    const isDeleting = deletingIds.has(noteId);
    
    return (
      <View className="flex-row justify-center items-center mr-2 w-20 rounded-lg bg-error dark:bg-error-dark">
        <TouchableOpacity
          className="flex-1 justify-center items-center"
          onPress={() => handleDeleteNote(noteIndex)}
          disabled={isDeleting}
        >
          {isDeleting ? (
            <LoadingIndicator size="small" color="white" />
          ) : (
            <Feather name="trash-2" size={20} color="white" />
          )}
        </TouchableOpacity>
      </View>
    );
  }, [deletingIds, handleDeleteNote]);

  const renderNoteItem = useCallback(({ item, index }: { item: GeneralNote; index: number }) => {
    const noteId = `note-${index}`;
    const isDeleting = deletingIds.has(noteId);
    const isExpanded = expandedNotes.has(index);
    const shouldShowToggle = item.note.length > 150;

    return (
      <Swipeable
        renderRightActions={() => renderRightActions(index)}
        rightThreshold={40}
      >
        <Animated.View
          entering={SlideInRight.delay(Math.min(index * 30, 300)).duration(250)}
          exiting={SlideOutLeft.duration(200)}
          layout={Layout.springify().damping(20)}
          className="mb-4"
        >
          <Card className="p-4">
            <View className="flex-row items-start justify-between">
              <View className="flex-1 mr-3">
                <Animated.View layout={Layout.springify().damping(20)}>
                  <Text 
                    className="text-base leading-relaxed text-text-primary dark:text-text-primary-dark"
                    numberOfLines={isExpanded ? undefined : 3}
                  >
                    {item.note}
                  </Text>
                  {shouldShowToggle && (
                    <TouchableOpacity
                      onPress={() => toggleNoteExpansion(index)}
                      className="py-1 mt-2"
                      activeOpacity={0.7}
                    >
                      <View className="flex-row items-center">
                        <Text className="text-sm font-medium text-primary dark:text-primary-dark">
                          {isExpanded ? 'Show less' : 'Show more'}
                        </Text>
                        <MaterialCommunityIcons
                          name={isExpanded ? 'chevron-up' : 'chevron-down'}
                          size={16}
                          color={isDark ? '#C70039' : '#A3002B'}
                          style={{ marginLeft: 4 }}
                        />
                      </View>
                    </TouchableOpacity>
                  )}
                </Animated.View>

                {item.date && (
                  <Text className="mt-2 text-xs text-text-secondary dark:text-text-secondary-dark">
                    {format(timestampToDate(item.date), 'MMM d, yyyy')}
                  </Text>
                )}
              </View>

              <TouchableOpacity
                onPress={() => handleDeleteNote(index)}
                disabled={isDeleting}
                className="p-2 rounded-full active:bg-error/10"
              >
                {isDeleting ? (
                  <LoadingIndicator size="small" />
                ) : (
                  <Feather name="more-horizontal" size={18} color="#9CA3AF" />
                )}
              </TouchableOpacity>
            </View>
          </Card>
        </Animated.View>
      </Swipeable>
    );
  }, [deletingIds, handleDeleteNote, renderRightActions, expandedNotes, toggleNoteExpansion, isDark]);

  if (loading) {
    return (
      <SafeAreaView className="flex-1 bg-background dark:bg-background-dark">
        <View className="flex-1 justify-center items-center">
          <LoadingIndicator size="large" color="#A3002B" />
          <Text className="mt-4 text-base text-text-secondary dark:text-text-secondary-dark">
            Loading notes...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView className="flex-1 bg-background dark:bg-background-dark">
        <View className="flex-1 justify-center items-center px-6">
          <Feather name="alert-circle" size={48} color="#DC2626" />
          <Text className="mt-4 text-lg font-semibold text-center text-error dark:text-error-dark">
            {error}
          </Text>
          <Button
            title="Try Again"
            onPress={fetchData}
            variant="primary"
            className="mt-4"
          />
        </View>
      </SafeAreaView>
    );
  }

  const notes = filteredAndSortedNotes;

  return (
    <SafeAreaView className="flex-1 bg-background dark:bg-background-dark">
      <Stack.Screen
        options={{
          title: `${profile?.name || 'Profile'} - Notes`,
          headerLargeTitle: true,
          headerLargeTitleStyle: { color: "#A3002B" },
          headerTitleStyle: { color: "#A3002B", fontWeight: '600' },
          headerStyle: { backgroundColor: isDark ? '#111827' : '#F9FAFB' },
          headerShadowVisible: false,
          headerRight: () => (
            <AnimatedTouchableOpacity
              style={headerAddButtonAnimatedStyle}
              onPressIn={handleAddButtonPressIn}
              onPressOut={handleAddButtonPressOut}
              onPress={handleAddNote}
              className="flex-row justify-center items-center p-2 mr-1 rounded-full"
              accessibilityLabel="Add new note"
              accessibilityRole="button"
            >
              <Feather name="plus-circle" size={28} color="#A3002B" />
            </AnimatedTouchableOpacity>
          ),
        }}
      />
      <FlatList
        data={notes}
        renderItem={renderNoteItem}
        keyExtractor={(_, index) => `note-${index}`}
        contentContainerStyle={{ padding: 16 }}
        showsVerticalScrollIndicator={false}
        removeClippedSubviews={true}
        maxToRenderPerBatch={10}
        windowSize={10}
        initialNumToRender={8}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={['#A3002B']}
            tintColor={isDark ? '#C70039' : '#A3002B'}
          />
        }
        ListHeaderComponent={
          <View>
            {/* Header */}
            <Animated.View entering={FadeIn.duration(600)} className="mb-6">
              <Text className="text-base text-text-secondary dark:text-text-secondary-dark">
                Personal insights and important details
              </Text>
            </Animated.View>

            {/* Statistics */}
            {(profile?.generalNotes && profile.generalNotes.length > 0) && (
              <StatisticsSection 
                stats={stats}
                isDark={isDark}
              />
            )}

            {/* Search and Filters */}
            {(profile?.generalNotes && profile.generalNotes.length > 0) && (
              <Animated.View entering={FadeInDown.delay(400).duration(400)} className="mb-6">
                {/* Search Bar */}
                <View className="flex-row items-center mb-4">
                  <View className="flex-row flex-1 items-center px-4 py-3 rounded-lg border bg-card dark:bg-card-dark border-border dark:border-border-dark">
                    <Feather name="search" size={20} color="#9CA3AF" />
                    <TextInput
                      placeholder="Search notes..."
                      placeholderTextColor="#9CA3AF"
                      value={searchQuery}
                      onChangeText={handleSearchChange}
                      className="flex-1 ml-3 text-text-primary dark:text-text-primary-dark"
                    />
                    {searchQuery.length > 0 && (
                      <TouchableOpacity onPress={clearSearch}>
                        <Feather name="x" size={20} color="#9CA3AF" />
                      </TouchableOpacity>
                    )}
                  </View>
                  <TouchableOpacity
                    onPress={toggleFilters}
                    className={`
                      ml-3 p-3 rounded-lg border
                      ${showFilters 
                        ? 'bg-primary dark:bg-primary-dark border-primary dark:border-primary-dark' 
                        : 'bg-card dark:bg-card-dark border-border dark:border-border-dark'
                      }
                    `}
                  >
                    <Feather 
                      name="filter" 
                      size={20} 
                      color={showFilters ? 'white' : '#9CA3AF'} 
                    />
                  </TouchableOpacity>
                </View>

                {/* Filter Options */}
                {showFilters && (
                  <Animated.View entering={FadeInDown.duration(200)} className="mb-4">
                    <Text className="mb-2 text-sm font-medium text-text-secondary dark:text-text-secondary-dark">
                      Filter by date:
                    </Text>
                    <View className="flex-row mb-3">
                      <FilterButton
                        label={FILTER_LABELS.all}
                        isActive={filterBy === 'all'}
                        onPress={() => handleFilterChange('all')}
                      />
                      <FilterButton
                        label={FILTER_LABELS.recent}
                        isActive={filterBy === 'recent'}
                        onPress={() => handleFilterChange('recent')}
                      />
                      <FilterButton
                        label={FILTER_LABELS.older}
                        isActive={filterBy === 'older'}
                        onPress={() => handleFilterChange('older')}
                      />
                    </View>
                    
                    <Text className="mb-2 text-sm font-medium text-text-secondary dark:text-text-secondary-dark">
                      Sort by:
                    </Text>
                    <View className="flex-row">
                      <FilterButton
                        label="Recent"
                        isActive={sortBy === 'recent'}
                        onPress={() => handleSortChange('recent')}
                      />
                      <FilterButton
                        label="Oldest"
                        isActive={sortBy === 'oldest'}
                        onPress={() => handleSortChange('oldest')}
                      />
                      <FilterButton
                        label="Longest"
                        isActive={sortBy === 'longest'}
                        onPress={() => handleSortChange('longest')}
                      />
                      <FilterButton
                        label="Shortest"
                        isActive={sortBy === 'shortest'}
                        onPress={() => handleSortChange('shortest')}
                      />
                    </View>
                  </Animated.View>
                )}
              </Animated.View>
            )}

            {/* Results Summary */}
            {profile?.generalNotes && profile.generalNotes.length > 0 && (
              <View className="mb-4">
                <Text className="text-sm text-text-secondary dark:text-text-secondary-dark">
                  {notes.length === profile.generalNotes.length 
                    ? `Showing all ${profile.generalNotes.length} notes`
                    : `Showing ${notes.length} of ${profile.generalNotes.length} notes`
                  }
                </Text>
              </View>
            )}
          </View>
        }
        ListEmptyComponent={
          // Show different empty states based on whether we have items but they're filtered out
          profile?.generalNotes && profile.generalNotes.length > 0 && notes.length === 0 ? (
            <FilteredEmptyState
              filterType={filterBy as any}
              itemType="notes"
              totalCount={profile.generalNotes.length}
              onAddAction={handleAddNote}
              onClearFilter={() => {
                setFilterBy('all');
                setSearchQuery('');
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
              }}
              searchQuery={searchQuery || undefined}
            />
          ) : (
            <Animated.View entering={FadeIn.duration(600)} className="mt-8">
              <EmptyState
                icon="file-text"
                title="No Notes Yet"
                description="Start capturing important details, preferences, and insights about your loved one to make gift-giving more thoughtful and personal."
                actionText="Add First Note"
                onAction={handleAddNote}
                examples={[
                  "Loves handmade items",
                  "Prefers experiences over things",
                  "Allergic to certain materials"
                ]}
                benefit="Personal notes help you remember specific preferences, creating more meaningful and considerate gift choices that show how much you care."
              />
            </Animated.View>
          )
        }
      />
      
      {/* Add Note Modal */}
      <AddGeneralNoteModal
        isVisible={showAddNoteModal}
        onClose={handleCloseAddNoteModal}
        onSave={handleSaveNote}
      />
    </SafeAreaView>
  );
};

export default NotesScreen;
