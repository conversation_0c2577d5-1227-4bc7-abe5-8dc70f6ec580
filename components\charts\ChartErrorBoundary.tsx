import React, { Component, ReactNode } from 'react';
import { View, Text } from 'react-native';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: any) => void;
}

interface State {
  hasError: boolean;
  error?: Error;
}

class ChartErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: any) {
    console.error('Chart Error Boundary caught an error:', error, errorInfo);
    this.props.onError?.(error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <View className="p-4 rounded-xl border bg-card dark:bg-card-dark border-border dark:border-border-dark">
          <View className="items-center py-8">
            <Text className="text-2xl mb-2">⚠️</Text>
            <Text className="text-lg font-semibold text-text-primary dark:text-text-primary-dark mb-1">
              Chart Error
            </Text>
            <Text className="text-sm text-text-secondary dark:text-text-secondary-dark text-center">
              Unable to display chart data. Please try refreshing.
            </Text>
            {__DEV__ && this.state.error && (
              <Text className="text-xs text-red-500 mt-2 text-center">
                {this.state.error.message}
              </Text>
            )}
          </View>
        </View>
      );
    }

    return this.props.children;
  }
}

export default ChartErrorBoundary; 