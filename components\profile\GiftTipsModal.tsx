import React from 'react';
import { Modal, View, Text, ScrollView, TouchableOpacity, Pressable } from 'react-native';
import { Feather } from '@expo/vector-icons';
import Button from '@/components/ui/Button';

interface GiftTipsModalProps {
  isVisible: boolean;
  onClose: () => void;
}

const giftGivingTips = [
  "Listen for hints throughout the year.",
  "Consider their hobbies and passions.",
  "Think about experiences, not just physical items.",
  "Personalize it! A thoughtful touch goes a long way.",
  "Presentation matters – wrap it nicely.",
  "Don't be afraid to ask (subtly) or check their wishlist.",
  "The best gifts come from the heart.",
];

const GiftTipsModal: React.FC<GiftTipsModalProps> = ({ isVisible, onClose }) => {
  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={isVisible}
      onRequestClose={onClose}
    >
      {/* This View acts as the full-screen overlay, similar to AddGeneralNoteModal */}
      <Pressable
        className="flex-1 items-center justify-center bg-black/50 px-5"
        onPress={onClose} // Close modal when tapping outside the content box
      >
        {/* This inner Pressable holds the modal content and prevents closing when tapped inside */}
        <Pressable
          className="w-full max-w-md p-6 rounded-2xl bg-card dark:bg-card-dark shadow-xl"
          onPress={() => {}} // Consume press event to prevent propagation to outer Pressable
        >
          <View className="flex-row items-center justify-between mb-4">
            <Text className="text-xl font-bold text-text-primary dark:text-text-primary-dark">Gift Giving Tips & Inspiration</Text>
            <TouchableOpacity onPress={onClose} className="p-1">
              <Feather name="x" size={24} className="text-text-secondary dark:text-text-secondary-dark" />
            </TouchableOpacity>
          </View>

          <ScrollView className="max-h-80">
            {giftGivingTips.map((tip, index) => (
              <View key={index} className="flex-row items-start mb-3">
                <Text className="mr-2 text-text-primary dark:text-text-primary-dark">{`\u2022`}</Text>
                <Text className="flex-1 text-text-secondary dark:text-text-secondary-dark">{tip}</Text>
              </View>
            ))}
          </ScrollView>

          <View className="mt-4">
            <Button title="Close" onPress={onClose} variant="secondary" />
          </View>
        </Pressable>
      </Pressable>
    </Modal>
  );
};

export default GiftTipsModal;
