import React from 'react';
import { View, ViewProps, ViewStyle } from 'react-native';

interface CardProps extends ViewProps {
  children: React.ReactNode;
  style?: ViewStyle; // Allow style overrides
}

const Card = React.forwardRef<View, CardProps>(({ children, style, ...props }, ref) => {
  // Updated with dark mode support - consistent padding, rounded corners, background, and a subtle border
  const cardClasses = 'bg-card dark:bg-card-dark p-4 rounded-lg border border-border dark:border-border-dark shadow-md';

  return (
    <View ref={ref} className={cardClasses} style={style} {...props}>
      {children}
    </View>
  );
});

export default Card;