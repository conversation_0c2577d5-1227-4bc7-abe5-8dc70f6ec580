export const colorOptions = [
  // Classic Colors
  { label: 'Red', value: 'Red', color: '#ef4444' },
  { label: 'Blue', value: 'Blue', color: '#3b82f6' },
  { label: 'Green', value: 'Green', color: '#10b981' },
  { label: 'Yellow', value: 'Yellow', color: '#f59e0b' },
  { label: 'Orange', value: 'Orange', color: '#f97316' },
  { label: 'Purple', value: 'Purple', color: '#8b5cf6' },
  { label: 'Pink', value: 'Pink', color: '#ec4899' },
  { label: 'Black', value: 'Black', color: '#000000' },
  { label: 'White', value: 'White', color: '#f8fafc' },
  
  // Blue Family
  { label: 'Navy', value: 'Navy', color: '#1e3a8a' },
  { label: 'Sky Blue', value: 'Sky Blue', color: '#0ea5e9' },
  { label: 'Baby Blue', value: 'Baby Blue', color: '#bae6fd' },
  { label: 'Royal Blue', value: 'Royal Blue', color: '#1d4ed8' },
  { label: 'Teal', value: 'Teal', color: '#14b8a6' },
  { label: 'Turquoise', value: 'Turquoise', color: '#06b6d4' },
  
  // Green Family
  { label: 'Forest Green', value: 'Forest Green', color: '#16a34a' },
  { label: 'Sage', value: 'Sage', color: '#84cc16' },
  { label: 'Mint', value: 'Mint', color: '#6ee7b7' },
  { label: 'Olive', value: 'Olive', color: '#65a30d' },
  { label: 'Emerald', value: 'Emerald', color: '#059669' },
  
  // Red/Pink Family
  { label: 'Crimson', value: 'Crimson', color: '#dc2626' },
  { label: 'Rose', value: 'Rose', color: '#f43f5e' },
  { label: 'Hot Pink', value: 'Hot Pink', color: '#ec4899' },
  { label: 'Dusty Pink', value: 'Dusty Pink', color: '#f9a8d4' },
  { label: 'Coral', value: 'Coral', color: '#fb7185' },
  { label: 'Maroon', value: 'Maroon', color: '#991b1b' },
  
  // Purple Family
  { label: 'Lavender', value: 'Lavender', color: '#c084fc' },
  { label: 'Violet', value: 'Violet', color: '#8b5cf6' },
  { label: 'Indigo', value: 'Indigo', color: '#4f46e5' },
  { label: 'Plum', value: 'Plum', color: '#a855f7' },
  
  // Earth Tones
  { label: 'Brown', value: 'Brown', color: '#92400e' },
  { label: 'Tan', value: 'Tan', color: '#d2b48c' },
  { label: 'Beige', value: 'Beige', color: '#f5f5dc' },
  { label: 'Cream', value: 'Cream', color: '#fef7ed' },
  
  // Metallics & Neutrals
  { label: 'Silver', value: 'Silver', color: '#94a3b8' },
  { label: 'Gold', value: 'Gold', color: '#fbbf24' },
  { label: 'Rose Gold', value: 'Rose Gold', color: '#f59e0b' },
  { label: 'Copper', value: 'Copper', color: '#ea580c' },
  { label: 'Gray', value: 'Gray', color: '#6b7280' },
  { label: 'Charcoal', value: 'Charcoal', color: '#374151' },
  
  // Bright & Vibrant
  { label: 'Lime', value: 'Lime', color: '#84cc16' },
  { label: 'Cyan', value: 'Cyan', color: '#06b6d4' },
  { label: 'Magenta', value: 'Magenta', color: '#d946ef' },
  { label: 'Neon Green', value: 'Neon Green', color: '#22c55e' },
  { label: 'Electric Blue', value: 'Electric Blue', color: '#3b82f6' },
];

export const styleOptions = [
  { 
    label: 'Casual', 
    value: 'Casual',
    description: 'Comfortable, relaxed fits with jeans, t-shirts, and sneakers',
    icon: 'coffee',
    category: 'Lifestyle'
  },
  { 
    label: 'Smart Casual', 
    value: 'Smart Casual',
    description: 'Polished but relaxed - chinos, button-downs, and loafers',
    icon: 'briefcase',
    category: 'Professional'
  },
  { 
    label: 'Business Casual', 
    value: 'Business Casual',
    description: 'Professional yet approachable workplace attire',
    icon: 'user-check',
    category: 'Professional'
  },
  { 
    label: 'Formal / Business', 
    value: 'Formal / Business',
    description: 'Structured suits, dress shirts, and formal footwear',
    icon: 'award',
    category: 'Professional'
  },
  { 
    label: 'Sporty / Athletic', 
    value: 'Sporty / Athletic',
    description: 'Active wear, performance fabrics, and athletic shoes',
    icon: 'activity',
    category: 'Lifestyle'
  },
  { 
    label: 'Bohemian / Artsy', 
    value: 'Bohemian / Artsy',
    description: 'Flowing fabrics, unique patterns, and creative expression',
    icon: 'feather',
    category: 'Creative'
  },
  { 
    label: 'Minimalist', 
    value: 'Minimalist',
    description: 'Clean lines, neutral colors, and quality basics',
    icon: 'minus',
    category: 'Modern'
  },
  { 
    label: 'Vintage / Retro', 
    value: 'Vintage / Retro',
    description: 'Classic pieces from past decades with timeless appeal',
    icon: 'clock',
    category: 'Creative'
  },
  { 
    label: 'Trendy / Fashionable', 
    value: 'Trendy / Fashionable',
    description: 'Latest fashion trends and statement pieces',
    icon: 'trending-up',
    category: 'Modern'
  },
  { 
    label: 'Comfortable / Loungewear', 
    value: 'Comfortable / Loungewear',
    description: 'Soft fabrics, loose fits, perfect for relaxing at home',
    icon: 'home',
    category: 'Lifestyle'
  },
  { 
    label: 'Other', 
    value: 'Other',
    description: 'Unique personal style that doesn\'t fit standard categories',
    icon: 'more-horizontal',
    category: 'Custom'
  },
];

export const clothingSizeOptions = [
  // US Standard Letter Sizes - Ordered XS to XL
  { 
    label: 'XS', 
    value: 'XS',
    description: 'Extra Small',
    category: 'Standard',
    isCommon: true,
    icon: 'minimize-2'
  },
  { 
    label: 'S', 
    value: 'S',
    description: 'Small',
    category: 'Standard',
    isCommon: true,
    icon: 'minus'
  },
  { 
    label: 'M', 
    value: 'M',
    description: 'Medium',
    category: 'Standard',
    isCommon: true,
    icon: 'circle'
  },
  { 
    label: 'L', 
    value: 'L',
    description: 'Large',
    category: 'Standard',
    isCommon: true,
    icon: 'plus'
  },
  { 
    label: 'XL', 
    value: 'XL',
    description: 'Extra Large',
    category: 'Standard',
    isCommon: true,
    icon: 'maximize-2'
  },
  
  // Extended Sizes - Ordered XXL to XXXL
  { 
    label: 'XXL', 
    value: 'XXL',
    description: '2X Large',
    category: 'Extended',
    isCommon: false,
    icon: 'maximize'
  },
  { 
    label: 'XXXL', 
    value: 'XXXL',
    description: '3X Large',
    category: 'Extended',
    isCommon: false,
    icon: 'maximize'
  },
  
  // US Numeric Sizes - Ordered 00 to 20
  { 
    label: '00', 
    value: '00',
    description: 'Size 00',
    category: 'Numeric',
    isCommon: false,
    icon: 'hash'
  },
  { 
    label: '0', 
    value: '0',
    description: 'Size 0',
    category: 'Numeric',
    isCommon: true,
    icon: 'hash'
  },
  { 
    label: '2', 
    value: '2',
    description: 'Size 2',
    category: 'Numeric',
    isCommon: true,
    icon: 'hash'
  },
  { 
    label: '4', 
    value: '4',
    description: 'Size 4',
    category: 'Numeric',
    isCommon: true,
    icon: 'hash'
  },
  { 
    label: '6', 
    value: '6',
    description: 'Size 6',
    category: 'Numeric',
    isCommon: true,
    icon: 'hash'
  },
  { 
    label: '8', 
    value: '8',
    description: 'Size 8',
    category: 'Numeric',
    isCommon: true,
    icon: 'hash'
  },
  { 
    label: '10', 
    value: '10',
    description: 'Size 10',
    category: 'Numeric',
    isCommon: true,
    icon: 'hash'
  },
  { 
    label: '12', 
    value: '12',
    description: 'Size 12',
    category: 'Numeric',
    isCommon: true,
    icon: 'hash'
  },
  { 
    label: '14', 
    value: '14',
    description: 'Size 14',
    category: 'Numeric',
    isCommon: true,
    icon: 'hash'
  },
  { 
    label: '16', 
    value: '16',
    description: 'Size 16',
    category: 'Numeric',
    isCommon: true,
    icon: 'hash'
  },
  { 
    label: '18', 
    value: '18',
    description: 'Size 18',
    category: 'Numeric',
    isCommon: false,
    icon: 'hash'
  },
  { 
    label: '20', 
    value: '20',
    description: 'Size 20',
    category: 'Numeric',
    isCommon: false,
    icon: 'hash'
  },
  
  // Custom/Other - Always last
  { 
    label: 'Other', 
    value: 'Other',
    description: 'Custom or unlisted size',
    category: 'Custom',
    isCommon: false,
    icon: 'more-horizontal'
  },
];

export const shoeSizeOptions = [
  // Women's US Sizes - Ordered 5 to 11
  { 
    label: '5', 
    value: '5',
    description: 'US Women\'s 5',
    category: 'Women\'s',
    isCommon: true,
    icon: 'circle'
  },
  { 
    label: '5.5', 
    value: '5.5',
    description: 'US Women\'s 5.5',
    category: 'Women\'s',
    isCommon: true,
    icon: 'circle'
  },
  { 
    label: '6', 
    value: '6',
    description: 'US Women\'s 6',
    category: 'Women\'s',
    isCommon: true,
    icon: 'circle'
  },
  { 
    label: '6.5', 
    value: '6.5',
    description: 'US Women\'s 6.5',
    category: 'Women\'s',
    isCommon: true,
    icon: 'circle'
  },
  { 
    label: '7', 
    value: '7',
    description: 'US Women\'s 7',
    category: 'Women\'s',
    isCommon: true,
    icon: 'circle'
  },
  { 
    label: '7.5', 
    value: '7.5',
    description: 'US Women\'s 7.5',
    category: 'Women\'s',
    isCommon: true,
    icon: 'circle'
  },
  { 
    label: '8', 
    value: '8',
    description: 'US Women\'s 8',
    category: 'Women\'s',
    isCommon: true,
    icon: 'circle'
  },
  { 
    label: '8.5', 
    value: '8.5',
    description: 'US Women\'s 8.5',
    category: 'Women\'s',
    isCommon: true,
    icon: 'circle'
  },
  { 
    label: '9', 
    value: '9',
    description: 'US Women\'s 9',
    category: 'Women\'s',
    isCommon: true,
    icon: 'circle'
  },
  { 
    label: '9.5', 
    value: '9.5',
    description: 'US Women\'s 9.5',
    category: 'Women\'s',
    isCommon: true,
    icon: 'circle'
  },
  { 
    label: '10', 
    value: '10',
    description: 'US Women\'s 10',
    category: 'Women\'s',
    isCommon: true,
    icon: 'circle'
  },
  { 
    label: '10.5', 
    value: '10.5',
    description: 'US Women\'s 10.5',
    category: 'Women\'s',
    isCommon: false,
    icon: 'circle'
  },
  { 
    label: '11', 
    value: '11',
    description: 'US Women\'s 11',
    category: 'Women\'s',
    isCommon: false,
    icon: 'circle'
  },
  
  // Men's US Sizes - Ordered 7 to 15
  { 
    label: '7 (M)', 
    value: '7 (M)',
    description: 'US Men\'s 7',
    category: 'Men\'s',
    isCommon: true,
    icon: 'square'
  },
  { 
    label: '7.5 (M)', 
    value: '7.5 (M)',
    description: 'US Men\'s 7.5',
    category: 'Men\'s',
    isCommon: true,
    icon: 'square'
  },
  { 
    label: '8 (M)', 
    value: '8 (M)',
    description: 'US Men\'s 8',
    category: 'Men\'s',
    isCommon: true,
    icon: 'square'
  },
  { 
    label: '8.5 (M)', 
    value: '8.5 (M)',
    description: 'US Men\'s 8.5',
    category: 'Men\'s',
    isCommon: true,
    icon: 'square'
  },
  { 
    label: '9 (M)', 
    value: '9 (M)',
    description: 'US Men\'s 9',
    category: 'Men\'s',
    isCommon: true,
    icon: 'square'
  },
  { 
    label: '9.5 (M)', 
    value: '9.5 (M)',
    description: 'US Men\'s 9.5',
    category: 'Men\'s',
    isCommon: true,
    icon: 'square'
  },
  { 
    label: '10 (M)', 
    value: '10 (M)',
    description: 'US Men\'s 10',
    category: 'Men\'s',
    isCommon: true,
    icon: 'square'
  },
  { 
    label: '10.5 (M)', 
    value: '10.5 (M)',
    description: 'US Men\'s 10.5',
    category: 'Men\'s',
    isCommon: true,
    icon: 'square'
  },
  { 
    label: '11 (M)', 
    value: '11 (M)',
    description: 'US Men\'s 11',
    category: 'Men\'s',
    isCommon: true,
    icon: 'square'
  },
  { 
    label: '11.5 (M)', 
    value: '11.5 (M)',
    description: 'US Men\'s 11.5',
    category: 'Men\'s',
    isCommon: true,
    icon: 'square'
  },
  { 
    label: '12 (M)', 
    value: '12 (M)',
    description: 'US Men\'s 12',
    category: 'Men\'s',
    isCommon: true,
    icon: 'square'
  },
  { 
    label: '13 (M)', 
    value: '13 (M)',
    description: 'US Men\'s 13',
    category: 'Men\'s',
    isCommon: false,
    icon: 'square'
  },
  { 
    label: '14 (M)', 
    value: '14 (M)',
    description: 'US Men\'s 14',
    category: 'Men\'s',
    isCommon: false,
    icon: 'square'
  },
  { 
    label: '15 (M)', 
    value: '15 (M)',
    description: 'US Men\'s 15',
    category: 'Men\'s',
    isCommon: false,
    icon: 'square'
  },
  
  // Custom/Other - Always last
  { 
    label: 'Other', 
    value: 'Other',
    description: 'Custom or unlisted size',
    category: 'Custom',
    isCommon: false,
    icon: 'more-horizontal'
  },
];