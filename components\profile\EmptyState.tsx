import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { Feather } from '@expo/vector-icons';
import Animated, { FadeIn } from 'react-native-reanimated';

interface EmptyStateProps {
  icon: keyof typeof Feather.glyphMap;
  title: string;
  description: string;
  actionText: string;
  onAction: () => void;
  examples?: string[];
  benefit?: string;
  className?: string;
}

const EmptyState: React.FC<EmptyStateProps> = ({
  icon,
  title,
  description,
  actionText,
  onAction,
  examples,
  benefit,
  className = ''
}) => {
  return (
    <Animated.View 
      entering={FadeIn.duration(600)}
      className={`bg-gray-50 dark:bg-gray-800/50 rounded-xl p-8 border-2 border-dashed border-gray-200 dark:border-gray-700 ${className}`}
    >
      {/* Icon and Title */}
      <View className="items-center mb-6">
        <View className="mb-4 p-4 bg-primary-50 dark:bg-primary-500/10 rounded-full">
          <Feather name={icon} size={32} color="#A3002B" />
        </View>
        <Text className="text-lg font-semibold text-text-primary dark:text-text-primary-dark text-center mb-2">
          {title}
        </Text>
        <Text className="text-sm text-text-secondary dark:text-text-secondary-dark text-center">
          {description}
        </Text>
      </View>

      {/* Examples */}
      {examples && examples.length > 0 && (
        <View className="mb-6">
          <Text className="text-xs font-medium text-primary-600 dark:text-primary-400 mb-3 text-center">
            Examples:
          </Text>
          <View className="flex-row flex-wrap justify-center gap-2">
            {examples.map((example, index) => (
              <View 
                key={index}
                className="px-3 py-2 bg-primary-50 dark:bg-primary-500/10 rounded-full border border-primary-100 dark:border-primary-800"
              >
                <Text className="text-xs text-primary-700 dark:text-primary-300">
                  {example}
                </Text>
              </View>
            ))}
          </View>
        </View>
      )}

      {/* Benefit */}
      {benefit && (
        <View className="mb-6 bg-accent-50 dark:bg-accent-900/20 rounded-lg p-4 border border-accent-100 dark:border-accent-800">
          <View className="flex-row items-center mb-2">
            <Feather name="gift" size={16} color="#E5355F" />
            <Text className="text-xs font-medium text-accent-700 dark:text-accent-300 ml-2">
              Gift Benefit:
            </Text>
          </View>
          <Text className="text-xs text-accent-600 dark:text-accent-400">
            {benefit}
          </Text>
        </View>
      )}

      {/* Action Button */}
      <TouchableOpacity
        onPress={onAction}
        className="bg-primary-500 rounded-lg p-4 flex-row items-center justify-center"
        style={{ backgroundColor: '#A3002B' }}
        activeOpacity={0.8}
      >
        <Feather name="plus" size={16} color="white" />
        <Text className="text-white font-medium ml-2">
          {actionText}
        </Text>
      </TouchableOpacity>
    </Animated.View>
  );
};

export default EmptyState; 