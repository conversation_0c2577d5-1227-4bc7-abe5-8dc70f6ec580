import React, { useState, useEffect } from 'react';
import { Timestamp } from 'firebase/firestore';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  ScrollView,
  TextInput,
} from 'react-native';
import { Feather } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import { useColorScheme } from 'nativewind';
import * as Haptics from 'expo-haptics';
import { v4 as uuidv4 } from 'uuid';

import Input from '@/components/ui/Input';
import Button from '@/components/ui/Button';
import { PastGiftGiven } from '@/types/firestore';

const formatDate = (date: Date | null | undefined | Timestamp): string => {
  if (!date) return 'Select Date';
  let dateObject: Date;

  if (date instanceof Timestamp) {
    dateObject = date.toDate();
  } else if (date instanceof Date) {
    dateObject = date;
  } else {
    return 'Select Date';
  }

  return dateObject.toLocaleDateString(undefined, {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
};

interface AddPastGiftModalProps {
  isVisible: boolean;
  onClose: () => void;
  onAddItem: (
    newItem: Omit<PastGiftGiven, 'date'> & { date: Date | null }
  ) => void;
  prefilledDate?: string; // YYYY-MM-DD format from chart
}

const AddPastGiftModal: React.FC<AddPastGiftModalProps> = ({
  isVisible,
  onClose,
  onAddItem,
  prefilledDate,
}) => {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  // Basic gift information
  const [item, setItem] = useState('');
  const [occasion, setOccasion] = useState('');
  const [date, setDate] = useState<Date | null>(null);
  const [reaction, setReaction] = useState('');

  // Enhanced success tracking fields
  const [rating, setRating] = useState<number>(0);
  const [loved, setLoved] = useState<boolean | null>(null);
  const [stillUsed, setStillUsed] = useState<boolean | null>(null);
  const [wouldGiveAgain, setWouldGiveAgain] = useState<boolean | null>(null);

  // Context and metadata
  const [estimatedCost, setEstimatedCost] = useState<string>('');
  const [timeToFind, setTimeToFind] = useState<string>('');
  const [source, setSource] = useState<
    'store' | 'online' | 'handmade' | 'experience' | 'other' | null
  >(null);
  const [isHandmade, setIsHandmade] = useState<boolean>(false);
  const [category, setCategory] = useState('');

  // UI state
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [error, setError] = useState('');
  const [showAdditionalDetails, setShowAdditionalDetails] = useState(false);

  // Quick occasion suggestions
  const commonOccasions = [
    'Birthday',
    'Anniversary',
    'Christmas',
    "Valentine's Day",
    "Mother's Day",
    "Father's Day",
    'Graduation',
    'Just Because',
  ];

  // Gift category suggestions based on item input
  const suggestCategory = (itemText: string): string => {
    const text = itemText.toLowerCase();
    if (text.includes('book') || text.includes('novel')) return 'Books';
    if (
      text.includes('jewelry') ||
      text.includes('necklace') ||
      text.includes('ring')
    )
      return 'Jewelry';
    if (
      text.includes('phone') ||
      text.includes('laptop') ||
      text.includes('tech')
    )
      return 'Technology';
    if (
      text.includes('shirt') ||
      text.includes('dress') ||
      text.includes('clothes')
    )
      return 'Clothing';
    if (text.includes('game') || text.includes('toy')) return 'Entertainment';
    if (text.includes('perfume') || text.includes('skincare')) return 'Beauty';
    if (
      text.includes('ticket') ||
      text.includes('concert') ||
      text.includes('movie')
    )
      return 'Experience';
    return 'Other';
  };

  // Initialize with prefilled date if provided
  useEffect(() => {
    if (prefilledDate && isVisible) {
      try {
        setDate(new Date(prefilledDate));
      } catch (error) {
        console.warn('Invalid prefilled date:', prefilledDate);
      }
    }
  }, [prefilledDate, isVisible]);

  // Auto-suggest category when item changes
  useEffect(() => {
    if (item.length > 3) {
      setCategory(suggestCategory(item));
    }
  }, [item]);

  const handleGiftDateConfirm = (event: any, selectedDate?: Date) => {
    setShowDatePicker(false);
    if (selectedDate) {
      setDate(selectedDate);
    }
  };

  const calculateSuccessScore = (): number => {
    let score = 50; // Base score

    if (rating > 0) score += (rating - 1) * 10;
    if (loved === true) score += 10;
    if (stillUsed === true) score += 10;
    if (wouldGiveAgain === true) score += 10;

    return Math.max(0, Math.min(100, score));
  };

  const handleSave = () => {
    if (!item.trim()) {
      setError('Please enter the gift item');
      return;
    }

    const successScore = calculateSuccessScore();

    if (successScore >= 80) {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    }

    const newGift = {
      id: uuidv4(),
      item: item.trim(),
      occasion: occasion.trim() || undefined,
      reaction: reaction.trim() || undefined,
      rating: rating > 0 ? rating : undefined,
      loved: loved !== null ? loved : undefined,
      stillUsed: stillUsed !== null ? stillUsed : undefined,
      wouldGiveAgain: wouldGiveAgain !== null ? wouldGiveAgain : undefined,
      estimatedCost: estimatedCost ? parseFloat(estimatedCost) : undefined,
      timeToFind: timeToFind ? parseFloat(timeToFind) : undefined,
      source: source || undefined,
      isHandmade,
      category: category || undefined,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now(),
      version: 2, // Enhanced version
    };

    onAddItem({
      ...newGift,
      date: date,
    });

    handleCancel();
  };

  const handleCancel = () => {
    // Reset all form fields
    setItem('');
    setOccasion('');
    setDate(null);
    setReaction('');
    setRating(0);
    setLoved(null);
    setStillUsed(null);
    setWouldGiveAgain(null);
    setEstimatedCost('');
    setTimeToFind('');
    setSource(null);
    setIsHandmade(false);
    setCategory('');
    setError('');
    onClose();
  };

  return (
    <Modal
      visible={isVisible}
      animationType="slide"
      transparent={true}
      onRequestClose={handleCancel}
    >
      <View
        style={{ backgroundColor: 'rgba(0, 0, 0, 0.5)' }}
        className="flex-1 justify-center items-center"
      >
        <View className="p-6 w-full max-w-lg max-h-[90%] rounded-lg border border-border bg-card dark:bg-card-dark">
          {/* Header */}
          <View className="flex-row justify-between items-center mb-4">
            <Text className="text-xl font-bold text-primary dark:text-text-primary-dark">
              🎁 Record Past Gift
            </Text>
            <TouchableOpacity onPress={handleCancel}>
              <Feather
                name="x"
                size={24}
                color={isDark ? '#9CA3AF' : '#6B7280'}
              />
            </TouchableOpacity>
          </View>

          <ScrollView className="" showsVerticalScrollIndicator={false}>
            {error ? (
              <View className="p-3 mb-4 rounded-lg border bg-destructive/10 dark:bg-destructive-dark/10 border-destructive/30 dark:border-destructive-dark/30">
                <Text className="text-sm text-destructive dark:text-destructive-dark">
                  {error}
                </Text>
              </View>
            ) : null}

            {/* === BASIC INFORMATION === */}
            <View className="my-6">
              <Text className="mb-4 text-xl font-semibold text-center text-primary dark:text-text-primary-dark">
                📝 Basic Information
              </Text>

              {/* Gift Item */}
              <View className="mb-4">
                <Text className="mb-2 text-lg font-medium text-primary dark:text-text-secondary-dark">
                  Gift Item{' '}
                  <Text className="text-text-tertiary dark:text-text-tertiary-dark">
                    *
                  </Text>
                </Text>
                <Input
                  placeholder="e.g., Bluetooth headphones, Handmade scarf..."
                  onChangeText={(text) => {
                    setItem(text);
                    setError('');
                  }}
                  value={item}
                  className="ml-4 h-10 text-base"
                />
              </View>

              {/* Date */}
              <View className="mb-4">
                <Text className="mb-2 text-lg font-medium text-primary dark:text-text-secondary-dark">
                  Date Given{' '}
                  <Text className="text-text-tertiary dark:text-text-tertiary-dark">
                    *
                  </Text>
                </Text>
                <TouchableOpacity
                  onPress={() => setShowDatePicker(true)}
                  className="flex-row justify-between items-center p-4 rounded-lg border border-border dark:border-border-dark bg-card dark:bg-card-dark"
                >
                  <Text
                    className={`text-base ${
                      !date
                        ? 'text-text-secondary dark:text-text-secondary-dark'
                        : 'text-text-primary dark:text-text-primary-dark'
                    }`}
                  >
                    {formatDate(date)}
                  </Text>
                  <Feather
                    name="calendar"
                    size={20}
                    color={isDark ? '#9CA3AF' : '#6B7280'}
                  />
                </TouchableOpacity>
              </View>

              {showDatePicker && (
                <DateTimePicker
                  value={date || new Date()}
                  mode="date"
                  display="default"
                  onChange={handleGiftDateConfirm}
                />
              )}

              {/* Occasion */}
              <View className="mb-4">
                <Text className="mb-2 text-lg font-medium text-primary dark:text-text-secondary-dark">
                  Occasion
                </Text>
                <ScrollView
                  horizontal
                  showsHorizontalScrollIndicator={false}
                  className="mb-2"
                >
                  {commonOccasions.map((occ) => (
                    <TouchableOpacity
                      key={occ}
                      onPress={() => {
                        setOccasion(occ);
                        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                      }}
                      className={`px-3 py-2 mr-2 rounded-full border ${
                        occasion === occ
                          ? 'bg-primary dark:bg-primary-dark border-primary dark:border-primary-dark'
                          : 'bg-card dark:bg-card-dark border-border dark:border-border-dark'
                      }`}
                    >
                      <Text
                        className={`text-sm ${
                          occasion === occ
                            ? 'text-white'
                            : 'text-text-secondary dark:text-text-secondary-dark'
                        }`}
                      >
                        {occ}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </ScrollView>
                <Input
                  placeholder="Custom occasion or leave blank"
                  onChangeText={setOccasion}
                  value={occasion}
                  className="ml-4 h-10 text-base"
                />
              </View>
            </View>

            {/* === EXPERIENCE RATING === */}
            <View className="flex justify-center pb-6 mb-2 border-b border-border dark:border-border-dark">
              <Text className="items-center self-center mb-4 text-xl font-semibold text-center text-primary dark:text-text-primary-dark">
                How did it go?
              </Text>

              {/* Rating */}
              <View className="mb-4">
                <View className="flex-row justify-center items-center space-x-2">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <TouchableOpacity
                      key={star}
                      onPress={() => {
                        setRating(star);
                        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                      }}
                      className="p-2"
                    >
                      <Text className="text-3xl">
                        {star <= rating ? '⭐' : '☆'}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>

              {/* Simplified Success Indicators */}
              <View className="flex-col gap-2 space-y-2">
                <TouchableOpacity
                  onPress={() => {
                    setLoved(loved === true ? null : true);
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                  }}
                  className={`p-3 rounded-lg border flex-row items-center ${
                    loved === true
                      ? 'bg-accent/10 dark:bg-accent-dark/10 border-accent dark:border-accent-dark'
                      : 'bg-card dark:bg-card-dark border-border dark:border-border-dark'
                  }`}
                >
                  <Text className="mr-2">❤️</Text>
                  <Text
                    className={`flex-1 font-medium text-lg ${
                      loved === true
                        ? 'text-primary dark:text-accent-dark'
                        : 'text-text-primary dark:text-text-primary-dark'
                    }`}
                  >
                    They absolutely loved it!
                  </Text>
                  {loved === true && (
                    <Feather
                      name="check"
                      size={18}
                      color={isDark ? '#8B5CF6' : '#A3002B'}
                    />
                  )}
                </TouchableOpacity>

                <TouchableOpacity
                  onPress={() => {
                    setStillUsed(stillUsed === true ? null : true);
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                  }}
                  className={`p-3 rounded-lg border flex-row items-center ${
                    stillUsed === true
                      ? 'bg-accent/10 dark:bg-accent-dark/10 border-accent dark:border-accent-dark'
                      : 'bg-card dark:bg-card-dark border-border dark:border-border-dark'
                  }`}
                >
                  <Text className="mr-2">💯</Text>
                  <Text
                    className={`flex-1 font-medium text-lg ${
                      stillUsed === true
                        ? 'text-primary dark:text-accent-dark'
                        : 'text-text-primary dark:text-text-primary-dark'
                    }`}
                  >
                    They still use/have it
                  </Text>
                  {stillUsed === true && (
                    <Feather
                      name="check"
                      size={18}
                      color={isDark ? '#8B5CF6' : '#A3002B'}
                    />
                  )}
                </TouchableOpacity>

                <TouchableOpacity
                  onPress={() => {
                    setWouldGiveAgain(wouldGiveAgain === true ? null : true);
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                  }}
                  className={`p-3 rounded-lg border flex-row items-center ${
                    wouldGiveAgain === true
                      ? 'bg-accent/10 dark:bg-accent-dark/10 border-accent dark:border-accent-dark'
                      : 'bg-card dark:bg-card-dark border-border dark:border-border-dark'
                  }`}
                >
                  <Text className="mr-2">🔄</Text>
                  <Text
                    className={`flex-1 font-medium text-lg ${
                      wouldGiveAgain === true
                        ? 'text-primary dark:text-accent-dark'
                        : 'text-text-primary dark:text-text-primary-dark'
                    }`}
                  >
                    I'd give this type of gift again
                  </Text>
                  {wouldGiveAgain === true && (
                    <Feather
                      name="check"
                      size={18}
                      color={isDark ? '#8B5CF6' : '#A3002B'}
                    />
                  )}
                </TouchableOpacity>
              </View>
            </View>

            {/* === ADDITIONAL DETAILS (Collapsible) === */}
            <View className="mb-4">
              <TouchableOpacity
                onPress={() => setShowAdditionalDetails(!showAdditionalDetails)}
                className="flex flex-row justify-between items-center p-4 rounded-lg border bg-card dark:bg-card-dark border-border dark:border-border-dark"
              >
                <Text className="flex text-lg font-semibold text-center text-primary dark:text-text-primary-dark">
                  📋 Additional Details
                </Text>
                <Feather
                  name={showAdditionalDetails ? 'chevron-up' : 'chevron-down'}
                  size={20}
                  color={isDark ? '#9CA3AF' : '#6B7280'}
                />
              </TouchableOpacity>

              {showAdditionalDetails && (
                <View className="mt-4 space-y-4">
                  {/* Their Reaction */}
                  <View>
                    <Text className="mb-2 text-lg font-medium text-primary dark:text-text-secondary-dark">
                      Their Reaction
                    </Text>
                    <Input
                      placeholder="e.g., 'I've been wearing it every day!' or 'It's perfect!'"
                      onChangeText={setReaction}
                      value={reaction}
                      multiline
                      numberOfLines={3}
                      className="ml-4 h-10 text-base"
                    />
                  </View>

                  {/* Source */}
                  <View>
                    <Text className="mb-3 text-lg font-medium text-primary dark:text-text-secondary-dark">
                      How did you get this gift?
                    </Text>
                    <View className="flex-row flex-wrap gap-2">
                      {[
                        { key: 'store', icon: '🏪', label: 'Store' },
                        { key: 'online', icon: '💻', label: 'Online' },
                        { key: 'handmade', icon: '🎨', label: 'Handmade' },
                        { key: 'experience', icon: '🎫', label: 'Experience' },
                        { key: 'other', icon: '📦', label: 'Other' },
                      ].map(({ key, icon, label }) => (
                        <TouchableOpacity
                          key={key}
                          onPress={() => {
                            setSource(key as any);
                            setIsHandmade(key === 'handmade');
                            Haptics.impactAsync(
                              Haptics.ImpactFeedbackStyle.Light
                            );
                          }}
                          className={`m-1 px-3 py-2 rounded-full border flex-row items-center ${
                            source === key
                              ? 'bg-primary dark:bg-primary-dark border-primary dark:border-primary-dark'
                              : 'bg-card dark:bg-card-dark border-border dark:border-border-dark'
                          }`}
                        >
                          <Text className="mr-1 text-sm">{icon}</Text>
                          <Text
                            className={`text-sm ${
                              source === key
                                ? 'text-white'
                                : 'text-text-secondary dark:text-text-secondary-dark'
                            }`}
                          >
                            {label}
                          </Text>
                        </TouchableOpacity>
                      ))}
                    </View>
                  </View>

                  {/* Cost and Time */}
                  <View className="flex flex-row justify-between">
                    <View className='w-1/2'>
                      <Text className="mb-2 text-lg font-medium text-primary dark:text-text-secondary-dark">
                        How much did it cost?
                      </Text>
                      <View className="flex-row items-center">
                        <Input
                          placeholder="$100"
                          onChangeText={setEstimatedCost}
                          value={estimatedCost}
                          keyboardType="decimal-pad"
                          className="items-center self-center w-full h-10 text-base"
                        />
                      </View>
                    </View>
                    <View className='w-1/2'>
                      <Text className="mb-2 text-lg font-medium text-primary dark:text-text-secondary-dark">
                        Time it took to find it
                      </Text>
                      <Input
                        placeholder="2 Hours"
                        onChangeText={setTimeToFind}
                        value={timeToFind}
                        keyboardType="decimal-pad"
                        className="items-center self-center w-full h-10 text-base"
                      />
                    </View>
                  </View>

                  {/* Category */}
                  <View>
                    <Text className="mb-2 text-lg font-medium text-primary dark:text-text-secondary-dark">
                      Category
                    </Text>
                    <Input
                      placeholder="e.g., Technology, Clothing, Books..."
                      onChangeText={setCategory}
                      value={category}
                      className="ml-4 h-10 text-base"
                    />
                  </View>
                </View>
              )}
            </View>
          </ScrollView>

          {/* Action buttons */}
          <View className="flex-row justify-end p-4 mb-12 border-t border-border dark:border-border-dark">
            <Button
              title="Cancel"
              onPress={handleCancel}
              className="mr-2 w-1/2"
            />
            <Button title="Save Gift" onPress={handleSave} className="w-1/2" />
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default AddPastGiftModal;
