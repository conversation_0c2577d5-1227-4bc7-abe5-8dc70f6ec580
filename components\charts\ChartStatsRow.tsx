import React from 'react';
import { View, Text } from 'react-native';
import { GiftContributionsData } from '../../utils/chartDataUtils';

interface ChartStatsRowProps {
  chartData: GiftContributionsData;
}

const ChartStatsRow: React.FC<ChartStatsRowProps> = ({ chartData }) => {
  const statsData = [
    {
      value: chartData.totalGifts,
      label: 'Total Gifts',
      accessibilityLabel: `${chartData.totalGifts} total gifts`,
    },
    {
      value: `🔥 ${chartData.achievements.currentStreak}`,
      label: 'Day Streak',
      accessibilityLabel: `${chartData.achievements.currentStreak} day streak`,
    },
    {
      value: `🏆 ${chartData.achievements.perfectWeeks}`,
      label: 'Perfect Weeks',
      accessibilityLabel: `${chartData.achievements.perfectWeeks} perfect weeks`,
    },
    {
      value: `${Math.round(chartData.avgSuccessScore)}%`,
      label: 'Success Rate',
      accessibilityLabel: `${Math.round(chartData.avgSuccessScore)}% success rate`,
    },
  ];

  return (
    <View 
      className="flex-row justify-between items-center p-3 mb-4 rounded-lg bg-accent/5 dark:bg-accent-dark/5"
      accessible={true}
      accessibilityRole="summary"
      accessibilityLabel={`Statistics: ${statsData.map(stat => stat.accessibilityLabel).join(', ')}`}
    >
      {statsData.map((stat, index) => (
        <View 
          key={index} 
          className="items-center" 
          accessible={true} 
          accessibilityLabel={stat.accessibilityLabel}
        >
          <Text className="text-lg font-bold text-primary dark:text-primary-dark">
            {stat.value}
          </Text>
          <Text className="text-xs text-text-secondary dark:text-text-secondary-dark">
            {stat.label}
          </Text>
        </View>
      ))}
    </View>
  );
};

export default React.memo(ChartStatsRow); 