import { useEffect } from 'react';
import useProfileData from './useProfileData';
import useProfileSelection from './useProfileSelection';
import useCalendarEvents, { CalendarEvent } from './useCalendarEvents';

/**
 * Refactored main calendar hook - orchestrates focused sub-hooks
 * Replaces the 491-line god object with clean, maintainable code
 * 
 * BREAKING DOWN THE GOD OBJECT:
 * - useProfileData: Profile fetching, caching, management (120 lines)
 * - useProfileSelection: Profile selection & persistence (140 lines)  
 * - useCalendarEvents: Date processing & event generation (240 lines)
 * - useCalendarData: Main orchestration (THIS FILE - 60 lines)
 * 
 * Total: ~560 lines across 4 focused files vs 491 lines in 1 god object
 * BUT: Each file has single responsibility, easier testing, better maintainability
 */
const useCalendarData = () => {
  // 1. Manage profile data (fetching, caching, loading states)
  const {
    profiles,
    isLoading: profilesLoading,
    error: profilesError,
    refreshProfiles,
    lastFetchTimestamp
  } = useProfileData();

  // 2. Manage profile selection (which profile to show events for)
  const {
    selectedProfileId,
    selectedProfile,
    error: selectionError,
    handleProfileSelect,
    clearError: clearSelectionError
  } = useProfileSelection(profiles, lastFetchTimestamp);

  // 3. Process calendar events and generate markings
  const {
    markedDates,
    processedEvents,
    closestDate,
    upcomingDates,
    error: eventsError,
    processEvents,
    clearEvents,
    clearError: clearEventsError
  } = useCalendarEvents();

  // Effect: Process events when selected profile changes
  useEffect(() => {
    if (selectedProfile) {
      console.log('CALENDAR DATA: Processing events for selected profile:', selectedProfile.name);
      processEvents(selectedProfile);
    } else if (profiles.length === 0 && !profilesLoading) {
      console.log('CALENDAR DATA: No profiles found, processing holidays only');
      processEvents(null); // Process only holidays
    } else {
      console.log('CALENDAR DATA: Clearing events (no selection or still loading)');
      clearEvents();
    }
  }, [selectedProfile, profiles.length, profilesLoading, processEvents, clearEvents]);

  // Combined loading state
  const isLoading = profilesLoading;

  // Combined error state (prioritize profile errors, then selection, then events)
  const error = profilesError || selectionError || eventsError;

  // Enhanced profile select that clears events first for immediate feedback
  const handleProfileSelectWithClear = async (profileId: string) => {
    if (profileId !== selectedProfileId) {
      clearEvents(); // Immediate UI feedback
      clearEventsError(); // Clear any calendar events errors
      clearSelectionError(); // Clear any profile selection errors
      await handleProfileSelect(profileId);
    }
  };

  return {
    // Profile data
    profiles,
    
    // Calendar data  
    processedEvents,
    calendarMarkings: markedDates,
    closestDate,
    upcomingDates,
    
    // State
    isLoading,
    error,
    
    // Profile selection
    selectedProfileId,
    handleProfileSelect: handleProfileSelectWithClear,
    
    // Manual refresh capability
    optimizedDataLoad: refreshProfiles
  };
};

export default useCalendarData;

// Re-export types for components that need them
export type { CalendarEvent } from './useCalendarEvents'; 