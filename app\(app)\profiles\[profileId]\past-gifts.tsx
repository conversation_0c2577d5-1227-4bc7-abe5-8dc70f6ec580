import React, { useState, useEffect, useCallback, useMemo, memo } from 'react';
import {
  View,
  Text,
  SafeAreaView,
  FlatList,
  TouchableOpacity,
  Alert,
  RefreshControl,
  TextInput,
  Dimensions,
} from 'react-native';
import { useLocalSearchParams, useRouter, Stack } from 'expo-router';
import { Feather, MaterialCommunityIcons } from '@expo/vector-icons';
import Animated, { 
  FadeIn, 
  SlideInRight, 
  SlideInUp, 
  SlideOutLeft,
  FadeInDown,
  Layout,
  withSpring,
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  runOnJS
} from 'react-native-reanimated';
import { useColorScheme } from 'nativewind';
import { Swipeable } from 'react-native-gesture-handler';
import { format, differenceInDays } from 'date-fns';
import * as Haptics from 'expo-haptics';

import { useAuth } from '../../../../contexts/AuthContext';
import {
  getSignificantOtherById,
  updateSignificantOther
} from '../../../../services/profileService';
import { SignificantOtherProfile, PastGiftGiven } from '../../../../functions/src/types/firestore';
import { Timestamp } from 'firebase/firestore';
import LoadingIndicator from '../../../../components/ui/LoadingIndicator';
import Card from '../../../../components/ui/Card';
import Button from '../../../../components/ui/Button';
import EmptyState from '../../../../components/profile/EmptyState';
import AddPastGiftModal from '../../../../components/profile/AddPastGiftModal';
import FilteredEmptyState from '../../../../components/profile/FilteredEmptyState';
import { 
  TIME_PERIODS, 
  safeFilterByDate, 
  calculateDateStats, 
  timestampToDate 
} from '../../../../utils/dateUtils';

const { width } = Dimensions.get('window');

const AnimatedTouchableOpacity = Animated.createAnimatedComponent(TouchableOpacity);

interface GiftsStats {
  // Basic stats
  totalGifts: number;
  recentActivity: number;
  occasionCount: number;
  oldestGift: Date | null;
  newestGift: Date | null;
  mostFrequentOccasion: string | null;
  
  // Success analytics
  averageRating: number;
  lovedPercentage: number;
  stillUsedPercentage: number;
  wouldRepeatPercentage: number;
  averageSuccessScore: number;
  highSuccessCount: number; // Gifts with 80%+ success score
  
  // Cost analytics
  totalSpent: number;
  averageCost: number;
  mostExpensiveGift: number;
  budgetFriendlyCount: number; // Gifts under $50
  
  // Source and category analytics
  mostCommonSource: string | null;
  mostSuccessfulCategory: string | null;
  handmadeCount: number;
  enhancedDataCount: number; // Count of gifts with enhanced data
}

type SortOption = 'recent' | 'oldest' | 'occasion' | 'item' | 'rating' | 'success-score' | 'cost-high' | 'cost-low';
type FilterOption = 'all' | 'recent' | 'older' | 'with-reaction' | 'no-reaction' | 'high-rating' | 'loved-gifts' | 'budget-friendly' | 'expensive' | 'handmade' | 'high-success' | 'enhanced-only';

// Enhanced filter button labels
const FILTER_LABELS = {
  'all': 'All',
  'recent': `Last ${TIME_PERIODS.RECENT_DAYS} Days`,
  'older': `Older than ${TIME_PERIODS.RECENT_DAYS} Days`,
  'with-reaction': 'With Reaction',
  'no-reaction': 'No Reaction',
  'high-rating': '4+ Stars',
  'loved-gifts': 'Loved Gifts',
  'budget-friendly': 'Under $50',
  'expensive': '$100+',
  'handmade': 'Handmade',
  'high-success': '80%+ Success',
  'enhanced-only': 'Enhanced Data',
} as const;

// Memoized Statistics Section Component
const StatisticsSection = memo(({ stats, isDark }: { stats: GiftsStats; isDark: boolean }) => {
  const StatCard = memo(({ 
    title, 
    value, 
    subtitle, 
    icon, 
    color,
    delay = 0 
  }: {
    title: string;
    value: string | number;
    subtitle?: string;
    icon: keyof typeof MaterialCommunityIcons.glyphMap;
    color: string;
    delay?: number;
  }) => (
    <Animated.View
      entering={SlideInUp.delay(delay).duration(300)}
      className="flex-1 mx-1"
    >
      <Card className="items-center">
        <View 
          className="justify-center items-center mb-2 w-12 h-12 rounded-full"
          style={{ backgroundColor: color + '20' }}
        >
          <MaterialCommunityIcons name={icon} size={24} color={color} />
        </View>
        <Text className="text-2xl font-bold text-text-primary dark:text-text-primary-dark">
          {value}
        </Text>
        <Text className="text-sm font-medium text-center text-text-secondary dark:text-text-secondary-dark">
          {title}
        </Text>
        {subtitle && (
          <Text className="mt-1 text-xs text-center text-text-secondary dark:text-text-secondary-dark">
            {subtitle}
          </Text>
        )}
      </Card>
    </Animated.View>
  ));

  const formatCostDisplay = (cost: number): string => {
    if (cost < 1000) return `$${Math.round(cost)}`;
    return `$${(cost / 1000).toFixed(1)}k`;
  };

  const hasEnhancedData = stats.enhancedDataCount > 0;

  return (
    <View className="mb-6">
      {/* Basic Stats Row */}
      <View className="flex-row mb-4">
        <StatCard
          title="Total Gifts"
          value={stats.totalGifts}
          icon="gift"
          color={isDark ? '#C70039' : '#A3002B'}
          delay={0}
        />
        <StatCard
          title="Recent Activity"
          value={stats.recentActivity}
          subtitle="Last 30 days"
          icon="calendar-clock"
          color="#16A34A"
          delay={100}
        />
        <StatCard
          title="Occasions"
          value={stats.occasionCount}
          subtitle="Different events"
          icon="calendar-star"
          color="#F59E0B"
          delay={200}
        />
      </View>

      {/* Enhanced Analytics Section */}
      {hasEnhancedData && (
        <>
          {/* Success Metrics Row */}
          <Animated.View entering={FadeInDown.delay(400).duration(400)} className="mb-4">
            <Text className="mb-3 text-lg font-semibold text-text-primary dark:text-text-primary-dark">
              📊 Success Analytics
            </Text>
            <View className="flex-row mb-4">
              <StatCard
                title="Average Rating"
                value={stats.averageRating > 0 ? `${stats.averageRating.toFixed(1)}⭐` : 'N/A'}
                subtitle={`From ${stats.enhancedDataCount} gifts`}
                icon="star"
                color="#F59E0B"
                delay={300}
              />
              <StatCard
                title="Success Score"
                value={stats.averageSuccessScore > 0 ? `${Math.round(stats.averageSuccessScore)}%` : 'N/A'}
                subtitle={`${stats.highSuccessCount} high-success`}
                icon="chart-line"
                color="#10B981"
                delay={400}
              />
              <StatCard
                title="Loved Gifts"
                value={stats.lovedPercentage > 0 ? `${Math.round(stats.lovedPercentage)}%` : 'N/A'}
                subtitle="Really loved them"
                icon="heart"
                color="#EF4444"
                delay={500}
              />
            </View>
          </Animated.View>

          {/* Cost Analytics Row */}
          {stats.totalSpent > 0 && (
            <Animated.View entering={FadeInDown.delay(600).duration(400)} className="mb-4">
              <Text className="mb-3 text-lg font-semibold text-text-primary dark:text-text-primary-dark">
                💰 Cost Analytics
              </Text>
              <View className="flex-row mb-4">
                <StatCard
                  title="Total Spent"
                  value={formatCostDisplay(stats.totalSpent)}
                  subtitle="On tracked gifts"
                  icon="currency-usd"
                  color="#8B5CF6"
                  delay={600}
                />
                <StatCard
                  title="Average Cost"
                  value={formatCostDisplay(stats.averageCost)}
                  subtitle="Per gift"
                  icon="calculator"
                  color="#3B82F6"
                  delay={700}
                />
                <StatCard
                  title="Budget Friendly"
                  value={stats.budgetFriendlyCount}
                  subtitle="Under $50"
                  icon="piggy-bank"
                  color="#10B981"
                  delay={800}
                />
              </View>
            </Animated.View>
          )}

          {/* Insights Cards */}
          <Animated.View entering={FadeInDown.delay(900).duration(400)} className="mb-4">
            <View className="flex-row gap-2">
              {/* Success Insights */}
              <View className="flex-1">
                <Card className="p-4 bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-800">
                  <View className="flex-row items-center mb-2">
                    <MaterialCommunityIcons name="trophy" size={18} color="#10B981" />
                    <Text className="ml-2 text-sm font-semibold text-green-800 dark:text-green-200">
                      Top Performers
                    </Text>
                  </View>
                  <Text className="text-xs text-green-700 dark:text-green-300">
                    {stats.stillUsedPercentage > 0 && `${Math.round(stats.stillUsedPercentage)}% still in use`}
                    {stats.wouldRepeatPercentage > 0 && (
                      <Text className="block mt-1">
                        {Math.round(stats.wouldRepeatPercentage)}% would repeat
                      </Text>
                    )}
                  </Text>
                </Card>
              </View>

              {/* Source Insights */}
              <View className="flex-1">
                <Card className="p-4 bg-blue-50 border-blue-200 dark:bg-blue-900/20 dark:border-blue-800">
                  <View className="flex-row items-center mb-2">
                    <MaterialCommunityIcons name="source-branch" size={18} color="#3B82F6" />
                    <Text className="ml-2 text-sm font-semibold text-blue-800 dark:text-blue-200">
                      Gift Sources
                    </Text>
                  </View>
                  <Text className="text-xs text-blue-700 dark:text-blue-300">
                    {stats.mostCommonSource && `Most: ${stats.mostCommonSource}`}
                    {stats.handmadeCount > 0 && (
                      <Text className="block mt-1">
                        {stats.handmadeCount} handmade gifts
                      </Text>
                    )}
                  </Text>
                </Card>
              </View>
            </View>
          </Animated.View>
        </>
      )}

      {/* Summary Card */}
      <Animated.View entering={FadeInDown.delay(hasEnhancedData ? 1000 : 300).duration(400)}>
        <Card className="p-4 bg-accent/5 dark:bg-accent-dark/5 border-accent/20 dark:border-accent-dark/20">
          <View className="flex-row items-center">
            <MaterialCommunityIcons
              name="chart-timeline-variant"
              size={20}
              color={isDark ? '#FF507B' : '#E5355F'}
            />
            <View className="flex-1 ml-2">
              <Text className="text-sm text-text-secondary dark:text-text-secondary-dark">
                {stats.totalGifts > 0 && stats.oldestGift 
                  ? `First gift recorded ${differenceInDays(new Date(), stats.oldestGift)} days ago`
                  : 'Start building your gift history'
                }
              </Text>
              {stats.mostFrequentOccasion && (
                <Text className="mt-1 text-xs text-text-tertiary dark:text-text-tertiary-dark">
                  Most frequent occasion: {stats.mostFrequentOccasion}
                  {stats.mostSuccessfulCategory && ` • Best category: ${stats.mostSuccessfulCategory}`}
                </Text>
              )}
              {hasEnhancedData && (
                <Text className="mt-1 text-xs text-accent dark:text-accent-dark">
                  {stats.enhancedDataCount} of {stats.totalGifts} gifts have detailed analytics
                </Text>
              )}
            </View>
          </View>
        </Card>
      </Animated.View>
    </View>
  );
});

const PastGiftsScreen = () => {
  const { profileId } = useLocalSearchParams();
  const router = useRouter();
  const { user } = useAuth();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const [profile, setProfile] = useState<SignificantOtherProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [deletingIds, setDeletingIds] = useState<Set<string>>(new Set());
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<SortOption>('recent');
  const [filterBy, setFilterBy] = useState<FilterOption>('all');
  const [showFilters, setShowFilters] = useState(false);
  const [expandedReactions, setExpandedReactions] = useState<Set<number>>(new Set());
  const [showAddGiftModal, setShowAddGiftModal] = useState(false);

  const id = Array.isArray(profileId) ? profileId[0] : profileId;

  // Header animation values
  const headerAddButtonScale = useSharedValue(1);

  const headerAddButtonAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: headerAddButtonScale.value }],
      opacity: withSpring(headerAddButtonScale.value < 1 ? 0.7 : 1),
    };
  });

  const handleAddButtonPressIn = () => {
    headerAddButtonScale.value = withSpring(0.9, { damping: 15, stiffness: 300 });
  };

  const handleAddButtonPressOut = () => {
    headerAddButtonScale.value = withSpring(1, { damping: 15, stiffness: 300 });
  };

  // Memoized filter handlers to prevent unnecessary re-renders
  const handleFilterChange = useCallback((filter: FilterOption) => {
    setFilterBy(filter);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  }, []);

  const handleSortChange = useCallback((sort: SortOption) => {
    setSortBy(sort);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  }, []);

  const toggleFilters = useCallback(() => {
    setShowFilters(!showFilters);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  }, [showFilters]);

  const handleSearchChange = useCallback((text: string) => {
    setSearchQuery(text);
  }, []);

  const clearSearch = useCallback(() => {
    setSearchQuery('');
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  }, []);

  const toggleReactionExpansion = useCallback((index: number) => {
    setExpandedReactions(prev => {
      const newSet = new Set(prev);
      if (newSet.has(index)) {
        newSet.delete(index);
      } else {
        newSet.add(index);
      }
      return newSet;
    });
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  }, []);

  const handleAddGift = useCallback(() => {
    setShowAddGiftModal(true);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  }, []);

  const handleCloseAddGiftModal = useCallback(() => {
    setShowAddGiftModal(false);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  }, []);

  const handleSaveGift = useCallback(async (giftData: Omit<PastGiftGiven, 'date'> & { date: Date | null }) => {
    if (!profile || !user?.uid) return;

    try {
      const newGift: PastGiftGiven = {
        item: giftData.item,
        occasion: giftData.occasion,
        date: giftData.date ? Timestamp.fromDate(giftData.date) : null,
        reaction: giftData.reaction,
      };

      const updatedGifts = [...(profile.pastGiftsGiven || []), newGift];
      await updateSignificantOther(user.uid, id, {
        pastGiftsGiven: updatedGifts,
      });

      setProfile(prev => prev ? { ...prev, pastGiftsGiven: updatedGifts } : null);
      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    } catch (err) {
      console.error('Error saving gift:', err);
      Alert.alert('Error', 'Failed to save gift. Please try again.');
      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
    }
  }, [profile, user?.uid, id]);

  const fetchData = useCallback(async () => {
    if (!user?.uid || !id) return;

    try {
      setLoading(true);
      setError(null);

      const profileData = await getSignificantOtherById(user.uid, id);
      setProfile(profileData);
    } catch (err) {
      setError('Failed to load past gifts. Please try again.');
      console.error('Error fetching past gifts:', err);
    } finally {
      setLoading(false);
    }
  }, [user?.uid, id]);

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await fetchData();
    setRefreshing(false);
  }, [fetchData]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // Helper function to calculate success score
  const calculateSuccessScore = useCallback((gift: PastGiftGiven): number => {
    let score = 50; // Base score
    if (gift.rating && gift.rating > 0) score += (gift.rating - 1) * 10;
    if (gift.loved === true) score += 10;
    if (gift.stillUsed === true) score += 10;
    if (gift.wouldGiveAgain === true) score += 10;
    return Math.max(0, Math.min(100, score));
  }, []);

  // Helper function to get success score color
  const getSuccessScoreColor = useCallback((score: number): string => {
    if (score >= 80) return '#10B981'; // Green
    if (score >= 60) return '#F59E0B'; // Yellow
    if (score >= 40) return '#F97316'; // Orange
    return '#EF4444'; // Red
  }, []);

  // Helper function to format cost
  const formatCost = useCallback((cost: number | undefined): string => {
    if (!cost) return '';
    return cost < 1000 ? `$${cost}` : `$${(cost / 1000).toFixed(1)}k`;
  }, []);

  // Helper function to get source icon and color
  const getSourceInfo = useCallback((source: string | undefined) => {
    const sourceMap = {
      'store': { icon: '🏪', label: 'Store', color: '#3B82F6' },
      'online': { icon: '💻', label: 'Online', color: '#8B5CF6' },
      'handmade': { icon: '🎨', label: 'Handmade', color: '#10B981' },
      'experience': { icon: '🎫', label: 'Experience', color: '#F59E0B' },
      'other': { icon: '📦', label: 'Other', color: '#6B7280' },
    };
    return sourceMap[source as keyof typeof sourceMap] || { icon: '🎁', label: 'Gift', color: '#6B7280' };
  }, []);

  const handleDeleteGift = async (giftIndex: number) => {
    if (!profile || !user?.uid) return;

    const gift = profile.pastGiftsGiven?.[giftIndex];
    if (!gift) return;

    // Haptic feedback
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

    Alert.alert(
      'Delete Gift',
      `Are you sure you want to delete "${gift.item}"?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            const giftId = `gift-${giftIndex}`;
            setDeletingIds(prev => new Set(prev).add(giftId));

            try {
              const updatedGifts = profile.pastGiftsGiven?.filter((_, index) => index !== giftIndex) || [];
              await updateSignificantOther(user.uid, id, {
                pastGiftsGiven: updatedGifts,
              });

              setProfile(prev => prev ? { ...prev, pastGiftsGiven: updatedGifts } : null);
              await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
            } catch (err) {
              Alert.alert('Error', 'Failed to delete gift. Please try again.');
              await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
            } finally {
              setDeletingIds(prev => {
                const newSet = new Set(prev);
                newSet.delete(giftId);
                return newSet;
              });
            }
          },
        },
      ]
    );
  };

  // Statistics calculation using safe utilities
  const stats = useMemo((): GiftsStats => {
    const gifts = profile?.pastGiftsGiven || [];
    const totalGifts = gifts.length;
    
    // Use safe date statistics calculator
    const dateStats = calculateDateStats(gifts, TIME_PERIODS.ACTIVITY_DAYS);
    const recentActivity = dateStats.recentActivity;

    // Occasion count
    const uniqueOccasions = new Set(gifts.filter(gift => gift.occasion).map(gift => gift.occasion));
    const occasionCount = uniqueOccasions.size;

    // Use safer date handling for oldest/newest
    const oldestGift = dateStats.oldestDate;
    const newestGift = dateStats.newestDate;

    // Most frequent occasion
    const occasionCounts = gifts.reduce((acc, gift) => {
      if (gift.occasion) {
        acc[gift.occasion] = (acc[gift.occasion] || 0) + 1;
      }
      return acc;
    }, {} as Record<string, number>);

    const mostFrequentOccasion = Object.entries(occasionCounts).length > 0 
      ? Object.entries(occasionCounts).reduce((a, b) => a[1] > b[1] ? a : b)[0]
      : null;

    // Enhanced analytics - only for gifts with enhanced data
    const enhancedGifts = gifts.filter(gift => gift.version === 2 || gift.rating || gift.loved !== undefined || gift.estimatedCost);
    const enhancedDataCount = enhancedGifts.length;

    // Success analytics
    const ratingsAvailable = enhancedGifts.filter(gift => gift.rating && gift.rating > 0);
    const averageRating = ratingsAvailable.length > 0 
      ? ratingsAvailable.reduce((sum, gift) => sum + (gift.rating || 0), 0) / ratingsAvailable.length
      : 0;

    const lovedCount = enhancedGifts.filter(gift => gift.loved === true).length;
    const lovedPercentage = enhancedGifts.length > 0 ? (lovedCount / enhancedGifts.length) * 100 : 0;

    const stillUsedCount = enhancedGifts.filter(gift => gift.stillUsed === true).length;
    const stillUsedPercentage = enhancedGifts.length > 0 ? (stillUsedCount / enhancedGifts.length) * 100 : 0;

    const wouldRepeatCount = enhancedGifts.filter(gift => gift.wouldGiveAgain === true).length;
    const wouldRepeatPercentage = enhancedGifts.length > 0 ? (wouldRepeatCount / enhancedGifts.length) * 100 : 0;

    // Calculate success scores for enhanced gifts
    const successScores = enhancedGifts.map(gift => calculateSuccessScore(gift));
    const averageSuccessScore = successScores.length > 0 
      ? successScores.reduce((sum, score) => sum + score, 0) / successScores.length 
      : 0;
    const highSuccessCount = successScores.filter(score => score >= 80).length;

    // Cost analytics
    const giftsWithCost = enhancedGifts.filter(gift => gift.estimatedCost && gift.estimatedCost > 0);
    const totalSpent = giftsWithCost.reduce((sum, gift) => sum + (gift.estimatedCost || 0), 0);
    const averageCost = giftsWithCost.length > 0 ? totalSpent / giftsWithCost.length : 0;
    const mostExpensiveGift = giftsWithCost.length > 0 
      ? Math.max(...giftsWithCost.map(gift => gift.estimatedCost || 0))
      : 0;
    const budgetFriendlyCount = giftsWithCost.filter(gift => (gift.estimatedCost || 0) < 50).length;

    // Source analytics
    const sourceCounts = enhancedGifts.reduce((acc, gift) => {
      if (gift.source) {
        acc[gift.source] = (acc[gift.source] || 0) + 1;
      }
      return acc;
    }, {} as Record<string, number>);

    const mostCommonSource = Object.entries(sourceCounts).length > 0 
      ? Object.entries(sourceCounts).reduce((a, b) => a[1] > b[1] ? a : b)[0]
      : null;

    // Category analytics - find most successful category
    const categorySuccessMap = enhancedGifts.reduce((acc, gift) => {
      if (gift.category) {
        const score = calculateSuccessScore(gift);
        if (!acc[gift.category]) {
          acc[gift.category] = { totalScore: 0, count: 0 };
        }
        acc[gift.category].totalScore += score;
        acc[gift.category].count += 1;
      }
      return acc;
    }, {} as Record<string, { totalScore: number; count: number }>);

    const mostSuccessfulCategory = Object.entries(categorySuccessMap).length > 0
      ? Object.entries(categorySuccessMap).reduce((a, b) => 
          (a[1].totalScore / a[1].count) > (b[1].totalScore / b[1].count) ? a : b
        )[0]
      : null;

    const handmadeCount = enhancedGifts.filter(gift => gift.isHandmade === true || gift.source === 'handmade').length;

    return {
      // Basic stats
      totalGifts,
      recentActivity,
      occasionCount,
      oldestGift,
      newestGift,
      mostFrequentOccasion,
      
      // Success analytics
      averageRating,
      lovedPercentage,
      stillUsedPercentage,
      wouldRepeatPercentage,
      averageSuccessScore,
      highSuccessCount,
      
      // Cost analytics
      totalSpent,
      averageCost,
      mostExpensiveGift,
      budgetFriendlyCount,
      
      // Source and category analytics
      mostCommonSource,
      mostSuccessfulCategory,
      handmadeCount,
      enhancedDataCount,
    };
  }, [profile?.pastGiftsGiven, calculateSuccessScore]);

  // Filtered and sorted gifts with safer date handling
  const filteredAndSortedGifts = useMemo(() => {
    try {
      let gifts = profile?.pastGiftsGiven || [];

      // Apply search filter - now includes enhanced fields
      if (searchQuery.trim()) {
        const query = searchQuery.toLowerCase();
        gifts = gifts.filter(gift => 
          gift.item.toLowerCase().includes(query) ||
          gift.occasion?.toLowerCase().includes(query) ||
          gift.reaction?.toLowerCase().includes(query) ||
          gift.category?.toLowerCase().includes(query) ||
          gift.source?.toLowerCase().includes(query) ||
          (gift.estimatedCost && gift.estimatedCost.toString().includes(query))
        );
      }

      // Apply filters using safe utilities
      if (filterBy !== 'all') {
        if (filterBy === 'recent') {
          // Use safe filtering with 30-day period
          gifts = safeFilterByDate(gifts, TIME_PERIODS.RECENT_DAYS, 'after');
        } else if (filterBy === 'older') {
          // Use safe filtering with 30-day period
          gifts = safeFilterByDate(gifts, TIME_PERIODS.RECENT_DAYS, 'before');
        } else if (filterBy === 'with-reaction') {
          gifts = gifts.filter(gift => gift.reaction && gift.reaction.trim().length > 0);
        } else if (filterBy === 'no-reaction') {
          gifts = gifts.filter(gift => !gift.reaction || gift.reaction.trim().length === 0);
        } else if (filterBy === 'high-rating') {
          gifts = gifts.filter(gift => gift.rating && gift.rating >= 4);
        } else if (filterBy === 'loved-gifts') {
          gifts = gifts.filter(gift => gift.loved === true);
        } else if (filterBy === 'budget-friendly') {
          gifts = gifts.filter(gift => gift.estimatedCost && gift.estimatedCost < 50);
        } else if (filterBy === 'expensive') {
          gifts = gifts.filter(gift => gift.estimatedCost && gift.estimatedCost >= 100);
        } else if (filterBy === 'handmade') {
          gifts = gifts.filter(gift => gift.isHandmade === true || gift.source === 'handmade');
        } else if (filterBy === 'high-success') {
          gifts = gifts.filter(gift => {
            const successScore = calculateSuccessScore(gift);
            return successScore >= 80;
          });
        } else if (filterBy === 'enhanced-only') {
          gifts = gifts.filter(gift => gift.version === 2 || gift.rating || gift.loved !== undefined || gift.estimatedCost);
        }
      }

      // Apply sorting with safer date handling
      gifts = [...gifts].sort((a, b) => {
        switch (sortBy) {
          case 'recent':
            const aTime = timestampToDate(a.date);
            const bTime = timestampToDate(b.date);
            return bTime.getTime() - aTime.getTime();
          case 'oldest':
            const aTimeOld = timestampToDate(a.date);
            const bTimeOld = timestampToDate(b.date);
            return aTimeOld.getTime() - bTimeOld.getTime();
          case 'occasion':
            const aOccasion = a.occasion || '';
            const bOccasion = b.occasion || '';
            return aOccasion.localeCompare(bOccasion);
          case 'item':
            return a.item.localeCompare(b.item);
          case 'rating':
            const aRating = a.rating || 0;
            const bRating = b.rating || 0;
            return bRating - aRating; // Highest rating first
          case 'success-score':
            const aScore = calculateSuccessScore(a);
            const bScore = calculateSuccessScore(b);
            return bScore - aScore; // Highest success score first
          case 'cost-high':
            const aCostHigh = a.estimatedCost || 0;
            const bCostHigh = b.estimatedCost || 0;
            return bCostHigh - aCostHigh; // Highest cost first
          case 'cost-low':
            const aCostLow = a.estimatedCost || Number.MAX_VALUE;
            const bCostLow = b.estimatedCost || Number.MAX_VALUE;
            return aCostLow - bCostLow; // Lowest cost first
          default:
            return 0;
        }
      });

      return gifts;
    } catch (error) {
      console.warn('Error filtering gifts, returning unfiltered list:', error);
      // Graceful fallback to prevent empty state on error
      return profile?.pastGiftsGiven || [];
    }
  }, [profile?.pastGiftsGiven, searchQuery, filterBy, sortBy]);

  const FilterButton = memo(({ 
    label, 
    isActive, 
    onPress 
  }: { 
    label: string; 
    isActive: boolean; 
    onPress: () => void; 
  }) => (
    <TouchableOpacity
      onPress={onPress}
      className={`
        px-4 py-2 rounded-full mr-2 border
        ${isActive 
          ? 'bg-primary dark:bg-primary-dark border-primary dark:border-primary-dark' 
          : 'bg-card dark:bg-card-dark border-border dark:border-border-dark'
        }
      `}
      activeOpacity={0.7}
    >
      <Text className={`
        text-sm font-medium
        ${isActive 
          ? 'text-white' 
          : 'text-text-secondary dark:text-text-secondary-dark'
        }
      `}>
        {label}
      </Text>
    </TouchableOpacity>
  ));

  const renderRightActions = useCallback((giftIndex: number) => {
    const giftId = `gift-${giftIndex}`;
    const isDeleting = deletingIds.has(giftId);
    
    return (
      <View className="flex-row justify-center items-center mr-2 w-20 rounded-lg bg-error dark:bg-error-dark">
        <TouchableOpacity
          className="flex-1 justify-center items-center"
          onPress={() => handleDeleteGift(giftIndex)}
          disabled={isDeleting}
        >
          {isDeleting ? (
            <LoadingIndicator size="small" color="white" />
          ) : (
            <Feather name="trash-2" size={20} color="white" />
          )}
        </TouchableOpacity>
      </View>
          );
    }, [deletingIds, handleDeleteGift]);

  const renderGiftItem = useCallback(({ item, index }: { item: PastGiftGiven; index: number }) => {
    const giftId = `gift-${index}`;
    const isDeleting = deletingIds.has(giftId);
    const isExpanded = expandedReactions.has(index);
    const shouldShowToggle = item.reaction && item.reaction.length > 100;
    
    // Calculate enhanced metrics
    const successScore = calculateSuccessScore(item);
    const successColor = getSuccessScoreColor(successScore);
    const sourceInfo = getSourceInfo(item.source);
    const hasEnhancedData = item.version === 2 || item.rating || item.loved !== undefined || item.estimatedCost;

    return (
      <Swipeable
        renderRightActions={() => renderRightActions(index)}
        rightThreshold={40}
      >
        <Animated.View
          entering={SlideInRight.delay(Math.min(index * 30, 300)).duration(250)}
          exiting={SlideOutLeft.duration(200)}
          layout={Layout.springify().damping(20)}
          className="mb-4"
        >
          <Card className={`p-4 ${hasEnhancedData && successScore >= 80 ? 'border-l-4' : ''}`} 
                style={hasEnhancedData && successScore >= 80 ? { borderLeftColor: successColor } : {}}>
            
            {/* Header Section */}
            <View className="flex-row justify-between items-start mb-3">
              <View className="flex-1 mr-3">
                <View className="flex-row justify-between items-center">
                  <View className="flex-row flex-1 items-center">
                    <View className="p-2 mr-3 rounded-full" style={{ backgroundColor: sourceInfo.color + '20' }}>
                      <Text className="text-lg">{sourceInfo.icon}</Text>
                    </View>
                    <View className="flex-1">
                      <Text className="text-base font-semibold text-text-primary dark:text-text-primary-dark">
                        {item.item}
                      </Text>
                      <View className="flex-row flex-wrap items-center">
                        {item.occasion && (
                          <Text className="mr-2 text-sm text-text-secondary dark:text-text-secondary-dark">
                            For {item.occasion}
                          </Text>
                        )}
                        {item.category && (
                          <View className="px-2 py-1 mr-2 rounded-full bg-accent/10 dark:bg-accent-dark/10">
                            <Text className="text-xs text-accent dark:text-accent-dark">
                              {item.category}
                            </Text>
                          </View>
                        )}
                      </View>
                    </View>
                  </View>

                  {/* Success Score Badge */}
                  {hasEnhancedData && (
                    <View className="items-center ml-2">
                      <View 
                        className="px-2 py-1 rounded-full"
                        style={{ backgroundColor: successColor + '20' }}
                      >
                        <Text 
                          className="text-xs font-bold"
                          style={{ color: successColor }}
                        >
                          {successScore}%
                        </Text>
                      </View>
                    </View>
                  )}
                </View>
              </View>

              <TouchableOpacity
                onPress={() => handleDeleteGift(index)}
                disabled={isDeleting}
                className="p-2 rounded-full active:bg-error/10"
              >
                {isDeleting ? (
                  <LoadingIndicator size="small" />
                ) : (
                  <Feather name="more-horizontal" size={18} color="#9CA3AF" />
                )}
              </TouchableOpacity>
            </View>

            {/* Enhanced Metrics Section */}
            {hasEnhancedData && (
              <View className="mb-3">
                {/* Rating Stars */}
                {item.rating && item.rating > 0 && (
                  <View className="flex-row items-center mb-2">
                    <Text className="mr-2 text-sm text-text-secondary dark:text-text-secondary-dark">
                      Rating:
                    </Text>
                    <View className="flex-row">
                      {[1, 2, 3, 4, 5].map((star) => (
                        <Text key={star} className="text-sm">
                          {star <= item.rating! ? '⭐' : '☆'}
                        </Text>
                      ))}
                    </View>
                  </View>
                )}

                {/* Success Indicators */}
                <View className="flex-row flex-wrap gap-1 mb-2">
                  {item.loved === true && (
                    <View className="flex-row items-center px-2 py-1 bg-red-100 rounded-full dark:bg-red-900/30">
                      <Text className="text-xs">❤️ Loved</Text>
                    </View>
                  )}
                  {item.stillUsed === true && (
                    <View className="flex-row items-center px-2 py-1 bg-green-100 rounded-full dark:bg-green-900/30">
                      <Text className="text-xs">💯 Still Used</Text>
                    </View>
                  )}
                  {item.wouldGiveAgain === true && (
                    <View className="flex-row items-center px-2 py-1 bg-blue-100 rounded-full dark:bg-blue-900/30">
                      <Text className="text-xs">🔄 Would Repeat</Text>
                    </View>
                  )}
                </View>

                {/* Cost and Time Info */}
                <View className="flex-row justify-between items-center">
                  <View className="flex-row items-center space-x-4">
                    {item.estimatedCost && (
                      <View className="flex-row items-center">
                        <MaterialCommunityIcons name="currency-usd" size={14} color="#6B7280" />
                        <Text className="ml-1 text-xs text-text-secondary dark:text-text-secondary-dark">
                          {formatCost(item.estimatedCost)}
                        </Text>
                      </View>
                    )}
                    {item.timeToFind && (
                      <View className="flex-row items-center">
                        <MaterialCommunityIcons name="clock-outline" size={14} color="#6B7280" />
                        <Text className="ml-1 text-xs text-text-secondary dark:text-text-secondary-dark">
                          {item.timeToFind}h
                        </Text>
                      </View>
                    )}
                    {item.source && (
                      <View className="flex-row items-center">
                        <Text className="text-xs text-text-secondary dark:text-text-secondary-dark">
                          {sourceInfo.label}
                        </Text>
                      </View>
                    )}
                  </View>
                </View>
              </View>
            )}

            {/* Reaction Section */}
            {item.reaction && (
              <Animated.View layout={Layout.springify().damping(20)} className="mb-3">
                <View className="p-3 rounded-lg border-l-2 bg-accent/5 dark:bg-accent-dark/5 border-accent/20 dark:border-accent-dark/20">
                  <Text className="mb-1 text-xs font-medium text-accent dark:text-accent-dark">
                    Their Reaction:
                  </Text>
                  <Text 
                    className="text-sm text-text-secondary dark:text-text-secondary-dark"
                    numberOfLines={isExpanded ? undefined : 2}
                  >
                    {item.reaction}
                  </Text>
                  {shouldShowToggle && (
                    <TouchableOpacity
                      onPress={() => toggleReactionExpansion(index)}
                      className="py-1 mt-1"
                      activeOpacity={0.7}
                    >
                      <View className="flex-row items-center">
                        <Text className="text-sm font-medium text-primary dark:text-primary-dark">
                          {isExpanded ? 'Show less' : 'Show more'}
                        </Text>
                        <MaterialCommunityIcons
                          name={isExpanded ? 'chevron-up' : 'chevron-down'}
                          size={16}
                          color={isDark ? '#C70039' : '#A3002B'}
                          style={{ marginLeft: 4 }}
                        />
                      </View>
                    </TouchableOpacity>
                  )}
                </View>
              </Animated.View>
            )}

            {/* Date Section */}
            <View className="flex-row justify-between items-center">
              {item.date && (
                <Text className="text-xs text-text-secondary dark:text-text-secondary-dark">
                  Given: {format(timestampToDate(item.date), 'MMM d, yyyy')}
                </Text>
              )}
              {hasEnhancedData && (
                <Text className="text-xs text-accent dark:text-accent-dark">
                  Enhanced
                </Text>
              )}
            </View>
          </Card>
        </Animated.View>
      </Swipeable>
    );
  }, [deletingIds, handleDeleteGift, renderRightActions, expandedReactions, toggleReactionExpansion, isDark, calculateSuccessScore, getSuccessScoreColor, getSourceInfo, formatCost]);

  if (loading) {
    return (
      <SafeAreaView className="flex-1 bg-background dark:bg-background-dark">
        <View className="flex-1 justify-center items-center">
          <LoadingIndicator size="large" color="#A3002B" />
          <Text className="mt-4 text-base text-text-secondary dark:text-text-secondary-dark">
            Loading past gifts...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView className="flex-1 bg-background dark:bg-background-dark">
        <View className="flex-1 justify-center items-center px-6">
          <Feather name="alert-circle" size={48} color="#DC2626" />
          <Text className="mt-4 text-lg font-semibold text-center text-error dark:text-error-dark">
            {error}
          </Text>
          <Button
            title="Try Again"
            onPress={fetchData}
            variant="primary"
            className="mt-4"
          />
        </View>
      </SafeAreaView>
    );
  }

  const gifts = filteredAndSortedGifts;

  return (
    <SafeAreaView className="flex-1 bg-background dark:bg-background-dark">
      <Stack.Screen
        options={{
          title: `${profile?.name || 'Profile'} - Past Gifts`,
          headerLargeTitle: true,
          headerLargeTitleStyle: { color: "#A3002B" },
          headerTitleStyle: { color: "#A3002B", fontWeight: '600' },
          headerStyle: { backgroundColor: isDark ? '#111827' : '#F9FAFB' },
          headerShadowVisible: false,
          headerRight: () => (
            <AnimatedTouchableOpacity
              style={headerAddButtonAnimatedStyle}
              onPressIn={handleAddButtonPressIn}
              onPressOut={handleAddButtonPressOut}
              onPress={handleAddGift}
              className="flex-row justify-center items-center p-2 mr-1 rounded-full"
              accessibilityLabel="Add new gift"
              accessibilityRole="button"
            >
              <Feather name="plus-circle" size={28} color="#A3002B" />
            </AnimatedTouchableOpacity>
          ),
        }}
      />
      <FlatList
        data={gifts}
        renderItem={renderGiftItem}
        keyExtractor={(_, index) => `gift-${index}`}
        contentContainerStyle={{ padding: 16 }}
        showsVerticalScrollIndicator={false}
        removeClippedSubviews={true}
        maxToRenderPerBatch={10}
        windowSize={10}
        initialNumToRender={8}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={['#A3002B']}
            tintColor={isDark ? '#C70039' : '#A3002B'}
          />
        }
        ListHeaderComponent={
          <View>
            {/* Header */}
            <Animated.View entering={FadeIn.duration(600)} className="mb-6">
              <Text className="text-base text-text-secondary dark:text-text-secondary-dark">
                Gifts you've given and their reactions
              </Text>
            </Animated.View>

            {/* Statistics */}
            {(profile?.pastGiftsGiven && profile.pastGiftsGiven.length > 0) && (
              <StatisticsSection 
                stats={stats}
                isDark={isDark}
              />
            )}

            {/* Search and Filters */}
            {(profile?.pastGiftsGiven && profile.pastGiftsGiven.length > 0) && (
              <Animated.View entering={FadeInDown.delay(400).duration(400)} className="mb-6">
                {/* Search Bar */}
                <View className="flex-row items-center mb-4">
                  <View className="flex-row flex-1 items-center px-4 py-3 rounded-lg border bg-card dark:bg-card-dark border-border dark:border-border-dark">
                    <Feather name="search" size={20} color="#9CA3AF" />
                    <TextInput
                      placeholder="Search gifts, occasions, reactions, categories, costs..."
                      placeholderTextColor="#9CA3AF"
                      value={searchQuery}
                      onChangeText={handleSearchChange}
                      className="flex-1 ml-3 text-text-primary dark:text-text-primary-dark"
                    />
                    {searchQuery.length > 0 && (
                      <TouchableOpacity onPress={clearSearch}>
                        <Feather name="x" size={20} color="#9CA3AF" />
                      </TouchableOpacity>
                    )}
                  </View>
                  <TouchableOpacity
                    onPress={toggleFilters}
                    className={`
                      ml-3 p-3 rounded-lg border
                      ${showFilters 
                        ? 'bg-primary dark:bg-primary-dark border-primary dark:border-primary-dark' 
                        : 'bg-card dark:bg-card-dark border-border dark:border-border-dark'
                      }
                    `}
                  >
                    <Feather 
                      name="filter" 
                      size={20} 
                      color={showFilters ? 'white' : '#9CA3AF'} 
                    />
                  </TouchableOpacity>
                </View>

                {/* Filter Options */}
                {showFilters && (
                  <Animated.View entering={FadeInDown.duration(200)} className="mb-4">
                    {/* Basic Filters */}
                    <Text className="mb-2 text-sm font-medium text-text-secondary dark:text-text-secondary-dark">
                      📅 Date & Content:
                    </Text>
                    <View className="flex-row flex-wrap gap-2 mb-3">
                      <FilterButton
                        label={FILTER_LABELS.all}
                        isActive={filterBy === 'all'}
                        onPress={() => handleFilterChange('all')}
                      />
                      <FilterButton
                        label={FILTER_LABELS.recent}
                        isActive={filterBy === 'recent'}
                        onPress={() => handleFilterChange('recent')}
                      />
                      <FilterButton
                        label={FILTER_LABELS.older}
                        isActive={filterBy === 'older'}
                        onPress={() => handleFilterChange('older')}
                      />
                      <FilterButton
                        label={FILTER_LABELS['with-reaction']}
                        isActive={filterBy === 'with-reaction'}
                        onPress={() => handleFilterChange('with-reaction')}
                      />
                      <FilterButton
                        label={FILTER_LABELS['no-reaction']}
                        isActive={filterBy === 'no-reaction'}
                        onPress={() => handleFilterChange('no-reaction')}
                      />
                    </View>

                    {/* Enhanced Data Filters */}
                    {stats.enhancedDataCount > 0 && (
                      <>
                        <Text className="mb-2 text-sm font-medium text-text-secondary dark:text-text-secondary-dark">
                          ⭐ Success & Quality:
                        </Text>
                        <View className="flex-row flex-wrap gap-2 mb-3">
                          <FilterButton
                            label={FILTER_LABELS['enhanced-only']}
                            isActive={filterBy === 'enhanced-only'}
                            onPress={() => handleFilterChange('enhanced-only')}
                          />
                          <FilterButton
                            label={FILTER_LABELS['high-rating']}
                            isActive={filterBy === 'high-rating'}
                            onPress={() => handleFilterChange('high-rating')}
                          />
                          <FilterButton
                            label={FILTER_LABELS['loved-gifts']}
                            isActive={filterBy === 'loved-gifts'}
                            onPress={() => handleFilterChange('loved-gifts')}
                          />
                          <FilterButton
                            label={FILTER_LABELS['high-success']}
                            isActive={filterBy === 'high-success'}
                            onPress={() => handleFilterChange('high-success')}
                          />
                        </View>
                      </>
                    )}

                    {/* Cost & Source Filters */}
                    {(stats.totalSpent > 0 || stats.handmadeCount > 0) && (
                      <>
                        <Text className="mb-2 text-sm font-medium text-text-secondary dark:text-text-secondary-dark">
                          💰 Cost & Source:
                        </Text>
                        <View className="flex-row flex-wrap gap-2 mb-3">
                          {stats.budgetFriendlyCount > 0 && (
                            <FilterButton
                              label={FILTER_LABELS['budget-friendly']}
                              isActive={filterBy === 'budget-friendly'}
                              onPress={() => handleFilterChange('budget-friendly')}
                            />
                          )}
                          {stats.totalSpent > 0 && (
                            <FilterButton
                              label={FILTER_LABELS['expensive']}
                              isActive={filterBy === 'expensive'}
                              onPress={() => handleFilterChange('expensive')}
                            />
                          )}
                          {stats.handmadeCount > 0 && (
                            <FilterButton
                              label={FILTER_LABELS['handmade']}
                              isActive={filterBy === 'handmade'}
                              onPress={() => handleFilterChange('handmade')}
                            />
                          )}
                        </View>
                      </>
                    )}
                    
                    {/* Sort Options */}
                    <Text className="mb-2 text-sm font-medium text-text-secondary dark:text-text-secondary-dark">
                      🔽 Sort by:
                    </Text>
                    
                    {/* Basic Sort Options */}
                    <View className="flex-row flex-wrap gap-2 mb-2">
                      <FilterButton
                        label="Recent"
                        isActive={sortBy === 'recent'}
                        onPress={() => handleSortChange('recent')}
                      />
                      <FilterButton
                        label="Oldest"
                        isActive={sortBy === 'oldest'}
                        onPress={() => handleSortChange('oldest')}
                      />
                      <FilterButton
                        label="Occasion"
                        isActive={sortBy === 'occasion'}
                        onPress={() => handleSortChange('occasion')}
                      />
                      <FilterButton
                        label="Item"
                        isActive={sortBy === 'item'}
                        onPress={() => handleSortChange('item')}
                      />
                    </View>

                    {/* Enhanced Sort Options */}
                    {stats.enhancedDataCount > 0 && (
                      <View className="flex-row flex-wrap gap-2">
                        <FilterButton
                          label="⭐ Rating"
                          isActive={sortBy === 'rating'}
                          onPress={() => handleSortChange('rating')}
                        />
                        <FilterButton
                          label="🏆 Success"
                          isActive={sortBy === 'success-score'}
                          onPress={() => handleSortChange('success-score')}
                        />
                        {stats.totalSpent > 0 && (
                          <>
                            <FilterButton
                              label="💸 Priciest"
                              isActive={sortBy === 'cost-high'}
                              onPress={() => handleSortChange('cost-high')}
                            />
                            <FilterButton
                              label="💰 Cheapest"
                              isActive={sortBy === 'cost-low'}
                              onPress={() => handleSortChange('cost-low')}
                            />
                          </>
                        )}
                      </View>
                    )}
                  </Animated.View>
                )}
              </Animated.View>
            )}

            {/* Results Summary */}
            {profile?.pastGiftsGiven && profile.pastGiftsGiven.length > 0 && (
              <View className="mb-4">
                <View className="flex-row justify-between items-center">
                  <Text className="text-sm text-text-secondary dark:text-text-secondary-dark">
                    {gifts.length === profile.pastGiftsGiven.length 
                      ? `Showing all ${profile.pastGiftsGiven.length} gifts`
                      : `Showing ${gifts.length} of ${profile.pastGiftsGiven.length} gifts`
                    }
                  </Text>
                  
                  {/* Quick stats for filtered results */}
                  {gifts.length !== profile.pastGiftsGiven.length && gifts.length > 0 && (
                    <View className="flex-row items-center">
                      {/* Success rate of filtered results */}
                      {(() => {
                        const enhancedFilteredGifts = gifts.filter(gift => 
                          gift.version === 2 || gift.rating || gift.loved !== undefined || gift.estimatedCost
                        );
                        const avgSuccessScore = enhancedFilteredGifts.length > 0 
                          ? enhancedFilteredGifts.reduce((sum, gift) => sum + calculateSuccessScore(gift), 0) / enhancedFilteredGifts.length
                          : 0;
                        
                        if (avgSuccessScore > 0) {
                          return (
                            <View className="px-2 py-1 rounded-full bg-accent/10 dark:bg-accent-dark/10">
                              <Text className="text-xs text-accent dark:text-accent-dark">
                                Avg: {Math.round(avgSuccessScore)}% success
                              </Text>
                            </View>
                          );
                        }
                        return null;
                      })()}
                    </View>
                  )}
                </View>
              </View>
            )}
          </View>
        }
                  ListEmptyComponent={
            // Show different empty states based on whether we have items but they're filtered out
            profile?.pastGiftsGiven && profile.pastGiftsGiven.length > 0 && gifts.length === 0 ? (
              <FilteredEmptyState
                filterType={filterBy as any}
                itemType="gifts"
                totalCount={profile.pastGiftsGiven.length}
                onAddAction={handleAddGift}
                onClearFilter={() => {
                  setFilterBy('all');
                  setSearchQuery('');
                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                }}
                searchQuery={searchQuery || undefined}
              />
            ) : (
              <Animated.View entering={FadeIn.duration(600)} className="mt-8">
                <EmptyState
                  icon="gift"
                  title="No Past Gifts Yet"
                  description="Start tracking the gifts you've given to build a history of what works well and what doesn't, helping you choose better gifts in the future."
                  actionText="Add First Gift"
                  onAction={handleAddGift}
                  examples={[
                    "Birthday: Concert tickets",
                    "Anniversary: Handmade photo album",
                    "Holiday: Favorite book series"
                  ]}
                  benefit="Tracking past gifts helps you avoid duplicates, understand what they love, and build on successful gift-giving patterns for more thoughtful future presents."
                />
              </Animated.View>
            )
          }
      />
      
      {/* Add Gift Modal */}
      <AddPastGiftModal
        isVisible={showAddGiftModal}
        onClose={handleCloseAddGiftModal}
        onAddItem={handleSaveGift}
      />
    </SafeAreaView>
  );
};

export default PastGiftsScreen;
