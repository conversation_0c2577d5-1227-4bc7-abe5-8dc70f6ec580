import React from 'react';
import { TouchableOpacity, ViewStyle, GestureResponderEvent } from 'react-native';
import Animated, { useSharedValue, useAnimatedStyle, withSpring } from 'react-native-reanimated';
import { DailyGiftData } from '../../utils/chartDataUtils';

const AnimatedTouchableOpacity = Animated.createAnimatedComponent(TouchableOpacity);

interface ContributionSquareProps {
  dayData: DailyGiftData;
  size: number;
  spacing: number;
  color: string;
  onPress?: (event: GestureResponderEvent) => void;
  style?: ViewStyle;
}

const ContributionSquare: React.FC<ContributionSquareProps> = ({
  dayData,
  size,
  spacing,
  color,
  onPress,
  style
}) => {
  const scale = useSharedValue(1);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: scale.value }],
    };
  });

  const handlePressIn = () => {
    scale.value = withSpring(0.9, { damping: 15, stiffness: 300 });
  };

  const handlePressOut = () => {
    scale.value = withSpring(1, { damping: 15, stiffness: 300 });
  };

  return (
    <AnimatedTouchableOpacity
      onPress={onPress}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      style={[
        {
          width: size,
          height: size,
          backgroundColor: color,
          borderRadius: 2,
        },
        animatedStyle,
        style
      ]}
      activeOpacity={0.8}
      accessibilityLabel={`${dayData.date}: ${dayData.giftCount} gifts given`}
      accessibilityRole="button"
    />
  );
};

export default ContributionSquare; 