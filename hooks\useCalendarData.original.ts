import { useState, useEffect, useCallback, useMemo } from 'react';
import { useFocusEffect } from 'expo-router';
import { Timestamp } from 'firebase/firestore';
import { CustomDate, SignificantOtherProfile } from '@/functions/src/types/firestore';
import { useAuth } from '@/contexts/AuthContext';
import { getSignificantOthers } from '@/services/profileService';
import { format, isToday, addYears, isPast, differenceInDays } from 'date-fns';
import localHolidays from '@/utils/holidays.json';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { 
  PROFILES_CACHE_KEY, 
  CACHE_EXPIRY_KEY, 
  PROFILES_LAST_UPDATED_KEY,
  SELECTED_PROFILE_KEY 
} from '@/constants/storageKeys';

// Cache constants
const CACHE_EXPIRY_TIME = 1000 * 60 * 30; // 30 minutes

// Interface for multi-dot marking
interface Dot {
  key: string; // Unique key for the dot (e.g., 'birthday-profileId', 'holiday-name')
  color: string;
  selectedDotColor?: string; // Optional: color when the day is selected
}

interface MarkedDates {
  [date: string]: {
    dots?: Dot[];
    marked?: boolean; // Can still use marked for general marking if needed
    selected?: boolean;
    selectedColor?: string;
    disabled?: boolean;
    disableTouchEvent?: boolean;
    activeOpacity?: number;
  };
}

// Define the type for processed dates
export interface CalendarEvent {
  id: string; // Unique ID for the event
  name: string;
  type: 'Birthday' | 'Anniversary' | 'Custom Date' | 'Holiday';
  date: Date;
  daysUntil: number;
  profileId?: string; // Link to profile if applicable
  description?: string; // For holidays
}

// Define the type for processed events map
interface ProcessedEvents {
  [date: string]: CalendarEvent[];
}

// Mapping for event types to theme colors (hex codes)
const eventColorMap: { [key in CalendarEvent['type']]: string } = {
  'Birthday': '#22C55E', // Using 'birthday' color from tailwind.config.js
  'Anniversary': '#3B82F6', // Using 'anniversary' color from tailwind.config.js
  'Custom Date': '#A855F7', // Using 'customDate' color from tailwind.config.js
  'Holiday': '#EF4444', // Using 'holiday' color from tailwind.config.js
};

// Helper function to get the next occurrence of a yearly date
const getNextOccurrence = (date: Date, today: Date): Date => {
  const currentYear = today.getFullYear();
  const dateThisYear = new Date(today.getFullYear(), date.getMonth(), date.getDate());

  if (isPast(dateThisYear) && !isToday(dateThisYear)) {
    return addYears(dateThisYear, 1);
  }
  return dateThisYear;
};

// Helper function to safely convert a date field (Timestamp, Date, ISO string) to a Date object
function safeConvertToDate(dateField: any): Date | null {
  if (!dateField) return null;
  try {
    // Handle Firestore Timestamp or potential ISO strings/Date objects
    const date = dateField.seconds && typeof dateField.seconds === 'number' && typeof dateField.nanoseconds === 'number'
      ? new Timestamp(dateField.seconds, dateField.nanoseconds).toDate()
      : new Date(dateField);
    // Check if the resulting date is valid
    if (isNaN(date.getTime())) {
      console.warn('Invalid date object encountered after conversion:', dateField);
      return null;
    }
    return date;
  } catch (e) {
    console.warn('Error converting date field:', dateField, e);
    return null;
  }
}

const useCalendarData = () => {
  const [profiles, setProfiles] = useState<SignificantOtherProfile[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [markedDates, setMarkedDates] = useState<MarkedDates>({});
  const [processedEvents, setProcessedEvents] = useState<ProcessedEvents>({});
  const [closestDate, setClosestDate] = useState<CalendarEvent | null>(null);
  const [upcomingDates, setUpcomingDates] = useState<CalendarEvent[]>([]);
  const [selectedProfileId, setSelectedProfileId] = useState<string | null>(null);
  const [dataInitialized, setDataInitialized] = useState(false);
  const [lastProfilesFetchTimestamp, setLastProfilesFetchTimestamp] = useState<number>(0); // Track last fetch time in this hook

  const { user } = useAuth();

  // Add a cache management function (simplified for now)
  const updateCache = async (key: string, data: any) => {
    try {
      await AsyncStorage.setItem(key, JSON.stringify(data));
      await AsyncStorage.setItem(CACHE_EXPIRY_KEY, Date.now().toString());
    } catch (error) {
      console.error(`Error updating cache for ${key}:`, error);
    }
  };

  // Check if cache is valid (simplified for now)
  const isCacheValid = async () => {
    try {
      const expiryTime = await AsyncStorage.getItem(CACHE_EXPIRY_KEY);
      if (!expiryTime) return false;

      const timeDiff = Date.now() - parseInt(expiryTime);
      return timeDiff < CACHE_EXPIRY_TIME;
    } catch (error) {
      return false;
    }
  };

  // Process dates function to generate markings and events map
  const processDates = useCallback((profiles: SignificantOtherProfile[], holidays: { name: string; month: number; day: number; type: string; description: string; }[]) => {
    const marks: MarkedDates = {};
    const eventsMap: ProcessedEvents = {};
    const allUpcomingDates: CalendarEvent[] = [];
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Process profile dates - ensure this only happens for the selected profile
    profiles.forEach(profile => {
      // Process birthday
      // Process birthday
      const birthdayDate = safeConvertToDate(profile.birthday);
      if (birthdayDate) {
        const nextOccurrence = getNextOccurrence(birthdayDate, today);
        const daysUntil = differenceInDays(nextOccurrence, today);

        if (daysUntil >= 0 && daysUntil <= 365) {
          const event: CalendarEvent = {
            id: `birthday-${profile.profileId}`,
            name: profile.name,
            type: 'Birthday',
            date: nextOccurrence,
            daysUntil: daysUntil,
            profileId: profile.profileId,
          };
          allUpcomingDates.push(event);
          const dateString = format(nextOccurrence, 'yyyy-MM-dd');
          if (!marks[dateString]) {
            marks[dateString] = { dots: [] };
          }
          marks[dateString].dots?.push({ key: event.id, color: eventColorMap[event.type] });
          if (!eventsMap[dateString]) {
            eventsMap[dateString] = [];
          }
          eventsMap[dateString].push(event);
        }
      }

      // Process anniversary
      // Process anniversary
      const anniversaryDate = safeConvertToDate(profile.anniversary);
      if (anniversaryDate) {
        const nextOccurrence = getNextOccurrence(anniversaryDate, today);
        const daysUntil = differenceInDays(nextOccurrence, today);

        if (daysUntil >= 0 && daysUntil <= 365) {
          const event: CalendarEvent = {
            id: `anniversary-${profile.profileId}`,
            name: profile.name,
            type: 'Anniversary',
            date: nextOccurrence,
            daysUntil: daysUntil,
            profileId: profile.profileId,
          };
          allUpcomingDates.push(event);
          const dateString = format(nextOccurrence, 'yyyy-MM-dd');
          if (!marks[dateString]) {
            marks[dateString] = { dots: [] };
          }
          marks[dateString].dots?.push({ key: event.id, color: eventColorMap[event.type] });
          if (!eventsMap[dateString]) {
            eventsMap[dateString] = [];
          }
          eventsMap[dateString].push(event);
        }
      }

      // Process custom dates
      if (profile.customDates && Array.isArray(profile.customDates)) {
        profile.customDates.forEach((customDate: CustomDate) => {
          const date = safeConvertToDate(customDate.date);
          if (date) {
            const nextOccurrence = getNextOccurrence(date, today);
            const daysUntil = differenceInDays(nextOccurrence, today);

            if (daysUntil >= 0 && daysUntil <= 365) {
              const event: CalendarEvent = {
                id: `custom-${profile.profileId}-${customDate.name}`,
                name: `${customDate.name} (${profile.name})`,
                type: 'Custom Date',
                date: nextOccurrence,
                daysUntil: daysUntil,
                profileId: profile.profileId,
              };
              allUpcomingDates.push(event);
              const dateString = format(nextOccurrence, 'yyyy-MM-dd');
              if (!marks[dateString]) {
                marks[dateString] = { dots: [] };
              }
              marks[dateString].dots?.push({ key: event.id, color: eventColorMap[event.type] });
              if (!eventsMap[dateString]) {
                eventsMap[dateString] = [];
              }
              eventsMap[dateString].push(event);
            }
          }
        });
      }
    });

    // Process holidays (only for the current year and next year)
    holidays.forEach((holiday) => {
      const currentYear = today.getFullYear();
      for (let year = currentYear; year <= currentYear + 1; year++) {
        const holidayDate = new Date(year, holiday.month - 1, holiday.day);
        const daysUntil = differenceInDays(holidayDate, today);

        if (daysUntil >= 0 && daysUntil <= 365) {
          const event: CalendarEvent = {
            id: `holiday-${holiday.name}-${year}`,
            name: holiday.name,
            type: 'Holiday',
            date: holidayDate,
            daysUntil: daysUntil,
            description: holiday.description,
          };
          allUpcomingDates.push(event);
          const dateString = format(holidayDate, 'yyyy-MM-dd');
          if (!marks[dateString]) {
            marks[dateString] = { dots: [] };
          }
          marks[dateString].dots?.push({ key: event.id, color: eventColorMap[event.type] });
          if (!eventsMap[dateString]) {
            eventsMap[dateString] = [];
          }
          eventsMap[dateString].push(event);
        }
      }
    });

    // Sort all upcoming dates and find the closest
    const sortedUpcomingDates = allUpcomingDates.sort((a, b) => a.daysUntil - b.daysUntil);
    const closest = sortedUpcomingDates.length > 0 ? sortedUpcomingDates[0] : null;

    // Update state with processed data
    setMarkedDates(marks);
    setProcessedEvents(eventsMap);
    setClosestDate(closest);
    setUpcomingDates(sortedUpcomingDates);
  }, []); // Empty dependency array since we don't depend on any changing values

 // Optimized data loading function
 // Added forceRefresh parameter and logic to update last fetch timestamp
  const optimizedDataLoad = async (forceRefresh = false) => {
    if (!user) {
      setError('User not authenticated.');
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);

      // Start by loading the selected profile ID (critical for UI)
      const savedProfileId = await AsyncStorage.getItem(SELECTED_PROFILE_KEY);
      if (savedProfileId) {
        setSelectedProfileId(savedProfileId);
      }

      // Check if we can use cached data (only if not forcing refresh)
      const cacheValid = !forceRefresh && (await isCacheValid());
      let profilesResult: SignificantOtherProfile[] = [];

      if (cacheValid) {
        try {
          const cachedProfilesString = await AsyncStorage.getItem(PROFILES_CACHE_KEY);
          if (cachedProfilesString) {
            profilesResult = JSON.parse(cachedProfilesString);
            setProfiles(profilesResult);
            console.log('CALENDAR HOOK: Using cached profiles');
          }
        } catch (cacheError) {
          console.error('CALENDAR HOOK: Error reading profiles from cache:', cacheError);
        }
      }

      // If cache wasn't valid or profiles weren't in cache, fetch from network
      if (profilesResult.length === 0 || forceRefresh) {
        console.log('CALENDAR HOOK: Fetching profiles from network...');
        profilesResult = await getSignificantOthers(user.uid);
        console.log('CALENDAR HOOK: Fetched profiles count:', profilesResult?.length || 0);
        setProfiles(profilesResult);

        // Update cache
        if (profilesResult.length > 0) {
          updateCache(PROFILES_CACHE_KEY, profilesResult);
        }
      }

      // Update the last fetch timestamp state
      setLastProfilesFetchTimestamp(Date.now());

      // Determine selected profile
      let profileToUse = null;

      if (savedProfileId) {
        const savedProfile = profilesResult.find(p => p.profileId === savedProfileId);
        if (savedProfile) {
          profileToUse = savedProfile;
          setSelectedProfileId(savedProfile.profileId);
        }
      }

      // If no saved profile or it wasn't found, use the first one
      if (!profileToUse && profilesResult.length > 0) {
        profileToUse = profilesResult[0];
        setSelectedProfileId(profileToUse.profileId);
        await AsyncStorage.setItem(SELECTED_PROFILE_KEY, profileToUse.profileId);
      }

      // Process dates only for the selected profile, using local holidays
      if (profileToUse) {
        processDates([profileToUse], localHolidays);
      } else {
        // If no profiles, process only holidays
        processDates([], localHolidays);
      }

      setError(null);

    } catch (err) {
      console.error('Error in optimized data load:', err);
      setError('Failed to load data. Please try again.');
    } finally {
      setIsLoading(false);
      setDataInitialized(true);
    }
  };

  // On initial mount, load data
  useEffect(() => {
    if (!dataInitialized && user?.uid) {
      optimizedDataLoad();
    }
  }, [dataInitialized, user?.uid]); // Only depend on dataInitialized and user.uid, not the full user object

 // Use useFocusEffect to check for profile updates and refresh if needed
 useFocusEffect(
   useCallback(() => {
     const checkForProfileUpdatesAndSyncSelected = async () => {
       console.log('CALENDAR HOOK: useFocusEffect running, checking for profile updates and syncing selected profile');
       try {
         const lastUpdatedString = await AsyncStorage.getItem(PROFILES_LAST_UPDATED_KEY);
         const lastUpdatedTimestamp = lastUpdatedString ? parseInt(lastUpdatedString, 10) : 0;
         const savedProfileId = await AsyncStorage.getItem(SELECTED_PROFILE_KEY); // Read selected profile from storage
         console.log(`CALENDAR HOOK: useFocusEffect - Current state selectedProfileId: ${selectedProfileId}, AsyncStorage selectedProfileId: ${savedProfileId}`);

         // Check if the selected profile in storage is different from the current state
         if (savedProfileId && savedProfileId !== selectedProfileId) {
            console.log(`CALENDAR HOOK: useFocusEffect - Detected selected profile change in AsyncStorage to ${savedProfileId}. Updating state and processing dates.`);
            setSelectedProfileId(savedProfileId);
            // Find the newly selected profile and process dates immediately
            const newlySelectedProfile = profiles.find(p => p.profileId === savedProfileId);
            if (newlySelectedProfile) {
               processDates([newlySelectedProfile], localHolidays);
            } else {
               // If the profile from storage is not found in the current profiles list,
               // it might mean the list is stale. Force a full reload.
               console.log('CALENDAR HOOK: Selected profile from storage not found in current list. Forcing full reload.');
               optimizedDataLoad(true);
            }
         }

         // If the last updated timestamp in storage is newer than our last fetch timestamp,
         // or if data hasn't been initialized yet, force a reload of the profile list.
         // This check runs even if the selected profile was just synced, in case the list itself is stale.
         if (lastUpdatedTimestamp > lastProfilesFetchTimestamp || !dataInitialized) {
           console.log('CALENDAR HOOK: Profile list updates detected or data not initialized, forcing list reload.');
           optimizedDataLoad(true); // Force refresh and update last fetch timestamp state
         } else {
           console.log('CALENDAR HOOK: No profile list updates detected, using existing data.');
           // If no list updates and no selected profile change was detected above,
           // ensure dates are processed for the currently selected profile if profiles are loaded.
           // This handles cases where the screen gains focus but neither the list nor the selected ID changed in storage,
           // but perhaps the internal state was reset or needs re-processing.
           if (selectedProfileId && profiles.length > 0) {
              const currentProfile = profiles.find(p => p.profileId === selectedProfileId);
              if (currentProfile) {
                 console.log('CALENDAR HOOK: No updates detected, re-processing dates for current selected profile.');
                 processDates([currentProfile], localHolidays);
              } else {
                 // This case should ideally not happen if profiles.length > 0 and selectedProfileId is set,
                 // but as a fallback, if the selected profile isn't found in the current list,
                 // it might indicate a stale list, so force a reload.
                 console.log('CALENDAR HOOK: Selected profile ID set, but profile not found in current list. Forcing full reload.');
                 optimizedDataLoad(true);
              }
           } else if (!selectedProfileId && profiles.length > 0) {
              // If no profile is selected but profiles exist, select the first one
              const firstProfile = profiles[0];
              console.log(`CALENDAR HOOK: No profile selected, defaulting to first profile: ${firstProfile.profileId}`);
              setSelectedProfileId(firstProfile.profileId);
              await AsyncStorage.setItem(SELECTED_PROFILE_KEY, firstProfile.profileId);
              processDates([firstProfile], localHolidays);
           } else if (profiles.length === 0) {
              // If no profiles exist, process only holidays
              console.log('CALENDAR HOOK: No profiles found, processing only holidays.');
              processDates([], localHolidays);
           }
         }
       } catch (error) {
         console.error('CALENDAR HOOK: Error in useFocusEffect:', error);
         // In case of error checking timestamp or selected profile, still try to load data if not initialized
          if (!dataInitialized) {
            optimizedDataLoad(false); // Try loading without forcing cache bypass
          }
         }
       };

     checkForProfileUpdatesAndSyncSelected();

   }, [dataInitialized, lastProfilesFetchTimestamp]) // Remove profiles, selectedProfileId, and processDates from dependencies to prevent infinite loops
 );

  // Modified handleProfileSelect to immediately process dates
  const handleProfileSelect = useCallback(async (profileId: string) => {
    if (profileId === selectedProfileId) {
      return;
    }

    setSelectedProfileId(profileId);

    try {
      // Save to AsyncStorage
      await AsyncStorage.setItem(SELECTED_PROFILE_KEY, profileId);

      // Reset date markers and events
      setMarkedDates({});
      setProcessedEvents({});
      setClosestDate(null);
      setUpcomingDates([]);

      // Process dates for the new profile
      const selectedProfile = profiles.find(p => p.profileId === profileId);
      if (selectedProfile) {
        processDates([selectedProfile], localHolidays);
      } else {
         // If no profile found (shouldn't happen if profileId is valid), process only holidays
         processDates([], localHolidays);
      }
    } catch (error) {
      console.error('Error saving selected profile:', error);
    }
  }, [profiles, selectedProfileId]); // Remove processDates from dependencies

  return {
    profiles,
    processedEvents,
    calendarMarkings: markedDates,
    closestDate,
    upcomingDates,
    isLoading,
    error,
    selectedProfileId,
    handleProfileSelect,
    optimizedDataLoad // Expose optimizedDataLoad for manual refresh if needed
  };
};

export default useCalendarData;