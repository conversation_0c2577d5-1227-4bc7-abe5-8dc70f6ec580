import { Stack } from 'expo-router';

export default function SettingsLayout() {
  return (
    <Stack
      screenOptions={{
        headerLargeTitle: true,
        headerLargeTitleStyle: { color: '#A3002B' },
        headerTitleStyle: { color: '#A3002B', fontWeight: '600' },
        headerStyle: { backgroundColor: '#F9FAFB' },
        headerShadowVisible: false,
      }}
    >
      <Stack.Screen name="index" options={{ title: 'Settings', headerShown: false }} />
      <Stack.Screen name="personal-info" options={{ title: 'Personal Information' }} />
      <Stack.Screen name="notifications" options={{ title: 'Notification Settings' }} />
      <Stack.Screen name="privacy-security" options={{ title: 'Privacy & Security' }} />
      <Stack.Screen name="help-support" options={{ title: 'Help & Support' }} />
      {/* Add other settings screens here later */}
    </Stack>
  );
}
