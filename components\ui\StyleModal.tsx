import React from 'react';
import { View, Text, TouchableOpacity, ScrollView, Modal, Dimensions } from 'react-native';
import { Feather } from '@expo/vector-icons';
import Animated, { 
  useAnimatedStyle, 
  useSharedValue, 
  withSpring,
  FadeIn,
  FadeOut
} from 'react-native-reanimated';
import { useColorScheme } from 'nativewind';

interface StyleOption {
  label: string;
  value: string;
  description: string;
  icon: string;
  category: string;
}

interface StyleModalProps {
  isVisible: boolean;
  onClose: () => void;
  options: StyleOption[];
  selectedValue?: string;
  onValueChange: (value: string) => void;
  title?: string;
}

const AnimatedTouchableOpacity = Animated.createAnimatedComponent(TouchableOpacity);
const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

const StyleModal: React.FC<StyleModalProps> = ({
  isVisible,
  onClose,
  options,
  selectedValue,
  onValueChange,
  title = "Choose Style Preference"
}) => {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  // Theme colors
  const themedColors = {
    primary: isDark ? '#D96D00' : '#A3002B',
    textPrimary: isDark ? '#F9FAFB' : '#1F2937',
    textSecondary: isDark ? '#9CA3AF' : '#6B7280',
    border: isDark ? '#374151' : '#E5E7EB',
    background: isDark ? '#111827' : '#F9FAFB',
    card: isDark ? '#1F2937' : '#FFFFFF',
    backdrop: isDark ? 'rgba(0, 0, 0, 0.7)' : 'rgba(0, 0, 0, 0.5)',
  };

  const handleStyleSelect = (value: string) => {
    onValueChange(value);
    onClose();
  };

  // Group options by category
  const groupedOptions = options.reduce((groups, option) => {
    const category = option.category;
    if (!groups[category]) {
      groups[category] = [];
    }
    groups[category].push(option);
    return groups;
  }, {} as Record<string, StyleOption[]>);

  const StyleCard = ({ option }: { option: StyleOption }) => {
    const isSelected = selectedValue === option.value;
    const scaleValue = useSharedValue(1);

    const animatedStyle = useAnimatedStyle(() => ({
      transform: [{ scale: scaleValue.value }],
    }));

    const handlePress = () => {
      scaleValue.value = withSpring(0.98, { duration: 100 });
      setTimeout(() => {
        scaleValue.value = withSpring(1, { duration: 100 });
      }, 100);
      handleStyleSelect(option.value);
    };

    return (
      <AnimatedTouchableOpacity
        style={[
          animatedStyle,
          {
            backgroundColor: isSelected ? themedColors.primary + '10' : themedColors.card,
          }
        ]}
        onPress={handlePress}
        className="grid grid-cols-2 p-4 mb-3 rounded-xl border-2 border-border"
        accessibilityRole="button"
        accessibilityLabel={`Select ${option.label} style`}
        accessibilityState={{ selected: isSelected }}
      >
        <View className="flex-row items-start">
          {/* Icon */}
          <View
            className="p-3 mr-4 rounded-full"
            style={{
              backgroundColor: isSelected ? themedColors.primary : themedColors.background,
            }}
          >
            <Feather
              name={option.icon as any}
              size={24}
              color={isSelected ? '#FFFFFF' : themedColors.primary}
            />
          </View>

          {/* Content */}
          <View className="flex-1">
            <View className="flex-row justify-between items-center mb-2">
              <Text
                className={`text-lg font-bold ${isSelected ? 'font-bold' : 'font-semibold'}`}
                style={{
                  color: isSelected ? themedColors.primary : themedColors.textPrimary,
                }}
              >
                {option.label}
              </Text>
              
              {/* Selected Check Mark */}
              {isSelected && (
                <View
                  className="p-1 ml-2 rounded-full"
                  style={{ backgroundColor: themedColors.primary }}
                >
                  <Feather
                    name="check"
                    size={16}
                    color="#FFFFFF"
                  />
                </View>
              )}
            </View>

            <Text
              className="text-sm leading-5"
              style={{
                color: isSelected ? themedColors.textPrimary : themedColors.textSecondary,
              }}
            >
              {option.description}
            </Text>
          </View>
        </View>
      </AnimatedTouchableOpacity>
    );
  };

  return (
    <Modal
      visible={isVisible}
      transparent={true}
      animationType="fade"
      statusBarTranslucent={true}
      onRequestClose={onClose}
    >
      <TouchableOpacity
        activeOpacity={1}
        onPress={onClose}
        className="flex-1 justify-center items-center"
        style={{ backgroundColor: themedColors.backdrop }}
      >
        <Animated.View
          entering={FadeIn.duration(200)}
          exiting={FadeOut.duration(150)}
          className="overflow-hidden mx-4 rounded-2xl"
          style={{
            backgroundColor: themedColors.card,
            maxWidth: screenWidth * 0.92,
            maxHeight: screenHeight * 0.85,
            minWidth: screenWidth * 0.85,
          }}
          onStartShouldSetResponder={() => true}
        >
          {/* Header */}
          <View 
            className="flex-row justify-between items-center px-6 py-4 border-b"
            style={{ borderBottomColor: themedColors.border }}
          >
            <Text
              className="text-lg font-bold"
              style={{ color: themedColors.textPrimary }}
            >
              {title}
            </Text>
            <TouchableOpacity
              onPress={onClose}
              className="justify-center items-center w-8 h-8 rounded-full"
              style={{ backgroundColor: themedColors.background }}
              accessibilityRole="button"
              accessibilityLabel="Close style selection"
            >
              <Feather name="x" size={18} color={themedColors.textSecondary} />
            </TouchableOpacity>
          </View>

          {/* Style Cards by Category */}
          <ScrollView
            showsVerticalScrollIndicator={false}
            className="px-6 py-4"
            style={{ maxHeight: screenHeight * 0.7 }}
          >
            {Object.entries(groupedOptions).map(([category, categoryOptions]) => (
              <View key={category} className="mb-6">
                {/* Category Header */}
                <Text
                  className="mb-3 text-sm font-semibold tracking-wide uppercase"
                  style={{ color: themedColors.textSecondary }}
                >
                  {category}
                </Text>
                
                {/* Category Options */}
                {categoryOptions.map((option) => (
                  <StyleCard key={option.value} option={option} />
                ))}
              </View>
            ))}
          </ScrollView>
        </Animated.View>
      </TouchableOpacity>
    </Modal>
  );
};

export default StyleModal; 