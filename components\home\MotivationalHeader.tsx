import React, { useState, useEffect } from 'react';
import { View, Text } from 'react-native';
import Animated, { FadeIn, FadeOut } from 'react-native-reanimated';
import { useColorScheme } from 'nativewind';
import { Feather } from '@expo/vector-icons';
import { getEnhancedGreeting, getTimeBasedGradient } from '../../utils/dateUtils';
import { 
  enhancedMotivationalMessages, 
  getContextualMessage, 
  legacyMotivationalMessages,
  type MotivationalMessage 
} from '../../constants/suggestions';
import { colors } from '../../constants/Colors';

interface MotivationalHeaderProps {
  userName?: string;
  // Optional context for smart message selection
  upcomingDatesCount?: number;
  profileCompletionCount?: number;
  totalProfiles?: number;
  enableEnhancedMessaging?: boolean; // Feature flag for gradual rollout
  selectedProfileName?: string; // NEW: Current selected profile name for personalization
}

const MotivationalHeader: React.FC<MotivationalHeaderProps> = ({ 
  userName,
  upcomingDatesCount = 0,
  profileCompletionCount = 0,
  totalProfiles = 1,
  enableEnhancedMessaging = true,
  selectedProfileName
}) => {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  
  // State for enhanced messaging
  const [currentMessage, setCurrentMessage] = useState<MotivationalMessage | null>(null);
  const [currentLegacyIndex, setCurrentLegacyIndex] = useState(0);

  // Get enhanced greeting data
  const greetingData = getEnhancedGreeting(userName);
  const gradientClasses = getTimeBasedGradient(greetingData.timeContext);

  // Determine time of year for contextual messaging
  const getTimeOfYear = (): 'holiday' | 'summer' | 'back-to-school' | 'spring' | 'regular' => {
    const month = new Date().getMonth(); // 0-11
    if (month >= 10 || month <= 1) return 'holiday'; // Nov-Jan
    if (month >= 5 && month <= 7) return 'summer'; // Jun-Aug
    if (month === 8) return 'back-to-school'; // Sep
    if (month >= 2 && month <= 4) return 'spring'; // Mar-May
    return 'regular';
  };

  // Determine day of week context
  const getDayOfWeek = (): 'weekend' | 'weekday' => {
    const day = new Date().getDay();
    return (day === 0 || day === 6) ? 'weekend' : 'weekday';
  };

  useEffect(() => {
    const interval = setInterval(() => {
      if (enableEnhancedMessaging) {
        // Use enhanced contextual messaging
        const context = {
          hasUpcomingDates: upcomingDatesCount > 0,
          profileCompletionCount,
          totalProfiles,
          timeOfYear: getTimeOfYear(),
          dayOfWeek: getDayOfWeek(),
          selectedProfileName
        };
        
        const newMessage = getContextualMessage(context);
        setCurrentMessage(newMessage);
      } else {
        // Fall back to legacy system
        setCurrentLegacyIndex((prev) => (prev + 1) % legacyMotivationalMessages.length);
      }
    }, 5000); // Change message every 5 seconds

    // Initialize first message
    if (enableEnhancedMessaging) {
      const context = {
        hasUpcomingDates: upcomingDatesCount > 0,
        profileCompletionCount,
        totalProfiles,
        timeOfYear: getTimeOfYear(),
        dayOfWeek: getDayOfWeek(),
        selectedProfileName
      };
      setCurrentMessage(getContextualMessage(context));
    }

    return () => clearInterval(interval);
  }, [enableEnhancedMessaging, upcomingDatesCount, profileCompletionCount, totalProfiles, selectedProfileName]);

  // Get category-based styling using our app's pomegranate color palette
  const getCategoryBorderColor = (category: string): string => {
    switch (category) {
      case 'discovery':
        return isDark ? 'border-primary-dark' : 'border-primary';
      case 'connection':
        return isDark ? 'border-accent-dark' : 'border-accent';
      case 'planning':
        return 'border-customDate dark:border-customDate-dark';
      case 'achievement':
        return 'border-birthday dark:border-birthday-dark';
      case 'seasonal':
        return 'border-holiday dark:border-holiday-dark';
      default:
        return 'border-primary dark:border-primary-dark';
    }
  };

  const getCategoryBackgroundColor = (category: string): string => {
    switch (category) {
      case 'discovery':
        return 'bg-primary/5 dark:bg-primary-dark/10';
      case 'connection':
        return 'bg-accent/5 dark:bg-accent-dark/10';
      case 'planning':
        return 'bg-customDate/5 dark:bg-customDate-dark/10';
      case 'achievement':
        return 'bg-birthday/5 dark:bg-birthday-dark/10';
      case 'seasonal':
        return 'bg-holiday/5 dark:bg-holiday-dark/10';
      default:
        return 'bg-primary/5 dark:bg-primary-dark/5';
    }
  };

  const getCategoryIconColor = (category: string): string => {
    const themeColors = isDark ? colors.dark : colors.light;
    
    switch (category) {
      case 'discovery':
        return themeColors.primary; // Rich pomegranate red
      case 'connection':
        return themeColors.accent; // Ruby/pinkish-red (perfect for relationships)
      case 'planning':
        return themeColors.customDate; // Deep magenta/purple (for planning)
      case 'achievement':
        return themeColors.birthday; // Celebratory accent color
      case 'seasonal':
        return themeColors.holiday; // Strong holiday red
      default:
        return themeColors.primary;
    }
  };

  // Legacy getGreeting function for fallback (keeping for safety)
  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 17) return 'Good afternoon';
    return 'Good evening';
  };

  return (
    <View className="mb-6">
      {/* Enhanced Greeting with Weather Icon */}
      <Animated.View
        entering={FadeIn.duration(600)}
        className="mb-3"
      >
        <View className="flex-row items-center space-x-2">
          {/* Weather/Time Icon */}
          <Animated.View
            entering={FadeIn.delay(200).duration(800)}
            className="mr-2"
          >
            <Feather 
              name={greetingData.icon as any} 
              size={20} 
              color={isDark ? colors.dark.primary : colors.light.primary} 
            />
          </Animated.View>
          
          {/* Enhanced Greeting Text */}
          <Animated.View
            entering={FadeIn.delay(100).duration(700)}
            className="flex-1"
          >
            <Text className="text-lg font-medium text-text-primary dark:text-text-primary-dark">
              {greetingData.text}
            </Text>
          </Animated.View>
        </View>
      </Animated.View>

      {/* Enhanced Motivational Message */}
      {enableEnhancedMessaging && currentMessage ? (
        <Animated.View
          key={`${currentMessage.category}-${currentMessage.text.slice(0, 20)}`}
          entering={FadeIn.duration(800)}
          exiting={FadeOut.duration(400)}
          className={`p-4 border-l-4 rounded-xl ${getCategoryBackgroundColor(currentMessage.category)} ${getCategoryBorderColor(currentMessage.category)}`}
        >
          <View className="flex-row items-start space-x-3">
            {/* Category Icon */}
            <Animated.View
              entering={FadeIn.delay(100).duration(600)}
              className="mt-1"
            >
              <Feather 
                name={currentMessage.icon as any} 
                size={16} 
                color={getCategoryIconColor(currentMessage.category)} 
              />
            </Animated.View>
            
            {/* Message Text */}
            <Animated.View 
              entering={FadeIn.delay(200).duration(700)}
              className="flex-1"
            >
              <Text className="text-base italic leading-relaxed text-text-secondary dark:text-text-secondary-dark">
                "{currentMessage.text}"
              </Text>
            </Animated.View>
          </View>
        </Animated.View>
      ) : (
        /* Legacy Motivational Message */
        <Animated.View
          key={currentLegacyIndex}
          entering={FadeIn.duration(800)}
          exiting={FadeOut.duration(400)}
          className="p-4 border-l-4 rounded-xl bg-primary/5 dark:bg-primary-dark/5 border-primary dark:border-primary-dark"
        >
          <Text className="text-base italic leading-relaxed text-text-secondary dark:text-text-secondary-dark">
            "{legacyMotivationalMessages[currentLegacyIndex]}"
          </Text>
        </Animated.View>
      )}
    </View>
  );
};

export default MotivationalHeader;
