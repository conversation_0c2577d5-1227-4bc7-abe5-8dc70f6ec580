import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import * as Google from 'expo-auth-session/providers/google';
import {
  onAuthStateChanged,
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signOut,
  User,
  AuthError,
  GoogleAuthProvider,
  signInWithCredential,
} from 'firebase/auth';
import { auth } from '../firebaseConfig';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { STORAGE_KEYS } from '../constants/storageKeys';

interface AuthContextType {
  user: User | null;
  isLoading: boolean; // True during initial check or any auth operation
  error: AuthError | null; // Holds the last auth error
  signIn: (email: string, pass: string) => Promise<void>;
  signUp: (email: string, pass: string) => Promise<void>;
  signOutUser: () => Promise<void>;
  signInWithGoogle: () => Promise<void>;
  clearError: () => void; // Function to clear the error state
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [initialLoading, setInitialLoading] = useState(true); // Tracks initial auth state check
  const [actionLoading, setActionLoading] = useState(false); // Tracks specific actions like sign-in/up/out
  const [googleLoading, setGoogleLoading] = useState(false); // Tracks Google Sign-in flow
  const [error, setError] = useState<AuthError | null>(null); // New error state

  const [request, response, promptAsync] = Google.useIdTokenAuthRequest({
    clientId: process.env.EXPO_PUBLIC_GOOGLE_WEB_CLIENT_ID,
    // Make sure these are configured for standalone builds
    // iosClientId: process.env.EXPO_PUBLIC_GOOGLE_IOS_CLIENT_ID,
    // androidClientId: process.env.EXPO_PUBLIC_GOOGLE_ANDROID_CLIENT_ID,
  });

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (currentUser) => {
      setUser(currentUser);
      
      // BUG FIX: Handle AsyncStorage operations AFTER auth state is confirmed
      if (currentUser) {
        // Only update onboarding status after successful authentication is confirmed
        try {
          await AsyncStorage.setItem(STORAGE_KEYS.HAS_COMPLETED_ONBOARDING, 'true');
        } catch (error) {
          console.error('Failed to update onboarding status:', error);
          // Don't throw - this shouldn't break the auth flow
        }
      }
      
      setInitialLoading(false); // Initial check is done
      setActionLoading(false); // Reset action loading on auth change
      setGoogleLoading(false); // Reset google loading on auth change
      // Optional: setError(null) here if you want auth changes to clear errors
    });
    return () => unsubscribe();
  }, []);

  useEffect(() => {
    const handleGoogleResponse = async () => {
      if (response?.type === 'success') {
        const { id_token } = response.params;
        const credential = GoogleAuthProvider.credential(id_token);
        // No need to set googleLoading true here, signInWithGoogle did it.
        // But we keep it true until signInWithCredential finishes or fails.
        try {
          setError(null); // Clear previous errors
          await signInWithCredential(auth, credential);
          // onAuthStateChanged will handle setting user, AsyncStorage & relevant loading states
          console.log('Google Sign-In Successful (Firebase)');
        } catch (err) {
          const authError = err as AuthError;
          console.error("Google Sign-In Firebase Error:", authError.code, authError.message);
          setError(authError);
          setGoogleLoading(false); // Ensure loading stops on Firebase error
        }
        // No finally needed here as onAuthStateChanged handles the success case loading
      } else if (response?.type === 'error') {
        console.error("Google Sign-In Expo Auth Error:", response.error);
        // You might want to create a pseudo-AuthError or custom error object here
        setError({ code: 'google/expo-auth-error', message: response.error?.message || 'Expo Google Auth failed.' } as AuthError);
        setGoogleLoading(false);
      } else if (response?.type === 'cancel') {
        console.log("Google Sign-In Cancelled by user.");
        setError(null); // User cancellation isn't usually an "error" to display
        setGoogleLoading(false);
      } else if (response !== null) {
         // Potentially handle 'dismiss' or other types if needed
         setGoogleLoading(false); // Assume flow finished if response is not null/success/error/cancel
      }
    };

    handleGoogleResponse();
  }, [response]);

  const clearError = () => {
    setError(null);
  };

  const signIn = async (email: string, pass: string): Promise<void> => {
    setActionLoading(true);
    setError(null);
    try {
      await signInWithEmailAndPassword(auth, email, pass);
      // onAuthStateChanged handles success and AsyncStorage update
    } catch (err) {
      const authError = err as AuthError;
      console.error("Sign In Error:", authError.code, authError.message);
      setError(authError);
      setActionLoading(false); // Stop loading on error
      throw authError;
    }
    // No finally needed: onAuthStateChanged sets loading false on success, catch block on error.
  };

  const signUp = async (email: string, pass: string): Promise<void> => {
    setActionLoading(true);
    setError(null);
    try {
      await createUserWithEmailAndPassword(auth, email, pass);
      // onAuthStateChanged handles success and AsyncStorage update
    } catch (err) {
      const authError = err as AuthError;
      console.error("Sign Up Error:", authError.code, authError.message);
      setError(authError);
      setActionLoading(false); // Stop loading on error
      throw authError;
    }
     // No finally needed
  };

  const signOutUser = async (): Promise<void> => {
    setActionLoading(true);
    setError(null);
    try {
      await signOut(auth);
      // onAuthStateChanged handles success (sets user to null)
    } catch (err) {
      const authError = err as AuthError;
      console.error("Sign Out Error:", authError.code, authError.message);
      setError(authError);
      setActionLoading(false); // Stop loading on error
      throw authError;
    }
     // No finally needed
  };

  const signInWithGoogle = async (): Promise<void> => {
    // Only start if another Google flow isn't already running
    if (googleLoading) return;
    setGoogleLoading(true);
    setError(null);
    try {
      await promptAsync();
      // Response useEffect will handle the result. googleLoading remains true until handled.
    } catch (err: any) { // Catching potential errors from promptAsync itself
      console.error("Google Sign-In Prompt Error:", err);
      setError({ code: 'google/prompt-error', message: err.message || 'Failed to start Google Sign-In.' } as AuthError);
      setGoogleLoading(false); // Ensure loading stops if prompt fails
    }
  };

  const value: AuthContextType = {
    user,
    // Combine all loading states for a single indicator
    isLoading: initialLoading || actionLoading || googleLoading,
    error,
    signIn,
    signUp,
    signOutUser,
    signInWithGoogle,
    clearError,
  };

  // Render null or a loading indicator until initial check is complete
  // This prevents rendering children components before auth state is known
  if (initialLoading) {
      // Optional: Render a global loading spinner here instead of null
      // Or pass initialLoading in value if components need to handle it
      return null;
  }


  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};