import React, { useState } from 'react';
import { Modal, View, Text, TouchableOpacity } from 'react-native';
import Input from '../ui/Input'; // Assuming Input is in components/ui
import Button from '../ui/Button'; // Assuming Button is in components/ui
import { GeneralNote } from '../../functions/src/types/firestore'; // Assuming GeneralNote type is here

interface AddGeneralNoteModalProps {
  isVisible: boolean;
  onClose: () => void;
  onSave: (noteText: string) => Promise<void>;
}

export const AddGeneralNoteModal: React.FC<AddGeneralNoteModalProps> = ({
  isVisible,
  onClose,
  onSave,
}) => {
  const [noteText, setNoteText] = useState('');
  const [error, setError] = useState('');

  const handleSave = () => {
    if (!noteText.trim()) {
      setError('Note text cannot be empty.');
      return;
    }
    setError('');
    onSave(noteText.trim());
    setNoteText(''); // Clear state on successful save
    onClose();
  };

  const handleCancel = () => {
    setNoteText(''); // Clear state on cancel
    setError(''); // Clear error on cancel
    onClose();
  };

  return (
    <Modal
      visible={isVisible}
      transparent={true}
      animationType="slide"
      onRequestClose={handleCancel}
    >
      <View style={{ backgroundColor: 'rgba(0, 0, 0, 0.5)' }} className="flex-1 justify-center items-center bg-black bg-opacity-50">
        <View className="p-6 w-full max-w-sm rounded-lg border border-border bg-card">
          <Text className="mb-4 text-xl font-bold">New Note</Text>

          {error ? <Text className="mb-2 text-error">{error}</Text> : null}

          <Input
            placeholder="e.g., Mentioned wanting to try pottery"
            value={noteText}
            onChangeText={setNoteText}
            multiline={true}
            className="mb-4 w-full rounded-md" // Added height for multiline
            textAlignVertical="top" // Align text to top in multiline
          />

          <View className="flex-row justify-end w-full">
            <Button  title="Cancel" onPress={handleCancel} className="mr-2 w-1/2 h-1/2" />
            <Button  title="Save Note" onPress={handleSave} className='w-1/2 h-1/2' />
          </View>
        </View>
      </View>
    </Modal>
  );
};
