import React, { useState } from 'react';
import { View, Text, TouchableOpacity, Animated } from 'react-native';
import { Feather } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';

interface AccordionProps {
  title: string;
  children: React.ReactNode;
  initialOpen?: boolean;
  icon?: React.ReactElement;
  description?: string;
  completionStatus?: 'empty' | 'partial' | 'complete';
  summary?: string;
  priority?: 'high' | 'medium' | 'low';
}

const Accordion: React.FC<AccordionProps> = ({
  title,
  children,
  initialOpen = false,
  icon,
  description,
  completionStatus = 'empty',
  summary,
  priority = 'medium'
}) => {
  const [isOpen, setIsOpen] = useState(initialOpen);
  const [rotateAnim] = useState(new Animated.Value(initialOpen ? 1 : 0));

  const toggleOpen = () => {
    const toValue = isOpen ? 0 : 1;
    setIsOpen(!isOpen);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    
    Animated.spring(rotateAnim, {
      toValue,
      tension: 200,
      friction: 8,
      useNativeDriver: true,
    }).start();
  };

  const rotateInterpolate = rotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '180deg'],
  });

  const getCompletionColor = () => {
    switch (completionStatus) {
      case 'complete': return 'text-green-600 dark:text-green-400';
      case 'partial': return 'text-amber-600 dark:text-amber-400';
      default: return 'text-gray-400 dark:text-gray-500';
    }
  };

  const getCompletionIcon = () => {
    switch (completionStatus) {
      case 'complete': return 'check-circle';
      case 'partial': return 'clock';
      default: return 'circle';
    }
  };

  const getPriorityIndicator = () => {
    if (priority === 'high') {
      return <View className="w-2 h-2 bg-primary-500 rounded-full ml-2" />;
    }
    return null;
  };

  return (
    <View className="overflow-hidden border rounded-xl border-border dark:border-border-dark bg-card dark:bg-card-dark shadow-sm mb-6">
      <TouchableOpacity
        className="flex-row items-center justify-between p-6 bg-gradient-to-r from-transparent to-primary-500/5"
        onPress={toggleOpen}
        accessibilityRole="button"
        accessibilityState={{ expanded: isOpen }}
        activeOpacity={0.7}
      >
        <View className="flex-row items-center flex-1">
          {/* Section Icon */}
          <View className="mr-4 p-3 bg-primary-50 dark:bg-primary-500/10 rounded-lg">
            {icon}
          </View>
          
          <View className="flex-1">
            {/* Title and Priority */}
            <View className="flex-row items-center mb-1">
              <Text className="text-lg font-semibold text-text-primary dark:text-text-primary-dark">
                {title}
              </Text>
              {getPriorityIndicator()}
            </View>
            
            {/* Description */}
            {description && (
              <Text className="text-sm text-text-secondary dark:text-text-secondary-dark mb-2">
                {description}
              </Text>
            )}
            
            {/* Summary when collapsed */}
            {!isOpen && summary && (
              <Text className="text-sm text-primary-600 dark:text-primary-400 italic">
                {summary}
              </Text>
            )}
          </View>
        </View>

        {/* Completion Status and Chevron */}
        <View className="flex-row items-center ml-4">
          <Feather
            name={getCompletionIcon()}
            size={20}
            className={getCompletionColor()}
          />
          <Animated.View
            style={{ transform: [{ rotate: rotateInterpolate }] }}
            className="ml-3"
          >
            <Feather
              name="chevron-down"
              size={24}
              color="#6B7280"
            />
          </Animated.View>
        </View>
      </TouchableOpacity>

      {isOpen && (
        <Animated.View 
          className="px-6 pb-6"
          style={{
            opacity: rotateAnim,
          }}
        >
          {children}
        </Animated.View>
      )}
    </View>
  );
};

export default Accordion;