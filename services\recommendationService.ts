import { getFunctions, httpsCallable, HttpsCallableResult } from 'firebase/functions';
import { getFirestore, collection, addDoc, serverTimestamp, query, where, orderBy, getDocs, doc, deleteDoc, updateDoc } from 'firebase/firestore';
import { app } from '../firebaseConfig';

interface RecommendationFeedbackData {
  userId: string;
  profileId: string;
  recommendationId: string;
  feedbackType: 'like' | 'dislike';
  recommendationDetails?: {
    name: string;
    description: string;
    [key: string]: any;
  };
}

export interface GiftRecommendation {
  id: string;
  name: string;
  description: string;
  priceRange: string;
  categories: string[];
  
  // Enhanced AI reasoning & confidence
  reasoning: {
    why: string;
    confidence: number; // 0-100
    personalizedMatch: string;
    profileConnections: string[];
  };
  
  // Gift characteristics
  attributes: {
    giftType: 'physical' | 'experiential' | 'digital' | 'subscription';
    thoughtfulness: 'practical' | 'sentimental' | 'creative' | 'luxurious';
    effort: 'low' | 'medium' | 'high';
    surpriseFactor: 'expected' | 'delightful' | 'unexpected';
  };
  
  // Emotional & social context
  context: {
    emotionalTone: string;
    anticipatedReaction: string;
    relationshipFit: string;
    conversationPotential: string;
  };
  
  // Presentation & practical
  practical: {
    presentationIdea: string;
    giftingTips: string;
    complementaryIdeas: string[];
    usageScenario: string;
  };
  
  // Visual hints for UI
  visual: {
    colorTheme: string;
    iconSuggestion: string;
    emoji: string;
    oneWordEssence: string;
  };
}

interface RecommendationsObject {
  recommendations?: GiftRecommendation[];
  timestamp: any; // Firestore Timestamp or serverTimestamp
}

interface DataObject {
  data?: GiftRecommendation[];
}

interface SuccessResponse {
  success: boolean;
  recommendations?: GiftRecommendation[];
}

type RecommendationResponse =
  | GiftRecommendation[]
  | RecommendationsObject
  | DataObject
  | SuccessResponse;


export interface FeedbackEntry {
  id: string;
  userId: string;
  profileId: string;
  recommendationId: string;
  feedbackType: 'like' | 'dislike';
  recommendationDetails?: {
    name: string;
    description: string;
    [key: string]: any;
  };
  timestamp: any; // Firestore Timestamp or serverTimestamp
}

const functions = getFunctions(app);

// Helper to ensure a consistent ID for recommendations and provide defaults for enhanced data
const ensureConsistentId = (rec: any): GiftRecommendation => {
  return {
    ...rec,
    id: rec.id || rec.recommendationId || rec.name, // Fallback chain to ensure an ID
    
    // Ensure enhanced data exists with fallbacks
    reasoning: rec.reasoning || {
      why: "AI-generated recommendation",
      confidence: 75,
      personalizedMatch: "Based on available preferences",
      profileConnections: []
    },
    
    attributes: rec.attributes || {
      giftType: 'physical',
      thoughtfulness: 'practical',
      effort: 'medium',
      surpriseFactor: 'expected'
    },
    
    context: rec.context || {
      emotionalTone: "thoughtful",
      anticipatedReaction: "positive",
      relationshipFit: "appropriate",
      conversationPotential: "good"
    },
    
    practical: rec.practical || {
      presentationIdea: "Wrap thoughtfully",
      giftingTips: "Include a personal note",
      complementaryIdeas: [],
      usageScenario: "general use"
    },
    
    visual: rec.visual || {
      colorTheme: "neutral",
      iconSuggestion: "gift",
      emoji: "🎁",
      oneWordEssence: "thoughtful"
    }
  };
};

export const getProfileFeedback = async (profileId: string): Promise<FeedbackEntry[]> => {
  try {
    const db = getFirestore(app);
    const feedbackCollection = collection(db, 'recommendation_feedback');
    
    // Create query to filter by profileId and order by timestamp descending
    const feedbackQuery = query(
      feedbackCollection,
      where('profileId', '==', profileId),
      orderBy('timestamp', 'desc')
    );

    const querySnapshot = await getDocs(feedbackQuery);
    
    // Map each document to include the document id
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...(doc.data() as Omit<FeedbackEntry, 'id'>)
    }));
  } catch (error) {
    console.error('Error fetching profile feedback:', error);
    throw error;
  }
};

export const deleteRecommendationFeedback = async (feedbackId: string): Promise<boolean> => {
  try {
    const db = getFirestore(app);
    const feedbackDoc = doc(db, 'recommendation_feedback', feedbackId);
    await deleteDoc(feedbackDoc);
    return true;
  } catch (error) {
    console.error('Error deleting recommendation feedback:', error);
    return false;
  }
};

export const processRecommendationFeedback = async (
  feedbackData: Omit<RecommendationFeedbackData, 'recommendationDetails'> & { recommendationDetails?: any }
): Promise<{ status: 'created' | 'updated' | 'deleted'; docId: string }> => {
  const { userId, profileId, recommendationId, feedbackType, recommendationDetails } = feedbackData;

  if (!userId || !profileId || !recommendationId) {
    throw new Error('Missing required fields for feedback');
  }

  const db = getFirestore(app);
  const feedbackCollection = collection(db, 'recommendation_feedback');

  const q = query(
    feedbackCollection,
    where('profileId', '==', profileId),
    where('recommendationId', '==', recommendationId)
  );

  const querySnapshot = await getDocs(q);

  if (querySnapshot.empty) {
    // Create new feedback
    const docData = {
      userId,
      profileId,
      recommendationId,
      feedbackType,
      recommendationDetails: recommendationDetails || null,
      timestamp: serverTimestamp(),
    };
    const docRef = await addDoc(feedbackCollection, docData);
    return { status: 'created', docId: docRef.id };
  } else {
    // Existing feedback found
    const existingDoc = querySnapshot.docs[0];
    const existingFeedback = existingDoc.data();

    if (existingFeedback.feedbackType === feedbackType) {
      // Same type, so delete
      await deleteDoc(existingDoc.ref);
      return { status: 'deleted', docId: existingDoc.id };
    } else {
      // Different type, so update
      await updateDoc(existingDoc.ref, {
        feedbackType: feedbackType,
        timestamp: serverTimestamp(),
      });
      return { status: 'updated', docId: existingDoc.id };
    }
  }
};

export const fetchGiftRecommendations = async (
  profileId: string,
  occasion?: string,
  date?: string
): Promise<GiftRecommendation[]> => {
  try {
    const getRecommendations = httpsCallable<
      { profileId: string; occasion?: string; date?: string },
      RecommendationResponse
    >(
      functions,
      'getGiftRecommendations'
    );
    const result = await getRecommendations({ profileId, occasion, date });
    console.log('Recommendations response:', result.data);

    // Handle different response formats with proper type guards
    if (Array.isArray(result.data)) {
      return result.data.map(ensureConsistentId);
    }
    
    const response = result.data as Record<string, any>;
    if (response?.recommendations) {
      return response.recommendations.map(ensureConsistentId);
    }
    if (response?.data) {
      return response.data.map(ensureConsistentId);
    }
    
    return [];
  } catch (error) {
    console.error('Error calling getGiftRecommendations:', error);
    throw error;
  }
};

export const callGetGenericRecommendations = async (
  query: string
): Promise<GiftRecommendation[]> => {
  try {
    const getRecommendations = httpsCallable<
      { query: string }, // Only pass query for generic search
      RecommendationResponse
    >(
      functions,
      'getGiftRecommendations'
    );
    const result = await getRecommendations({ query });
    console.log('Generic Recommendations response:', result.data);

    // Handle different response formats with proper type guards
    if (Array.isArray(result.data)) {
      return result.data.map(ensureConsistentId);
    }
    
    const response = result.data as Record<string, any>;
    if (response?.recommendations) {
      return response.recommendations.map(ensureConsistentId);
    }
    if (response?.data) {
      return response.data.map(ensureConsistentId);
    }
    
    return [];
  } catch (error) {
    console.error('Error calling getGiftRecommendations (generic):', error);
    throw error;
  }
};

export const callGetProfileAwareRecommendations = async (
  query: string,
  profileId: string,
  occasion?: string,
  date?: string
): Promise<GiftRecommendation[]> => {
  try {
    const getRecommendations = httpsCallable<
      { query: string; profileId: string; occasion?: string; date?: string },
      RecommendationResponse
    >(
      functions,
      'getGiftRecommendations'
    );
    const result = await getRecommendations({ query, profileId, occasion, date });
    console.log('Profile-aware Recommendations response:', result.data);

    // Handle different response formats with proper type guards
    if (Array.isArray(result.data)) {
      return result.data.map(ensureConsistentId);
    }
    
    const response = result.data as Record<string, any>;
    if (response?.recommendations) {
      return response.recommendations.map(ensureConsistentId);
    }
    if (response?.data) {
      return response.data.map(ensureConsistentId);
    }
    
    return [];
  } catch (error) {
    console.error('Error calling getGiftRecommendations (profile-aware):', error);
    throw error;
  }
};
  
export const saveRecommendationFeedback = async (
  feedbackData: RecommendationFeedbackData
): Promise<boolean> => {
  try {
    // Validate required fields
    if (!feedbackData.userId || !feedbackData.profileId || !feedbackData.recommendationId) {
      console.error('Missing required fields for feedback:', feedbackData);
      return false;
    }

    const db = getFirestore(app);
    const feedbackCollection = collection(db, 'recommendation_feedback');
    
    // Create document with explicit field mapping
    const docData = {
      userId: feedbackData.userId,
      profileId: feedbackData.profileId,
      recommendationId: feedbackData.recommendationId,
      feedbackType: feedbackData.feedbackType,
      recommendationDetails: feedbackData.recommendationDetails || null,
      timestamp: serverTimestamp()
    };

    console.log('Saving feedback with data:', docData);
    await addDoc(feedbackCollection, docData);
    
    return true;
  } catch (error) {
    console.error('Error saving recommendation feedback:', error);
    return false;
  }
};