// components/home/<USER>/GiftRecommendationCard.tsx
import React, { useState } from 'react';
import {
  View,
  Text,
  Pressable,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import Animated, {
  SlideInRight,
  useSharedValue,
  useAnimatedStyle,
  withSequence,
  withTiming,
  runOnJS,
} from 'react-native-reanimated';
import { Feather } from '@expo/vector-icons';
import { useColorScheme } from 'nativewind';
import * as Haptics from 'expo-haptics';
import {
  GiftRecommendation,
  FeedbackEntry,
} from '../../../services/recommendationService';
import Card from '../../ui/Card';
import { useGiftActions } from '../../../hooks/useGiftActions';

const AnimatedCard = Animated.createAnimatedComponent(Card);

interface GiftRecommendationCardProps {
  item: GiftRecommendation;
  index: number;
  feedback: FeedbackEntry | undefined;
  onFeedback: (
    item: GiftRecommendation,
    feedbackType: 'like' | 'dislike'
  ) => Promise<void>;
  screenWidth: number;
}

const GiftRecommendationCard: React.FC<GiftRecommendationCardProps> = ({
  item,
  index,
  feedback,
  onFeedback,
  screenWidth,
}) => {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const [showFullDescription, setShowFullDescription] = useState(false);
  const { handleShare } = useGiftActions();

  // Animation values
  const heartScale = useSharedValue(1);

  // Animation handlers
  const animateHeart = (callback: () => void) => {
    heartScale.value = withSequence(
      withTiming(1.2, { duration: 100 }),
      withTiming(1, { duration: 100 }, (finished) => {
        if (finished) {
          runOnJS(callback)();
        }
      })
    );
  };

  // Animated styles
  const heartAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: heartScale.value }],
  }));

  // Enhanced feedback handlers
  const handleLikeWithAnimation = (item: GiftRecommendation) => {
    // Trigger haptics immediately on UI thread
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);

    // Animate heart and then call feedback
    animateHeart(() => {
      onFeedback(item, 'like');
    });
  };

  const handleDislikeAction = (item: GiftRecommendation) => {
    // Trigger haptics immediately on UI thread
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    onFeedback(item, 'dislike');
  };

  // Create stable callback refs
  const handleLikeRef = React.useCallback(() => {
    handleLikeWithAnimation(item);
  }, [item]);

  const handleDislikeRef = React.useCallback(() => {
    handleDislikeAction(item);
  }, [item]);

  const getCardWidth = () => {
    // For pagination, use consistent full width with padding
    return screenWidth - 40; // Leave 20px padding on each side
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 90) return isDark ? '#22C55E' : '#16A34A'; // Green
    if (confidence >= 75) return isDark ? '#E87900' : '#F59E0B'; // Orange
    if (confidence >= 60) return isDark ? '#E5355F' : '#EF4444'; // Red-ish
    return isDark ? '#6B7280' : '#9CA3AF'; // Gray
  };

  const getThoughtfulnessEmoji = (thoughtfulness: string) => {
    switch (thoughtfulness) {
      case 'practical':
        return '🔧';
      case 'sentimental':
        return '💝';
      case 'creative':
        return '🎨';
      case 'luxurious':
        return '✨';
      default:
        return '🎁';
    }
  };

  const getSurpriseIcon = (surpriseFactor: string) => {
    switch (surpriseFactor) {
      case 'expected':
        return 'check-circle';
      case 'delightful':
        return 'smile';
      case 'unexpected':
        return 'zap';
      default:
        return 'gift';
    }
  };

  const truncateDescription = (text: string, maxLength: number = 120) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  // Big card (only card type)
  return (
    <View
      style={{
        width: screenWidth,
        justifyContent: 'center',
        alignItems: 'center',
      }}
    >
      <AnimatedCard
        entering={SlideInRight.delay(index * 150).springify()}
        className="overflow-hidden mb-4 rounded-xl border shadow-sm bg-card dark:bg-card-dark border-border dark:border-border-dark"
        style={[{ width: getCardWidth(), minHeight: 520 }]}
      >
        <View className="flex-1 p-6">
          {/* Top Content Section */}
          <View className="flex-1">
            {/* Header Section */}
            <View className="flex-row justify-between items-start mb-4">
              <View className="flex-row flex-1 items-center">
                <Text className="mr-4 text-4xl">{item.visual.emoji}</Text>
                <View className="flex-1">
                  <Text className="text-xl font-bold text-primary dark:text-primary-dark">
                    {item.name}
                  </Text>
                  <View className="flex-row items-center mt-1">
                    <View
                      className="mr-2 w-4 h-4 rounded-full"
                      style={{
                        backgroundColor: getConfidenceColor(
                          item.reasoning.confidence
                        ),
                      }}
                    />
                    <Text className="text-sm font-medium text-text-secondary dark:text-text-secondary-dark">
                      {item.reasoning.confidence}% Match
                    </Text>
                  </View>
                </View>
              </View>
            </View>

            {/* AI Reasoning */}
            <View className="p-4 mb-4 rounded-lg border bg-background dark:bg-background-dark border-primary/20 dark:border-primary-dark/20">
              <View className="flex-row items-center mb-2">
                <Text className="text-lg">🎯</Text>
                <Text className="ml-2 text-sm font-medium text-primary dark:text-primary-dark">
                  Perfect match because
                </Text>
              </View>
              <Text className="text-sm leading-relaxed text-text-secondary dark:text-text-secondary-dark">
                {item.reasoning.personalizedMatch}
              </Text>
            </View>

            {/* Price and Details */}
            <View className="flex-row justify-between items-center mb-4">
              <Text className="text-lg font-medium text-text-secondary dark:text-text-secondary-dark">
                {item.priceRange}
              </Text>
              <View className="flex-row items-center">
                <Text className="mr-2 text-sm text-text-secondary dark:text-text-secondary-dark">
                  {item.attributes.effort} effort
                </Text>
                <Feather
                  name={getSurpriseIcon(item.attributes.surpriseFactor)}
                  size={16}
                  color={isDark ? '#C70039' : '#A3002B'}
                />
              </View>
            </View>

            {/* Description */}
            <View className="mb-4" style={{ minHeight: 60 }}>
              <Text
                className="text-base text-text-secondary dark:text-text-secondary-dark"
                numberOfLines={showFullDescription ? undefined : 3}
              >
                {item.description}
              </Text>
              {item.description.length > 120 && (
                <TouchableOpacity
                  onPress={() => setShowFullDescription(!showFullDescription)}
                  className="mt-1"
                >
                  <Text className="text-sm font-medium text-primary dark:text-primary-dark">
                    {showFullDescription ? 'Show less' : 'Read more'}
                  </Text>
                </TouchableOpacity>
              )}
            </View>

            {/* Categories */}
            <View className="mb-4" style={{ minHeight: 40 }}>
              <View className="flex-row flex-wrap">
                {item.categories.slice(0, 4).map((category, i) => (
                  <View
                    key={i}
                    className="px-3 py-1 mr-2 mb-2 rounded-full bg-accent/10 dark:bg-accent-dark/10"
                  >
                    <Text className="text-xs font-medium text-accent dark:text-accent-dark">
                      {category}
                    </Text>
                  </View>
                ))}
                {item.categories.length > 4 && (
                  <View className="px-3 py-1 mr-2 mb-2 rounded-full bg-accent/10 dark:bg-accent-dark/10">
                    <Text className="text-xs font-medium text-accent dark:text-accent-dark">
                      +{item.categories.length - 4} more
                    </Text>
                  </View>
                )}
              </View>
            </View>
          </View>

          {/* Bottom Actions Section */}
          <View>
            {/* Primary Actions */}
            <View className="flex-row gap-3 mb-3">
              <TouchableOpacity
                className={`flex-1 py-3 px-4 rounded-lg flex-row items-center justify-center ${
                  feedback?.feedbackType === 'like'
                    ? 'bg-primary/20 dark:bg-primary-dark/20'
                    : 'bg-primary dark:bg-primary-dark'
                }`}
                onPress={handleLikeRef}
              >
                <Animated.View style={heartAnimatedStyle}>
                  <Feather
                    name="heart"
                    size={20}
                    color={
                      feedback?.feedbackType === 'like'
                        ? isDark
                          ? '#C70039'
                          : '#A3002B'
                        : '#FFFFFF'
                    }
                  />
                </Animated.View>
                <Text
                  className={`ml-2 text-base font-semibold ${
                    feedback?.feedbackType === 'like'
                      ? 'text-primary dark:text-primary-dark'
                      : 'text-white'
                  }`}
                >
                  Love It!
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                className={`flex-1 py-3 px-4 rounded-lg flex-row items-center justify-center ${
                  feedback?.feedbackType === 'dislike'
                    ? 'bg-error/20 dark:bg-error-dark/20'
                    : 'bg-error/10 dark:bg-error-dark/10'
                }`}
                onPress={handleDislikeRef}
              >
                <Feather
                  name="thumbs-down"
                  size={20}
                  color={isDark ? '#B20021' : '#D90429'}
                />
                <Text className="ml-2 text-base font-semibold text-error dark:text-error-dark">
                  Not for them
                </Text>
              </TouchableOpacity>
            </View>

            {/* Secondary Actions */}
            <View className="flex-row justify-center items-center pt-3 border-t border-border/30 dark:border-border-dark/30">
              <TouchableOpacity
                className="px-3 py-2 rounded-full bg-background dark:bg-background-dark"
                onPress={() => handleShare(item)}
              >
                <Feather
                  name="share"
                  size={16}
                  color={isDark ? '#C70039' : '#A3002B'}
                />
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </AnimatedCard>
    </View>
  );
};

export default GiftRecommendationCard;
