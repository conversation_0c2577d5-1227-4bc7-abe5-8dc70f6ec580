import { PastGiftGiven } from '../types/firestore';
import { Timestamp } from 'firebase/firestore';
import { format, differenceInDays, startOfYear, endOfYear, eachDayOfInterval, isSameDay, subDays, isAfter, isBefore } from 'date-fns';

// Chart data interfaces
export interface DailyGiftData {
  date: string; // YYYY-MM-DD format
  giftCount: number;
  gifts: PastGiftGiven[];
  successScore: number; // 0-100 based on ratings/reactions
  totalValue?: number;
  categories: string[];
  avgRating?: number;
  hasHighSuccess: boolean; // For color intensity
}

export interface WeeklyGiftData {
  weekNumber: number;
  year: number;
  days: DailyGiftData[];
  totalGifts: number;
  avgSuccessScore: number;
  intensity: number; // 0-4 for color levels
}

export interface GiftContributionsData {
  year: number;
  weeks: WeeklyGiftData[];
  totalGifts: number;
  avgSuccessScore: number;
  achievements: AchievementData;
  stats: ContributionStats;
}

export interface AchievementData {
  currentStreak: number;
  longestStreak: number;
  totalGifts: number;
  perfectWeeks: number; // Weeks with 4+ high-success gifts
  bestMonth: string;
  streakStart?: Date;
  streakEnd?: Date;
  badges: string[];
}

export interface ContributionStats {
  thisMonth: number;
  thisYear: number;
  lastYearComparison: number; // Percentage change
  avgGiftsPerMonth: number;
  mostActiveDay: string;
  successRate: number; // Percentage of gifts rated 4+ stars
  favoriteCategory: string;
}

// Utility functions
export const timestampToDate = (timestamp: Timestamp | null | undefined | Date): Date => {
  if (!timestamp) return new Date();
  if (timestamp instanceof Date) return timestamp;
  if (timestamp instanceof Timestamp) return timestamp.toDate();
  return new Date();
};

export const calculateSuccessScore = (gift: PastGiftGiven): number => {
  let score = 50; // Base score of 50

  // Rating contributes 0-40 points
  if (gift.rating) {
    score += (gift.rating - 1) * 10; // 1-star = 0 extra, 5-star = 40 extra
  }

  // Mood contributes 0-20 points
  if (gift.recipientMood) {
    const moodScores = {
      "ecstatic": 20,
      "delighted": 15,
      "happy": 10,
      "satisfied": 5,
      "neutral": 0,
      "disappointed": -20
    };
    score += moodScores[gift.recipientMood] || 0;
  }

  // Still used adds 10 points
  if (gift.stillUsed === true) {
    score += 10;
  } else if (gift.stillUsed === false) {
    score -= 10;
  }

  // Would give again adds 10 points
  if (gift.wouldGiveAgain === true) {
    score += 10;
  } else if (gift.wouldGiveAgain === false) {
    score -= 10;
  }

  // Loved adds 10 points
  if (gift.loved === true) {
    score += 10;
  } else if (gift.loved === false) {
    score -= 10;
  }

  // BACKWARD COMPATIBILITY: Handle old format gifts with just 'reaction' field
  if (!gift.rating && !gift.recipientMood && !gift.loved && gift.reaction) {
    const reaction = gift.reaction.toLowerCase();
    
    if (reaction.includes('loved') || reaction.includes('adored') || reaction.includes('amazing')) {
      score += 25; // High positive reaction
    } else if (reaction.includes('liked') || reaction.includes('enjoyed') || reaction.includes('happy')) {
      score += 15; // Positive reaction
    } else if (reaction.includes('okay') || reaction.includes('fine') || reaction.includes('decent')) {
      score += 5; // Neutral-positive reaction
    } else if (reaction.includes('disliked') || reaction.includes('hated') || reaction.includes('disappointed')) {
      score -= 15; // Negative reaction
    } else {
      score += 10; // Default for any reaction (better than no feedback)
    }
  }

  // Ensure score is between 0 and 100
  return Math.max(0, Math.min(100, score));
};

export const aggregateGiftsByDay = (gifts: PastGiftGiven[]): Map<string, DailyGiftData> => {
  const dailyMap = new Map<string, DailyGiftData>();

  gifts.forEach(gift => {
    const giftDate = timestampToDate(gift.date);
    const dateKey = format(giftDate, 'yyyy-MM-dd');

    if (!dailyMap.has(dateKey)) {
      dailyMap.set(dateKey, {
        date: dateKey,
        giftCount: 0,
        gifts: [],
        successScore: 0,
        totalValue: 0,
        categories: [],
        avgRating: 0,
        hasHighSuccess: false
      });
    }

    const dayData = dailyMap.get(dateKey)!;
    dayData.gifts.push(gift);
    dayData.giftCount += 1;
    
    // Calculate aggregated success score
    const giftSuccessScore = calculateSuccessScore(gift);
    dayData.successScore = (dayData.successScore * (dayData.giftCount - 1) + giftSuccessScore) / dayData.giftCount;
    
    // Add to total value if available
    if (gift.estimatedCost) {
      dayData.totalValue = (dayData.totalValue || 0) + gift.estimatedCost;
    }
    
    // Add categories
    if (gift.category && !dayData.categories.includes(gift.category)) {
      dayData.categories.push(gift.category);
    }
    
    // Calculate average rating
    const ratingsTotal = dayData.gifts.filter(g => g.rating).reduce((sum, g) => sum + (g.rating || 0), 0);
    const ratingsCount = dayData.gifts.filter(g => g.rating).length;
    dayData.avgRating = ratingsCount > 0 ? ratingsTotal / ratingsCount : 0;
    
    // Determine if this day has high success (80+ score or 4+ rating)
    dayData.hasHighSuccess = dayData.successScore >= 80 || dayData.avgRating >= 4;
  });

  return dailyMap;
};

export const getIntensityLevel = (giftCount: number, successScore: number): number => {
  // Intensity levels 0-4 for chart colors
  if (giftCount === 0) return 0;
  if (giftCount === 1 && successScore < 60) return 1;
  if (giftCount === 1 && successScore >= 60) return 2;
  if (giftCount >= 2 && successScore < 80) return 2;
  if (giftCount >= 2 && successScore >= 80) return 3;
  if (giftCount >= 4 && successScore >= 85) return 4;
  return Math.min(4, giftCount);
};

export const calculateStreaks = (dailyData: Map<string, DailyGiftData>): { current: number; longest: number; streakStart?: Date; streakEnd?: Date } => {
  const sortedDates = Array.from(dailyData.keys()).sort();
  let currentStreak = 0;
  let longestStreak = 0;
  let currentStreakStart: Date | undefined;
  let longestStreakStart: Date | undefined;
  let longestStreakEnd: Date | undefined;

  // Start from today and go backwards for current streak
  const today = new Date();
  let checkDate = today;
  let foundToday = false;

  // Check if there's a gift today or yesterday (allow for some flexibility)
  for (let i = 0; i < 2; i++) {
    const dateKey = format(subDays(today, i), 'yyyy-MM-dd');
    if (dailyData.has(dateKey) && dailyData.get(dateKey)!.giftCount > 0) {
      foundToday = true;
      currentStreakStart = subDays(today, i);
      break;
    }
  }

  if (foundToday && currentStreakStart) {
    // Count backwards for current streak
    checkDate = currentStreakStart;
    while (true) {
      const dateKey = format(checkDate, 'yyyy-MM-dd');
      const dayData = dailyData.get(dateKey);
      
      if (dayData && dayData.giftCount > 0) {
        currentStreak++;
        checkDate = subDays(checkDate, 1);
      } else {
        break;
      }
    }
  }

  // Calculate longest streak from all data
  let tempStreak = 0;
  let tempStart: Date | undefined;

  sortedDates.forEach(dateString => {
    const dayData = dailyData.get(dateString)!;
    if (dayData.giftCount > 0) {
      if (tempStreak === 0) {
        tempStart = new Date(dateString);
      }
      tempStreak++;
      
      if (tempStreak > longestStreak) {
        longestStreak = tempStreak;
        longestStreakStart = tempStart;
        longestStreakEnd = new Date(dateString);
      }
    } else {
      tempStreak = 0;
      tempStart = undefined;
    }
  });

  return {
    current: currentStreak,
    longest: longestStreak,
    streakStart: longestStreakStart,
    streakEnd: longestStreakEnd
  };
};

export const generateContributionsData = (gifts: PastGiftGiven[], year: number = new Date().getFullYear()): GiftContributionsData => {
  const yearStart = startOfYear(new Date(year, 0, 1));
  const yearEnd = endOfYear(new Date(year, 0, 1));
  
  // Filter gifts for the specified year
  const yearGifts = gifts.filter(gift => {
    const giftDate = timestampToDate(gift.date);
    return giftDate >= yearStart && giftDate <= yearEnd;
  });

  // Aggregate by day
  const dailyData = aggregateGiftsByDay(yearGifts);

  // Generate all days of the year
  const allDays = eachDayOfInterval({ start: yearStart, end: yearEnd });
  
  // Ensure we have data for every day (even if empty)
  allDays.forEach(day => {
    const dateKey = format(day, 'yyyy-MM-dd');
    if (!dailyData.has(dateKey)) {
      dailyData.set(dateKey, {
        date: dateKey,
        giftCount: 0,
        gifts: [],
        successScore: 0,
        totalValue: 0,
        categories: [],
        avgRating: 0,
        hasHighSuccess: false
      });
    }
  });

  // Group into weeks (GitHub style: 53 weeks)
  const weeks: WeeklyGiftData[] = [];
  let currentWeek: DailyGiftData[] = [];
  let weekNumber = 1;

  allDays.forEach((day, index) => {
    const dateKey = format(day, 'yyyy-MM-dd');
    const dayData = dailyData.get(dateKey)!;
    currentWeek.push(dayData);

    // If it's Sunday (end of week) or the last day of year
    if (day.getDay() === 0 || index === allDays.length - 1) {
      const totalGifts = currentWeek.reduce((sum, d) => sum + d.giftCount, 0);
      const avgSuccessScore = currentWeek.reduce((sum, d) => sum + d.successScore, 0) / currentWeek.length;
      const maxGiftsInDay = Math.max(...currentWeek.map(d => d.giftCount));
      
      weeks.push({
        weekNumber,
        year,
        days: [...currentWeek],
        totalGifts,
        avgSuccessScore,
        intensity: getIntensityLevel(maxGiftsInDay, avgSuccessScore)
      });

      currentWeek = [];
      weekNumber++;
    }
  });

  // Calculate achievements and stats
  const streakData = calculateStreaks(dailyData);
  const totalGifts = yearGifts.length;
  const perfectWeeks = weeks.filter(week => week.intensity >= 3).length;
  
  const achievements: AchievementData = {
    currentStreak: streakData.current,
    longestStreak: streakData.longest,
    totalGifts,
    perfectWeeks,
    bestMonth: '', // TODO: Calculate best month
    streakStart: streakData.streakStart,
    streakEnd: streakData.streakEnd,
    badges: [] // TODO: Calculate badges
  };

  const stats: ContributionStats = {
    thisMonth: 0, // TODO: Calculate this month
    thisYear: totalGifts,
    lastYearComparison: 0, // TODO: Calculate comparison
    avgGiftsPerMonth: totalGifts / 12,
    mostActiveDay: '', // TODO: Calculate most active day
    successRate: 0, // TODO: Calculate success rate
    favoriteCategory: '' // TODO: Calculate favorite category
  };

  return {
    year,
    weeks,
    totalGifts,
    avgSuccessScore: yearGifts.length > 0 ? yearGifts.reduce((sum, gift) => sum + calculateSuccessScore(gift), 0) / yearGifts.length : 0,
    achievements,
    stats
  };
}; 