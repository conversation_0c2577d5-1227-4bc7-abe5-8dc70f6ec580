import React from 'react';
import { View, TouchableOpacity } from 'react-native';
import Animated, { useAnimatedStyle, withSpring, interpolateColor } from 'react-native-reanimated';
import { useColorScheme } from 'nativewind';

interface PaginationDotsProps {
  data: any[];
  currentIndex: number;
  onDotPress: (index: number) => void;
  isLoading?: boolean;
}

const PaginationDots: React.FC<PaginationDotsProps> = ({ 
  data, 
  currentIndex, 
  onDotPress,
  isLoading = false
}) => {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  // Don't show dots if there's only one item or no data
  if (!data || data.length <= 1) {
    return null;
  }

  // Filter out skeleton loading items for dot count
  const actualData = data.filter(item => !item.isLoading);
  
  // Don't show dots if there are no actual recommendations yet
  if (actualData.length <= 1) {
    return null;
  }

  return (
    <View className="flex-row justify-center items-center mt-4 mb-2">
      {actualData.map((_, index) => {
        const isActive = index === currentIndex;
        
        const animatedStyle = useAnimatedStyle(() => {
          const scale = withSpring(isActive ? 1.2 : 0.8, {
            damping: 15,
            stiffness: 150,
          });
          
          const backgroundColor = interpolateColor(
            isActive ? 1 : 0,
            [0, 1],
            [
              isDark ? 'rgba(255, 255, 255, 0.3)' : 'rgba(163, 0, 43, 0.3)',
              isDark ? '#C70039' : '#A3002B'
            ]
          );

          return {
            transform: [{ scale }],
            backgroundColor,
          };
        });

        return (
          <TouchableOpacity
            key={index}
            onPress={() => onDotPress(index)}
            disabled={isLoading}
            activeOpacity={0.7}
            className="mx-1"
          >
            <Animated.View
              style={[
                {
                  width: 8,
                  height: 8,
                  borderRadius: 4,
                },
                animatedStyle,
              ]}
            />
          </TouchableOpacity>
        );
      })}
    </View>
  );
};

export default PaginationDots; 