import { Timestamp } from 'firebase/firestore';

export interface UserSettings {
  userId: string; // Or implicitly the document ID
  reminders?: {
    birthdays?: boolean;
    anniversaries?: boolean;
    holidays?: boolean;
    customDates?: boolean;
  };
  // Add other settings like budget later
  updatedAt?: Timestamp;
}

export interface SOPreferences {
  favoriteColor?: string;
  preferredStyle?: string;
  favoriteBrands?: string[]; // Assuming this might exist or be added
  budgetMin?: number;
  budgetMax?: number;
  // ... other preference fields
}

// Client-side version of WishlistItem
export interface WishlistItem {
  item: string;
  notes?: string;
  link?: string;
  dateAdded: Timestamp | null;
}

// Client-side version of PastGiftGiven
export interface PastGiftGiven {
  // Core gift information (existing fields)
  item: string;
  occasion?: string;
  date: Timestamp | null;
  reaction?: string;
  
  // NEW: Unique identifier for better tracking
  id?: string; // UUID for tracking, optional for backward compatibility
  
  // NEW: Success metrics for analytics and chart enhancement
  rating?: number; // 1-5 star rating, optional for backward compatibility
  loved?: boolean; // Did they love it? Optional for backward compatibility
  stillUsed?: boolean; // Do they still use/have it? Optional for backward compatibility
  wouldGiveAgain?: boolean; // Would you give this type of gift again? Optional for backward compatibility
  recipientMood?: "ecstatic" | "delighted" | "happy" | "satisfied" | "neutral" | "disappointed"; // How did the recipient react/feel

  
  // NEW: Context and metadata for insights
  estimatedCost?: number; // Optional cost tracking
  timeToFind?: number; // Hours spent finding/making this gift
  source?: "store" | "online" | "handmade" | "experience" | "other"; // Where/how was it obtained
  isHandmade?: boolean; // Was this handmade?
  category?: string; // Gift category (auto-detected or manual)
  tags?: string[]; // Flexible tagging system
  
  // NEW: Analytics metadata
  createdAt?: Timestamp; // When this entry was created
  updatedAt?: Timestamp; // When this entry was last updated
  version?: number; // Data version for migrations (default 1 for legacy, 2 for enhanced)
}

// Client-side version of GeneralNote
export interface GeneralNote {
  note: string;
  date: Timestamp | null;
}

// Client-side version of CustomDate
export interface CustomDate {
  id: string;
  name: string;
  date: Timestamp | null;
  customDateMonthDay?: string; // "MM-DD" format
}

// Client-side version of SignificantOtherProfile
export interface SignificantOtherProfile {
  userId: string;
  profileId: string;
  name: string;
  relationship: string;
  birthday?: Timestamp | null;
  anniversary?: Timestamp | null;
  birthdayMonthDay?: string;
  anniversaryMonthDay?: string;
  interests: string[];
  dislikes: string[];
  preferences?: SOPreferences & { // Combine with existing and allow others
    favoriteColor?: string | null;
    preferredStyle?: string | null;
    favoriteBrands?: string[];
    budgetMin?: number;
    budgetMax?: number;
    [key: string]: any; // Allow other preference fields
  };
  sizes?: {
    clothing?: string | null;
    shoe?: string | null;
    [key: string]: any; // Allow other size categories
  };
  wishlistItems: WishlistItem[];
  pastGiftsGiven: PastGiftGiven[];
  generalNotes: GeneralNote[];
  customDates?: CustomDate[];
  createdAt: Timestamp;
  updatedAt: Timestamp;
  // Add any other top-level fields planned for client-side use
}

// Add other Firestore related types here as needed