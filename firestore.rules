rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // De<PERSON>ult deny all access
    match /{document=**} {
      allow read, write: if false;
    }

    // Allow users to manage their own document in a 'users' collection
    match /users/{userId} {
      // Allow read and update if the user is authenticated and the UID matches the document ID
      allow read, update: if request.auth != null && request.auth.uid == userId;
      // Allow create if the user is authenticated and the UID matches the document ID
      allow create: if request.auth != null && request.auth.uid == userId;
      // Deny delete for now
      allow delete: if false;

      // Allow users to manage their own push notification tokens
      match /tokens/{tokenId} {
        allow read, write: if request.auth != null && request.auth.uid == userId;
      }
    }

    match /significant_others/{profileId} {
      // Allow read/write only if the user is authenticated and owns the profile
      allow read, delete: if request.auth != null && resource.data.userId == request.auth.uid;
      allow update: if request.auth != null && resource.data.userId == request.auth.uid && (!('userId' in request.resource.data) || request.resource.data.userId == resource.data.userId);
      // Allow create if the user is authenticated and the userId matches the authenticated user's UID
      allow create: if request.auth != null && request.resource.data.userId == request.auth.uid;
    }

    match /recommendation_feedback/{feedbackId} {
      // Allow create only for authenticated users and ensure userId matches auth.uid
      allow create: if request.auth != null
        && request.resource.data.userId == request.auth.uid // User owns the feedback
        && request.resource.data.profileId is string // Required fields are present
        && request.resource.data.recommendationId is string
        && request.resource.data.feedbackType is string
        && request.resource.data.recommendationDetails is map // Details is a map
        && request.resource.data.timestamp == request.time; // Enforce server timestamp on create
      // Allow read/delete if:
      // - User is authenticated AND
      // - Either feedback belongs to user OR profile belongs to user
      allow read, delete: if request.auth != null
        && (resource.data.userId == request.auth.uid
            || get(/databases/$(database)/documents/significant_others/$(resource.data.profileId)).data.userId == request.auth.uid);
      // Allow updates only from the user who owns the document,
      // and only allow 'feedbackType' and 'timestamp' to be changed.
      allow update: if request.auth != null
        && resource.data.userId == request.auth.uid // User owns the document
        && request.resource.data.userId == resource.data.userId // Ensure immutable fields do not change
        && request.resource.data.profileId == resource.data.profileId
        && request.resource.data.recommendationId == resource.data.recommendationId
        && request.resource.data.recommendationDetails == resource.data.recommendationDetails
        && request.resource.data.timestamp == request.time; // Timestamp must be a server timestamp
    }

    // Corrected placement for pushTokens
    match /pushTokens/{tokenId} {
      allow read, delete: if request.auth != null && request.auth.uid == resource.data.userId;
      allow update: if request.auth != null && request.auth.uid == resource.data.userId && (!('userId' in request.resource.data) || request.resource.data.userId == resource.data.userId);
      allow create: if request.auth != null && request.auth.uid == request.resource.data.userId;
    }

    // Corrected placement for userSettings
    match /userSettings/{userId} {
      allow read: if request.auth != null && request.auth.uid == userId;
      allow create: if request.auth != null && request.auth.uid == userId && request.resource.data.userId == request.auth.uid; // Ensure doc field matches
      allow update: if request.auth != null && request.auth.uid == userId && (!('userId' in request.resource.data) || request.resource.data.userId == userId); // userId field cannot change
      allow delete: if request.auth != null && request.auth.uid == userId; // Or set to false if not deletable
    }
  }
}