import React from 'react';
import { View, Text, TouchableOpacity, ScrollView, Modal, Dimensions } from 'react-native';
import { Feather } from '@expo/vector-icons';
import Animated, { 
  useAnimatedStyle, 
  useSharedValue, 
  withSpring,
  FadeIn,
  FadeOut
} from 'react-native-reanimated';
import { useColorScheme } from 'nativewind';

interface SizeOption {
  label: string;
  value: string;
  description: string;
  category: string;
  isCommon: boolean;
  icon: string;
}

interface SizeModalProps {
  isVisible: boolean;
  onClose: () => void;
  options: SizeOption[];
  selectedValue?: string;
  onValueChange: (value: string) => void;
  title?: string;
  sizeType?: 'clothing' | 'shoe';
}

const AnimatedTouchableOpacity = Animated.createAnimatedComponent(TouchableOpacity);
const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

const SizeModal: React.FC<SizeModalProps> = ({
  isVisible,
  onClose,
  options,
  selectedValue,
  onValueChange,
  title = "Choose Size",
  sizeType = 'clothing'
}) => {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  // Theme colors
  const themedColors = {
    primary: isDark ? '#D96D00' : '#A3002B',
    textPrimary: isDark ? '#F9FAFB' : '#1F2937',
    textSecondary: isDark ? '#9CA3AF' : '#6B7280',
    border: isDark ? '#374151' : '#E5E7EB',
    background: isDark ? '#111827' : '#F9FAFB',
    card: isDark ? '#1F2937' : '#FFFFFF',
    backdrop: isDark ? 'rgba(0, 0, 0, 0.7)' : 'rgba(0, 0, 0, 0.5)',
    commonBadge: isDark ? '#374151' : '#F3F4F6',
  };

  const handleSizeSelect = (value: string) => {
    onValueChange(value);
    onClose();
  };

  // Helper function to get size order value for proper sorting
  const getSizeOrderValue = (option: SizeOption): number => {
    const label = option.label;
    
    // Handle clothing sizes (Standard category)
    if (option.category === 'Standard') {
      const sizeOrder = ['XS', 'S', 'M', 'L', 'XL'];
      const index = sizeOrder.indexOf(label);
      return index !== -1 ? index : 999;
    }
    
    // Handle extended clothing sizes
    if (option.category === 'Extended') {
      const sizeOrder = ['XXL', 'XXXL'];
      const index = sizeOrder.indexOf(label);
      return index !== -1 ? index + 100 : 999;
    }
    
    // Handle numeric clothing sizes
    if (option.category === 'Numeric') {
      const numericValue = parseFloat(label);
      return isNaN(numericValue) ? 999 : numericValue;
    }
    
    // Handle shoe sizes (Women's and Men's)
    if (option.category === 'Women\'s' || option.category === 'Men\'s') {
      // Extract the numeric part from labels like "7 (M)" or "7.5"
      const numericPart = label.replace(/\s*\(M\)/, '');
      const numericValue = parseFloat(numericPart);
      return isNaN(numericValue) ? 999 : numericValue;
    }
    
    // Handle custom/other - put at end
    if (option.category === 'Custom') {
      return 1000;
    }
    
    // Fallback to alphabetical for unknown categories
    return 999;
  };

  // Group options by category
  const groupedOptions = options.reduce((groups, option) => {
    const category = option.category;
    if (!groups[category]) {
      groups[category] = [];
    }
    groups[category].push(option);
    return groups;
  }, {} as Record<string, SizeOption[]>);

  // Sort each category by proper size order (smallest to largest)
  Object.keys(groupedOptions).forEach(category => {
    groupedOptions[category].sort((a, b) => {
      const aOrder = getSizeOrderValue(a);
      const bOrder = getSizeOrderValue(b);
      return aOrder - bOrder;
    });
  });

  const SizeCard = ({ option }: { option: SizeOption }) => {
    const isSelected = selectedValue === option.value;
    const scaleValue = useSharedValue(1);

    const animatedStyle = useAnimatedStyle(() => ({
      transform: [{ scale: scaleValue.value }],
    }));

    const handlePress = () => {
      scaleValue.value = withSpring(0.95, { duration: 100 });
      setTimeout(() => {
        scaleValue.value = withSpring(1, { duration: 100 });
      }, 100);
      handleSizeSelect(option.value);
    };

    return (
      <AnimatedTouchableOpacity
        style={[
          animatedStyle,
          {
            backgroundColor: isSelected ? themedColors.primary + '15' : themedColors.card,
          }
        ]}
        onPress={handlePress}
        className="p-4 mb-3 rounded-xl border-2 border-border"
        accessibilityRole="button"
        accessibilityLabel={`Select ${option.label} size`}
        accessibilityState={{ selected: isSelected }}
      >
        <View className="flex-row justify-between items-center">
          <View className="flex-row flex-1 items-center">
            {/* Size Icon */}
            <View
              className="p-2 mr-3 rounded-full"
              style={{
                backgroundColor: isSelected ? themedColors.primary : themedColors.background,
              }}
            >
              <Feather
                name={option.icon as any}
                size={20}
                color={isSelected ? '#FFFFFF' : themedColors.primary}
              />
            </View>

            {/* Size Info */}
            <View className="flex-1">
              <View className="flex-row items-center">
                <Text
                  className={`text-lg font-bold mr-2 ${isSelected ? 'font-bold' : 'font-semibold'}`}
                  style={{
                    color: isSelected ? themedColors.primary : themedColors.textPrimary,
                  }}
                >
                  {option.label}
                </Text>
                
                {/* Common Badge */}
                {option.isCommon && (
                  <View
                    className="px-2 py-1 rounded-full"
                    style={{ backgroundColor: themedColors.commonBadge }}
                  >
                    <Text
                      className="text-xs font-medium"
                      style={{ color: themedColors.textSecondary }}
                    >
                      Popular
                    </Text>
                  </View>
                )}
              </View>

              <Text
                className="mt-1 text-sm"
                style={{
                  color: isSelected ? themedColors.textPrimary : themedColors.textSecondary,
                }}
              >
                {option.description}
              </Text>
            </View>
          </View>

          {/* Selected Check Mark */}
          {isSelected && (
            <View
              className="p-1 ml-2 rounded-full"
              style={{ backgroundColor: themedColors.primary }}
            >
              <Feather
                name="check"
                size={16}
                color="#FFFFFF"
              />
            </View>
          )}
        </View>
      </AnimatedTouchableOpacity>
    );
  };

  // Category display order
  const categoryOrder = sizeType === 'clothing' 
    ? ['Standard', 'Numeric', 'Extended', 'Custom']
    : ['Women\'s', 'Men\'s', 'Custom'];

  return (
    <Modal
      visible={isVisible}
      transparent={true}
      animationType="fade"
      statusBarTranslucent={true}
      onRequestClose={onClose}
    >
      <TouchableOpacity
        activeOpacity={1}
        onPress={onClose}
        className="flex-1 justify-center items-center"
        style={{ backgroundColor: themedColors.backdrop }}
      >
        <Animated.View
          entering={FadeIn.duration(200)}
          exiting={FadeOut.duration(150)}
          className="overflow-hidden mx-4 rounded-2xl"
          style={{
            backgroundColor: themedColors.card,
            maxWidth: screenWidth * 0.92,
            maxHeight: screenHeight * 0.85,
            minWidth: screenWidth * 0.85,
          }}
          onStartShouldSetResponder={() => true}
        >
          {/* Header */}
          <View 
            className="flex-row justify-between items-center px-6 py-4 border-b"
            style={{ borderBottomColor: themedColors.border }}
          >
            <Text
              className="text-lg font-bold"
              style={{ color: themedColors.textPrimary }}
            >
              {title}
            </Text>
            <TouchableOpacity
              onPress={onClose}
              className="justify-center items-center w-8 h-8 rounded-full"
              style={{ backgroundColor: themedColors.background }}
              accessibilityRole="button"
              accessibilityLabel="Close size selection"
            >
              <Feather name="x" size={18} color={themedColors.textSecondary} />
            </TouchableOpacity>
          </View>

          {/* Size Cards by Category */}
          <ScrollView
            showsVerticalScrollIndicator={false}
            className="px-6 py-4"
            style={{ maxHeight: screenHeight * 0.7 }}
          >
            {categoryOrder.map(category => {
              const categoryOptions = groupedOptions[category];
              if (!categoryOptions || categoryOptions.length === 0) return null;

              return (
                <View key={category} className="mb-6">
                  {/* Category Header */}
                  <Text
                    className="mb-3 text-sm font-semibold tracking-wide uppercase"
                    style={{ color: themedColors.textSecondary }}
                  >
                    {category} {sizeType === 'clothing' && category === 'Standard' ? 'Sizes' : ''}
                  </Text>
                  
                  {/* Category Options */}
                  {categoryOptions.map((option) => (
                    <SizeCard key={option.value} option={option} />
                  ))}
                </View>
              );
            })}

            {/* Size Guide Tip */}
            <View 
              className="p-4 mt-4 rounded-xl"
              style={{ backgroundColor: themedColors.background }}
            >
              <View className="flex-row items-start">
                <Feather 
                  name="info" 
                  size={16} 
                  color={themedColors.primary}
                  style={{ marginTop: 2, marginRight: 8 }}
                />
                <Text
                  className="flex-1 text-sm"
                  style={{ color: themedColors.textSecondary }}
                >
                  {sizeType === 'clothing' 
                    ? "Not sure about size? Most people choose their most commonly worn size across different brands."
                    : "All sizes are in US sizing. If unsure, choose the size they wear most often in sneakers or dress shoes."
                  }
                </Text>
              </View>
            </View>
          </ScrollView>
        </Animated.View>
      </TouchableOpacity>
    </Modal>
  );
};

export default SizeModal; 