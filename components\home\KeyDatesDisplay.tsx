import React from 'react';
import { View, Text } from 'react-native';
import { Feather } from '@expo/vector-icons';
import Animated, { FadeIn } from 'react-native-reanimated';
import { useColorScheme } from 'nativewind';
import { format } from 'date-fns';

import { SignificantOtherProfile } from '../../functions/src/types/firestore';

interface KeyDatesDisplayProps {
  profile: SignificantOtherProfile | null;
  className?: string;
}

// Helper function to safely convert Firestore Timestamp or Date to Date
const toDate = (dateValue: any): Date | null => {
  if (!dateValue) return null;

  try {
    // Handle Firestore Timestamp
    if (dateValue.toDate && typeof dateValue.toDate === 'function') {
      const date = dateValue.toDate();
      return isValidDate(date) ? date : null;
    }

    // Handle other date formats
    const date = new Date(dateValue);
    return isValidDate(date) ? date : null;
  } catch (error) {
    console.warn('Invalid date value provided to toDate:', dateValue, error);
    return null;
  }
};

// Helper function to check if a date is valid
const isValidDate = (date: Date): boolean => {
  return date instanceof Date && !isNaN(date.getTime());
};

const KeyDatesDisplay: React.FC<KeyDatesDisplayProps> = ({
  profile,
  className = ''
}) => {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  if (!profile) return null;

  const hasBirthday = profile.birthday;
  const hasAnniversary = profile.anniversary;

  // Don't show if no key dates are set
  if (!hasBirthday && !hasAnniversary) return null;

  return (
    <Animated.View
      entering={FadeIn.duration(600)}
      className={`mb-6 ${className}`}
    >
      <Text className="mb-3 text-lg font-semibold text-text-primary dark:text-text-primary-dark">
        Key Dates
      </Text>

      <View className="flex-row flex-wrap gap-x-3 gap-y-3">
        {hasBirthday && (
          <View className="flex-1 items-center p-3 rounded-lg border bg-card dark:bg-card-dark border-border dark:border-border-dark">
            <View className="p-2 mb-2 rounded-full bg-primary/10 dark:bg-primary-dark/10">
              <Feather
                name="gift"
                size={24}
                color={isDark ? '#C70039' : '#A3002B'}
              />
            </View>

            <Text className="text-sm font-medium text-text-primary dark:text-text-primary-dark">
              Birthday
            </Text>
            <Text className="text-base text-text-secondary dark:text-text-secondary-dark text-center">
              {profile.birthday ? (() => {
                const date = toDate(profile.birthday);
                return date ? format(date, 'MMM d, yyyy') : 'Invalid date';
              })() : ''}
            </Text>
          </View>
        )}

        {hasAnniversary && (
          <View className="flex-1 items-center p-3 rounded-lg border bg-card dark:bg-card-dark border-border dark:border-border-dark">
            <View className="p-2 mb-2 rounded-full bg-accent/10 dark:bg-accent-dark/10">
              <Feather
                name="heart"
                size={24}
                color={isDark ? '#FF507B' : '#E5355F'}
              />
            </View>

            <Text className="text-sm font-medium text-text-primary dark:text-text-primary-dark">
              Anniversary
            </Text>
            <Text className="text-base text-text-secondary dark:text-text-secondary-dark text-center">
              {profile.anniversary ? (() => {
                const date = toDate(profile.anniversary);
                return date ? format(date, 'MMM d, yyyy') : 'Invalid date';
              })() : ''}
            </Text>
          </View>
        )}
      </View>
    </Animated.View>
  );
};

export default KeyDatesDisplay;
