import { useState, useCallback, useMemo } from 'react';
import { Timestamp } from 'firebase/firestore';
import { CustomDate, SignificantOtherProfile } from '@/functions/src/types/firestore';
import { format, isToday, addYears, isPast, differenceInDays } from 'date-fns';
import localHolidays from '@/utils/holidays.json';
import { AppError, createAppError, safeAsync, ErrorSeverity } from '@/utils/errorHandling';

// Interface for multi-dot marking
interface Dot {
  key: string; // Unique key for the dot (e.g., 'birthday-profileId', 'holiday-name')
  color: string;
  selectedDotColor?: string; // Optional: color when the day is selected
}

interface MarkedDates {
  [date: string]: {
    dots?: Dot[];
    marked?: boolean; // Can still use marked for general marking if needed
    selected?: boolean;
    selectedColor?: string;
    disabled?: boolean;
    disableTouchEvent?: boolean;
    activeOpacity?: number;
  };
}

// Define the type for processed dates
export interface CalendarEvent {
  id: string; // Unique ID for the event
  name: string;
  type: 'Birthday' | 'Anniversary' | 'Custom Date' | 'Holiday';
  date: Date;
  daysUntil: number;
  profileId?: string; // Link to profile if applicable
  description?: string; // For holidays
}

// Define the type for processed events map
interface ProcessedEvents {
  [date: string]: CalendarEvent[];
}

// Mapping for event types to theme colors (hex codes)
const eventColorMap: { [key in CalendarEvent['type']]: string } = {
  'Birthday': '#22C55E', // Using 'birthday' color from tailwind.config.js
  'Anniversary': '#3B82F6', // Using 'anniversary' color from tailwind.config.js
  'Custom Date': '#A855F7', // Using 'customDate' color from tailwind.config.js
  'Holiday': '#EF4444', // Using 'holiday' color from tailwind.config.js
};

interface UseCalendarEventsReturn {
  markedDates: MarkedDates;
  processedEvents: ProcessedEvents;
  closestDate: CalendarEvent | null;
  upcomingDates: CalendarEvent[];
  error: AppError | null;
  processEvents: (profile: SignificantOtherProfile | null) => void;
  clearEvents: () => void;
  clearError: () => void;
}

/**
 * Hook for processing calendar events and generating markings
 * Enhanced with comprehensive error handling (CQA-002)
 * Extracted from useCalendarData god object for single responsibility
 */
const useCalendarEvents = (): UseCalendarEventsReturn => {
  const [activeProfile, setActiveProfile] = useState<SignificantOtherProfile | null>(null);
  const [errorOverridden, setErrorOverridden] = useState(false);

  // Helper function to get the next occurrence of a yearly date
  const getNextOccurrence = useCallback((date: Date, today: Date): Date => {
    const currentYear = today.getFullYear();
    const dateThisYear = new Date(today.getFullYear(), date.getMonth(), date.getDate());

    if (isPast(dateThisYear) && !isToday(dateThisYear)) {
      return addYears(dateThisYear, 1);
    }
    return dateThisYear;
  }, []);

  // Enhanced helper function to safely convert a date field with proper error handling
  const safeConvertToDate = useCallback((dateField: any, context = 'date conversion'): Date | null => {
    if (!dateField) return null;
    
    try {
      // Handle Firestore Timestamp
      if (dateField.seconds && typeof dateField.seconds === 'number' && typeof dateField.nanoseconds === 'number') {
        const date = new Timestamp(dateField.seconds, dateField.nanoseconds).toDate();
        if (isNaN(date.getTime())) {
          const error = createAppError(
            new Error(`Invalid Firestore Timestamp: ${JSON.stringify(dateField)}`),
            `Calendar.${context}`,
            ErrorSeverity.LOW
          );
          console.warn('CALENDAR EVENTS: Invalid date from Firestore:', error.message);
          return null;
        }
        return date;
      }
      
      // Handle Date objects or ISO strings
      const date = new Date(dateField);
      if (isNaN(date.getTime())) {
        const error = createAppError(
          new Error(`Invalid date format: ${dateField}`),
          `Calendar.${context}`,
          ErrorSeverity.LOW
        );
        console.warn('CALENDAR EVENTS: Invalid date format:', error.message);
        return null;
      }
      return date;
    } catch (e) {
      const error = createAppError(
        e,
        `Calendar.${context}`,
        ErrorSeverity.LOW
      );
      console.warn('CALENDAR EVENTS: Error converting date:', error.message);
      return null;
    }
  }, []);

  const memoizedCalendarData = useMemo(() => {
    try {
      const marks: MarkedDates = {};
      const eventsMap: ProcessedEvents = {};
      const allUpcomingDates: CalendarEvent[] = [];
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const allErrors: string[] = [];

      // Process profile dates if a profile is active
      if (activeProfile) {
        console.log('CALENDAR EVENTS: Processing events for profile:', activeProfile.name);
        
        // Process birthday
        try {
          const birthdayDate = safeConvertToDate(activeProfile.birthday, 'birthday');
          if (birthdayDate) {
            const nextOccurrence = getNextOccurrence(birthdayDate, today);
            const daysUntil = differenceInDays(nextOccurrence, today);
            if (daysUntil >= 0 && daysUntil <= 365) {
              const event: CalendarEvent = { id: `birthday-${activeProfile.profileId}`, name: activeProfile.name, type: 'Birthday', date: nextOccurrence, daysUntil, profileId: activeProfile.profileId };
              allUpcomingDates.push(event);
              const dateString = format(nextOccurrence, 'yyyy-MM-dd');
              if (!marks[dateString]) marks[dateString] = { dots: [] };
              marks[dateString].dots?.push({ key: event.id, color: eventColorMap[event.type] });
              if (!eventsMap[dateString]) eventsMap[dateString] = [];
              eventsMap[dateString].push(event);
            }
          }
        } catch (e) {
          allErrors.push(`Birthday processing failed: ${createAppError(e).userMessage}`);
        }

        // Process anniversary
        try {
          const anniversaryDate = safeConvertToDate(activeProfile.anniversary, 'anniversary');
          if (anniversaryDate) {
            const nextOccurrence = getNextOccurrence(anniversaryDate, today);
            const daysUntil = differenceInDays(nextOccurrence, today);
            if (daysUntil >= 0 && daysUntil <= 365) {
              const event: CalendarEvent = { id: `anniversary-${activeProfile.profileId}`, name: activeProfile.name, type: 'Anniversary', date: nextOccurrence, daysUntil, profileId: activeProfile.profileId };
              allUpcomingDates.push(event);
              const dateString = format(nextOccurrence, 'yyyy-MM-dd');
              if (!marks[dateString]) marks[dateString] = { dots: [] };
              marks[dateString].dots?.push({ key: event.id, color: eventColorMap[event.type] });
              if (!eventsMap[dateString]) eventsMap[dateString] = [];
              eventsMap[dateString].push(event);
            }
          }
        } catch (e) {
          allErrors.push(`Anniversary processing failed: ${createAppError(e).userMessage}`);
        }

        // Process custom dates
        if (activeProfile.customDates && Array.isArray(activeProfile.customDates)) {
          for (const customDate of activeProfile.customDates) {
            try {
              const date = safeConvertToDate(customDate.date, 'customDate');
              if (date) {
                const nextOccurrence = getNextOccurrence(date, today);
                const daysUntil = differenceInDays(nextOccurrence, today);
                if (daysUntil >= 0 && daysUntil <= 365) {
                  const event: CalendarEvent = { id: `custom-${activeProfile.profileId}-${customDate.name}`, name: `${customDate.name} (${activeProfile.name})`, type: 'Custom Date', date: nextOccurrence, daysUntil, profileId: activeProfile.profileId };
                  allUpcomingDates.push(event);
                  const dateString = format(nextOccurrence, 'yyyy-MM-dd');
                  if (!marks[dateString]) marks[dateString] = { dots: [] };
                  marks[dateString].dots?.push({ key: event.id, color: eventColorMap[event.type] });
                  if (!eventsMap[dateString]) eventsMap[dateString] = [];
                  eventsMap[dateString].push(event);
                }
              }
            } catch (e) {
              allErrors.push(`Custom date "${customDate.name}" processing failed: ${createAppError(e).userMessage}`);
            }
          }
        }
      }

      // Process holidays
      try {
        for (const holiday of localHolidays) {
          const currentYear = today.getFullYear();
          for (let year = currentYear; year <= currentYear + 1; year++) {
            const holidayDate = new Date(year, holiday.month - 1, holiday.day);
            const daysUntil = differenceInDays(holidayDate, today);
            if (daysUntil >= 0 && daysUntil <= 365) {
              const event: CalendarEvent = { id: `holiday-${holiday.name}-${year}`, name: holiday.name, type: 'Holiday', date: holidayDate, daysUntil, description: holiday.description };
              allUpcomingDates.push(event);
              const dateString = format(holidayDate, 'yyyy-MM-dd');
              if (!marks[dateString]) marks[dateString] = { dots: [] };
              marks[dateString].dots?.push({ key: event.id, color: eventColorMap[event.type] });
              if (!eventsMap[dateString]) eventsMap[dateString] = [];
              eventsMap[dateString].push(event);
            }
          }
        }
      } catch (e) {
        allErrors.push(`Holiday data loading failed: ${createAppError(e).userMessage}`);
      }
      
      const sortedUpcomingDates = allUpcomingDates.sort((a, b) => a.daysUntil - b.daysUntil);
      const closest = sortedUpcomingDates.length > 0 ? sortedUpcomingDates[0] : null;

      console.log('CALENDAR EVENTS: Processed', allUpcomingDates.length, 'events, closest:', closest?.name);
      
      let error: AppError | null = null;
      if (allErrors.length > 0) {
        console.warn('CALENDAR EVENTS: Some events failed to process:', allErrors);
        error = createAppError(
            new Error(`Some calendar events could not be processed: ${allErrors.slice(0, 2).join(', ')}...`),
            'Calendar.processEvents.partialFailure',
            ErrorSeverity.LOW
        );
      }

      return { markedDates: marks, processedEvents: eventsMap, closestDate: closest, upcomingDates: sortedUpcomingDates, error };

    } catch (e) {
      const error = createAppError(e, 'Calendar.processEvents', ErrorSeverity.MEDIUM);
      console.error('CALENDAR EVENTS: Critical error processing events:', error);
      return { markedDates: {}, processedEvents: {}, closestDate: null, upcomingDates: [], error };
    }
  }, [activeProfile, getNextOccurrence, safeConvertToDate]);

  const processEvents = useCallback((profile: SignificantOtherProfile | null) => {
    setErrorOverridden(false);
    setActiveProfile(profile);
  }, []);

  const clearEvents = useCallback(() => {
    setActiveProfile(null);
  }, []);

  const clearError = useCallback(() => {
    setErrorOverridden(true);
  }, []);

  return {
    markedDates: memoizedCalendarData.markedDates,
    processedEvents: memoizedCalendarData.processedEvents,
    closestDate: memoizedCalendarData.closestDate,
    upcomingDates: memoizedCalendarData.upcomingDates,
    error: errorOverridden ? null : memoizedCalendarData.error,
    processEvents,
    clearEvents,
    clearError,
  };
};

export default useCalendarEvents; 