import React, { useState, useRef, useCallback, useMemo } from 'react';
import {
  View,
  Text,
  Pressable,
  Modal,
  Dimensions,
  Platform,
  ScrollView,
} from 'react-native';
import Animated, {
  FadeIn,
  FadeOut,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withTiming,
} from 'react-native-reanimated';
import { Feather } from '@expo/vector-icons';
import { useColorScheme } from 'nativewind';
import { SignificantOtherProfile } from '@/functions/src/types/firestore';
import { colors } from '@/constants/Colors';
import * as Haptics from 'expo-haptics';

// Extract getInitials function from profiles/index.tsx
const getInitials = (name: string | undefined | null): string => {
  if (!name || typeof name !== 'string' || name.trim() === '') {
    return '?';
  }
  const names = name.trim().split(/\s+/).filter(Boolean);
  if (names.length === 0) {
    return '?';
  }

  let initials = names[0][0].toUpperCase();

  if (names.length > 1 && names[names.length - 1].length > 0) {
    initials += names[names.length - 1][0].toUpperCase();
  }
  return initials;
};

interface ProfileDropdownProps {
  profiles: SignificantOtherProfile[];
  selectedProfileId: string | null;
  onProfileSelect: (profileId: string) => void;
  trigger: React.ReactNode;
  placeholder?: string;
  disabled?: boolean;
}

const ProfileDropdown: React.FC<ProfileDropdownProps> = ({
  profiles,
  selectedProfileId,
  onProfileSelect,
  trigger,
  placeholder = "Select Profile",
  disabled = false,
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [dropdownLayout, setDropdownLayout] = useState({ x: 0, y: 0, width: 0, height: 0 });
  const triggerRef = useRef<View>(null);
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  // Theme colors using your app's actual color system
  const themedColors = useMemo(() => ({
    primary: isDark ? colors.dark.primary : colors.light.primary,
    background: isDark ? colors.dark.background : colors.light.background,
    textPrimary: isDark ? colors.dark.textPrimary : colors.light.textPrimary,
    textSecondary: isDark ? colors.dark.textSecondary : colors.light.textSecondary,
    card: isDark ? colors.dark.card : colors.light.card,
    border: isDark ? colors.dark.border : colors.light.border,
  }), [isDark]);

  const dropdownOpacity = useSharedValue(0);
  const dropdownScale = useSharedValue(0.95);

  const handleTriggerPress = useCallback(() => {
    if (disabled || profiles.length === 0) return;
    
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    
    triggerRef.current?.measure((x, y, width, height, pageX, pageY) => {
      const screenHeight = Dimensions.get('window').height;
      const maxVisibleProfiles = 5;
      const profileItemHeight = 60;
      const padding = 20;
      
      // Calculate ideal height based on number of profiles
      const idealHeight = profiles.length * profileItemHeight + padding;
      const maxHeight = maxVisibleProfiles * profileItemHeight + padding;
      const dropdownHeight = Math.min(idealHeight, maxHeight);
      
      let dropdownY = pageY + height + 2; // 2px gap for seamless connection
      
      // Check if dropdown would go off screen, flip above if needed
      if (dropdownY + dropdownHeight > screenHeight - 50) {
        dropdownY = pageY - dropdownHeight - 2; // 2px gap above trigger
      }
      
      setDropdownLayout({
        x: pageX,
        y: dropdownY,
        width: width, // Use exact trigger width
        height: dropdownHeight,
      });
      
      setIsVisible(true);
      
      // Animate in
      dropdownOpacity.value = withTiming(1, { duration: 200 });
      dropdownScale.value = withSpring(1, { damping: 15, stiffness: 300 });
    });
  }, [disabled, profiles.length, dropdownOpacity, dropdownScale]);

  const handleClose = useCallback(() => {
    // Animate out
    dropdownOpacity.value = withTiming(0, { duration: 150 });
    dropdownScale.value = withTiming(0.95, { duration: 150 });
    
    setTimeout(() => {
      setIsVisible(false);
    }, 150);
  }, [dropdownOpacity, dropdownScale]);

  const handleProfileSelectInternal = useCallback((profileId: string) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    onProfileSelect(profileId);
    handleClose();
  }, [onProfileSelect, handleClose]);

  const dropdownAnimatedStyle = useAnimatedStyle(() => ({
    opacity: dropdownOpacity.value,
    transform: [{ scale: dropdownScale.value }],
  }));

  const selectedProfile = profiles.find(p => p.profileId === selectedProfileId);

  return (
    <>
      <View ref={triggerRef} collapsable={false}>
        <Pressable onPress={handleTriggerPress} disabled={disabled}>
          {trigger}
        </Pressable>
      </View>

      <Modal
        visible={isVisible}
        transparent
        animationType="none"
        onRequestClose={handleClose}
      >
        {/* Backdrop */}
        <Pressable
          style={{ flex: 1 }}
          onPress={handleClose}
        >
          <View style={{ flex: 1, backgroundColor: 'rgba(0,0,0,0.1)' }} />
        </Pressable>

        {/* Dropdown */}
        <Animated.View
          style={[
            {
              position: 'absolute',
              left: dropdownLayout.x,
              top: dropdownLayout.y,
              width: dropdownLayout.width,
              height: dropdownLayout.height,
              backgroundColor: themedColors.card,
              borderRadius: 12,
              borderWidth: 1,
              borderColor: themedColors.border,
              shadowColor: '#000',
              shadowOffset: { width: 0, height: 4 },
              shadowOpacity: 0.15,
              shadowRadius: 12,
              elevation: 8,
              zIndex: 1000,
              overflow: 'hidden', // Ensure content doesn't overflow
            },
            dropdownAnimatedStyle,
          ]}
        >
          <Animated.View 
            entering={FadeIn.duration(200)} 
            exiting={FadeOut.duration(150)}
            style={{ flex: 1 }}
          >
            {profiles.length === 0 ? (
              <View className="p-4">
                <Text style={{ color: themedColors.textSecondary }}>
                  No profiles available
                </Text>
              </View>
            ) : (
              <ScrollView
                style={{ flex: 1 }}
                contentContainerStyle={{ flexGrow: 1 }}
                showsVerticalScrollIndicator={profiles.length > 5}
                bounces={false}
                keyboardShouldPersistTaps="handled"
              >
                {profiles.map((profile, index) => {
                  const isSelected = profile.profileId === selectedProfileId;
                  const initials = getInitials(profile.name);
                  
                  return (
                    <Pressable
                      key={profile.profileId}
                      onPress={() => handleProfileSelectInternal(profile.profileId)}
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        padding: 12,
                        minHeight: 60, // Ensure consistent height
                        borderBottomWidth: index === profiles.length - 1 ? 0 : 1,
                        borderBottomColor: themedColors.border,
                        backgroundColor: isSelected ? `${themedColors.primary}15` : 'transparent',
                      }}
                    >
                      {/* Profile Avatar */}
                      <View
                        style={{
                          width: 36,
                          height: 36,
                          borderRadius: 18,
                          backgroundColor: themedColors.primary,
                          justifyContent: 'center',
                          alignItems: 'center',
                          marginRight: 12,
                        }}
                      >
                        <Text
                          style={{
                            color: '#FFFFFF',
                            fontSize: 14,
                            fontWeight: '600',
                          }}
                        >
                          {initials}
                        </Text>
                      </View>

                      {/* Profile Info */}
                      <View style={{ flex: 1 }}>
                        <Text
                          style={{
                            color: themedColors.textPrimary,
                            fontSize: 16,
                            fontWeight: isSelected ? '600' : '500',
                            marginBottom: 2,
                          }}
                        >
                          {profile.name}
                        </Text>
                        {profile.relationship && (
                          <Text
                            style={{
                              color: themedColors.textSecondary,
                              fontSize: 12,
                              textTransform: 'capitalize',
                            }}
                          >
                            {profile.relationship}
                          </Text>
                        )}
                      </View>

                      {/* Selected Indicator */}
                      {isSelected && (
                        <Feather
                          name="check"
                          size={18}
                          color={themedColors.primary}
                        />
                      )}
                    </Pressable>
                  );
                })}
              </ScrollView>
            )}
          </Animated.View>
        </Animated.View>
      </Modal>
    </>
  );
};

export default ProfileDropdown; 