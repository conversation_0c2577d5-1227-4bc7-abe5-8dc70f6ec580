// components/home/<USER>/SmartHeader.tsx
import React from 'react';
import { View, Text } from 'react-native';
import { Feather } from '@expo/vector-icons';
import { useColorScheme } from 'nativewind';
import LoadingIndicator from '../../ui/LoadingIndicator';

interface SmartHeaderProps {
  profileName?: string;
  isLoading?: boolean;
  totalFeedback?: number;
  className?: string;
}

const SmartHeader: React.FC<SmartHeaderProps> = ({
  profileName,
  isLoading = false,
  totalFeedback = 0,
  className = "",
}) => {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const getPersonalizedTitle = () => {
    if (profileName) {
      return `Perfect gifts for ${profileName}`;
    }
    return "Daily Gift Recommendations";
  };

  const getLearningProgress = () => {
    if (totalFeedback === 0) return 0;
    // Simple progress calculation based on feedback count
    // More feedback = better learning (up to 100%)
    return Math.min((totalFeedback / 20) * 100, 100);
  };

  const getLearningMessage = () => {
    const progress = getLearningProgress();
    if (progress === 0) return "Ready to learn your preferences";
    if (progress < 25) return "Learning your taste...";
    if (progress < 50) return "Getting to know you better";
    if (progress < 75) return "Understanding your preferences";
    if (progress < 90) return "Almost perfected your taste";
    return profileName ? `Knows ${profileName}'s style perfectly` : "Knows your style perfectly";
  };

  return (
    <View className={`mb-4 ${className}`}>
      {/* Main Title */}
      <View className="flex-row items-center justify-between mb-2">
        <Text className="text-xl font-bold text-text-primary dark:text-text-primary-dark">
          {getPersonalizedTitle()}
        </Text>
        
        {isLoading && (
          <View className="flex-row items-center">
            <LoadingIndicator color={isDark ? '#C70039' : '#A3002B'} size="small" />
            <Text className="ml-2 text-sm text-text-secondary dark:text-text-secondary-dark">
              Updating...
            </Text>
          </View>
        )}
      </View>

      {/* Learning Progress */}
      <View className="flex-row items-center justify-between">
        <View className="flex-row items-center flex-1">
          <Feather name="cpu" size={16} color={isDark ? '#C70039' : '#A3002B'} />
          <Text className="ml-2 text-sm text-text-secondary dark:text-text-secondary-dark">
            {getLearningMessage()}
          </Text>
        </View>
        
        {totalFeedback > 0 && (
          <View className="flex-row items-center">
            <View className="w-16 h-2 bg-border dark:bg-border-dark rounded-full mr-2">
              <View 
                className="h-2 bg-primary dark:bg-primary-dark rounded-full"
                style={{ width: `${getLearningProgress()}%` }}
              />
            </View>
            <Text className="text-xs text-text-secondary dark:text-text-secondary-dark">
              {Math.round(getLearningProgress())}%
            </Text>
          </View>
        )}
      </View>

      {/* Feedback Count */}
      {totalFeedback > 0 && (
        <View className="flex-row items-center mt-1">
          <Feather name="heart" size={14} color={isDark ? '#C70039' : '#A3002B'} />
          <Text className="ml-1 text-xs text-text-secondary dark:text-text-secondary-dark">
            {totalFeedback} preferences learned
          </Text>
        </View>
      )}
    </View>
  );
};

export default SmartHeader; 