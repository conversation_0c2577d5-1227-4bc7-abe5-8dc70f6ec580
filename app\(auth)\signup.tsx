import React, { useState } from 'react';
import { View, Text, ActivityIndicator } from 'react-native'; // Added ActivityIndicator
import { Link } from 'expo-router'; // Removed router, not used
import Input from '@/components/ui/Input'; // Assuming alias setup
import <PERSON><PERSON> from '@/components/ui/Button'; // Assuming alias setup
import { useAuth } from '../../contexts/AuthContext'; // Import useAuth

export default function SignupScreen() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState<string | null>(null); // Add error state
  const { signUp, isLoading } = useAuth(); // Get signUp and isLoading from context
  const handleSignUp = async () => {
    setError(null); // Clear previous errors

    // Validation
    if (!email || !password || !confirmPassword) {
      setError('All fields are required.');
      return;
    }
    // Basic email format validation
    const emailRegex = /^[^@]+@[^@]+\.[^@]+$/;
    if (!emailRegex.test(email)) {
      setError('Please enter a valid email address.');
      return;
    }

    if (password !== confirmPassword) {
      setError('Passwords do not match.');
      return;
    }
    // Add more validation if needed (e.g., email format, password strength)

    try {
      await signUp(email, password);
      // Navigation upon successful signup is handled by the AuthProvider's useEffect
      console.log('Signup successful');
    } catch (err: any) {
      console.error('Signup failed:', err);
      // Provide a user-friendly error message
      setError(err.message || 'Failed to create account. Please try again.');
    }
  };

  return (
    <View className="flex-1 justify-center items-center p-6 bg-background">
      <View className="w-full max-w-sm">
        {/* Consistent Title Styling */}
        <Text className="mb-8 text-3xl font-bold text-center text-text-primary">
          Create Account
        </Text>

        {/* Input Group with Spacing */}
        <View className="gap-y-4 mb-4 w-full">
          <Input
            placeholder="Email"
            value={email}
            onChangeText={setEmail}
            keyboardType="email-address"
            autoCapitalize="none"
            // Consistent Input Styling
            className="p-3 rounded-md bg-input-background text-text-primary"
          />
          <Input
            placeholder="Password"
            value={password}
            onChangeText={setPassword}
            secureTextEntry
            // Consistent Input Styling
            className="p-3 rounded-md bg-input-background text-text-primary"
          />
          <Input
            placeholder="Confirm Password"
            value={confirmPassword}
            onChangeText={setConfirmPassword}
            secureTextEntry
            // Consistent Input Styling
            className="p-3 rounded-md bg-input-background text-text-primary"
          />
        </View>

        {/* Consistent Error Text Styling */}
        {error && (
          <Text className="mb-4 text-sm text-center text-feedback-error">
            {error}
          </Text>
        )}

        {/* Use Button component's isLoading prop, ensure primary variant (default) */}
        <Button
          title="Sign Up"
          onPress={handleSignUp}
          isLoading={isLoading} // Pass isLoading to the Button component
          disabled={isLoading}
          // variant="primary" // Explicitly set or rely on default
          className="mb-4" // Only margin needed here, internal Button styles handle the rest
        />

        {/* Consistent Link Area Styling */}
        <View className="flex-row justify-center items-center mt-6">
          <Text className="text-sm text-text-secondary">
            Already have an account?{' '}
          </Text>
          <Link href="/login" asChild>
            {/* Consistent Link Styling */}
            <Text className="text-sm font-semibold text-primary-500">
              Login
            </Text>
          </Link>
        </View>
      </View>
    </View>
  );
}