import React, { useState, useEffect, useCallback, useMemo, memo, createContext, useContext } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Platform,
  Alert,
  ActivityIndicator,
} from 'react-native';
import {
  useLocalSearchParams,
  Stack,
  useRouter,
  useFocusEffect,
} from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Feather } from '@expo/vector-icons';
import Animated, {
  FadeIn,
  FadeInDown,
  SlideInUp,
  FadeOutUp,
  Layout,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  // withTiming, // Not directly used for primary shared values in this component
  // Easing, // Not directly used for primary shared values in this component
  interpolateColor,
} from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';
import { useColorScheme } from 'nativewind';

import { useAuth } from '@/contexts/AuthContext';
import { getSignificantOtherById } from '@/services/profileService';
import {
  getProfileFeedback,
  deleteRecommendationFeedback,
  FeedbackEntry,
} from '@/services/recommendationService';
import { PROFILES_LAST_UPDATED_KEY } from '@/constants/storageKeys';
import {
  SignificantOtherProfile,
  WishlistItem,
  PastGiftGiven,
  GeneralNote,
  CustomDate,
  // Assuming these types might eventually include a unique 'id' string property
} from '@/functions/src/types/firestore';
import { Timestamp } from 'firebase/firestore';
import Button from '@/components/ui/Button';
import LoadingIndicator from '@/components/ui/LoadingIndicator';
import GiftTipsModal from '@/components/profile/GiftTipsModal';
import { formatFeedbackTimestamp } from '@/utils/dateUtils';

// --- Theme Color Placeholder (Ensure this aligns with your app's theme structure) ---
const AppColors = {
  primary: '#A3002B',
  primaryDark: '#D96D00',
  accent: '#007AFF', // Example accent
  accentDark: '#0A84FF',
  background: '#F9FAFB',
  backgroundDark: '#111827',
  card: '#FFFFFF',
  cardDark: '#1F2937', // A slightly lighter dark for cards
  border: '#E5E7EB',
  borderDark: '#374151',
  textPrimary: '#1F2937',
  textPrimaryDark: '#F9FAFB',
  textSecondary: '#6B7280',
  textSecondaryDark: '#9CA3AF',
  textDisabled: '#9CA3AF',
  textDisabledDark: '#6B7280',
  error: '#A3002B',
  errorDark: '#F87171',
  success: '#A3002B',
  successDark: '#4ADE80',
  warning: '#A3002B',
  warningDark: '#FBBF24',
  white: '#FFFFFF',
  black: '#000000',
};

// --- Helper Functions ---
const formatDate = (
  date: Date | null | undefined | Timestamp | FirebaseFirestore.Timestamp,
  format: 'long' | 'short' | 'monthDay' = 'long'
): string => {
  if (!date) return 'N/A';
  let dateObject: Date;
  if (date instanceof Timestamp) dateObject = date.toDate();
  else if (date instanceof Date) dateObject = date;
  else return 'N/A';

  if (format === 'short') return dateObject.toLocaleDateString();
  if (format === 'monthDay')
    return dateObject.toLocaleDateString(undefined, {
      month: 'long',
      day: 'numeric',
    });
  return dateObject.toLocaleDateString(undefined, {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
};

// Robust getInitials from ProfilesListScreen (or a shared util)
const getInitials = (name: string | undefined | null): string => {
  if (!name || typeof name !== 'string' || name.trim() === '') return '?';
  const names = name.trim().split(/\s+/).filter(Boolean);
  if (names.length === 0) return '?';
  let initials = names[0][0].toUpperCase();
  if (names.length > 1 && names[names.length - 1].length > 0) {
    initials += names[names.length - 1][0].toUpperCase();
  }
  return initials;
};

// MEDIUM 1: Refined profile strength calculation
const calculateProfileStrength = (
  profile: SignificantOtherProfile | null
): number => {
  if (!profile) return 0;
  let strength = 0;
  const MAX_ITEMS_FOR_POINTS = 3;
  const POINTS_PER_ITEM_ARRAY = 4; // Adjusted points
  const POINTS_PER_SIMPLE_FIELD = 5;
  const POINTS_PER_PREFERENCE = 7; // Adjusted points

  if (profile.birthday) strength += POINTS_PER_SIMPLE_FIELD;
  if (profile.anniversary) strength += POINTS_PER_SIMPLE_FIELD;
  strength +=
    Math.min(profile.interests?.length || 0, MAX_ITEMS_FOR_POINTS) *
    POINTS_PER_ITEM_ARRAY;
  strength +=
    Math.min(profile.dislikes?.length || 0, MAX_ITEMS_FOR_POINTS) *
    POINTS_PER_ITEM_ARRAY;
  if (profile.preferences?.favoriteColor) strength += POINTS_PER_PREFERENCE;
  if (profile.preferences?.preferredStyle) strength += POINTS_PER_PREFERENCE;
  // Award points for budget only if both min and max are present or a significant one is
  if (profile.preferences?.budgetMin && profile.preferences?.budgetMax)
    strength += POINTS_PER_PREFERENCE;
  else if (profile.preferences?.budgetMin || profile.preferences?.budgetMax)
    strength += Math.floor(POINTS_PER_PREFERENCE / 2); // Fewer for partial

  strength +=
    Math.min(
      profile.preferences?.favoriteBrands?.length || 0,
      MAX_ITEMS_FOR_POINTS
    ) * POINTS_PER_ITEM_ARRAY;
  if (profile.sizes?.clothing || profile.sizes?.shoe)
    strength += POINTS_PER_PREFERENCE; // Points if any size info

  strength +=
    Math.min(profile.wishlistItems?.length || 0, MAX_ITEMS_FOR_POINTS) *
    POINTS_PER_ITEM_ARRAY;
  strength +=
    Math.min(profile.pastGiftsGiven?.length || 0, MAX_ITEMS_FOR_POINTS) *
    POINTS_PER_ITEM_ARRAY;
  strength +=
    Math.min(profile.generalNotes?.length || 0, MAX_ITEMS_FOR_POINTS) *
    POINTS_PER_ITEM_ARRAY;
  strength +=
    Math.min(profile.customDates?.length || 0, MAX_ITEMS_FOR_POINTS) *
    POINTS_PER_ITEM_ARRAY;

  return Math.min(Math.round(strength), 100);
};

// ProfileContext for optimized data sharing
interface ThemedColors {
  primary: string;
  accent: string;
  background: string;
  card: string;
  border: string;
  textPrimary: string;
  textSecondary: string;
  textDisabled: string;
  error: string;
  warning: string;
  success: string;
  headerBackground: string;
  buttonPrimaryText: string;
  buttonSecondaryText: string;
}

interface ProfileContextType {
  profile: SignificantOtherProfile;
  profileStrength: number;
  themedColors: ThemedColors;
  isDark: boolean;
  profileId: string;
  router: any;
}

const ProfileContext = createContext<ProfileContextType | null>(null);

const useProfileContext = () => {
  const context = useContext(ProfileContext);
  if (!context) {
    throw new Error('useProfileContext must be used within ProfileProvider');
  }
  return context;
};

// Memoized ProfileHeader component
const ProfileHeader = memo(() => {
  const { profile, profileStrength, themedColors, isDark } = useProfileContext();

  const animatedStrength = useSharedValue(0);

  useEffect(() => {
    animatedStrength.value = withSpring(profileStrength, {
      damping: 15,
      stiffness: 100,
    });
  }, [profileStrength]);

  const strengthIndicatorStyle = useAnimatedStyle(() => {
    const color = interpolateColor(
      animatedStrength.value,
      [0, 33, 67, 100],
      [
        themedColors.error,
        themedColors.warning,
        themedColors.success,
        AppColors.successDark,
      ]
    );
    return { width: `${animatedStrength.value}%`, backgroundColor: color };
  });

  const strengthTextStyle = useAnimatedStyle(() => {
    const color = interpolateColor(
      animatedStrength.value,
      [0, 33, 67, 100],
      [
        themedColors.error,
        themedColors.warning,
        themedColors.success,
        AppColors.successDark,
      ]
    );
    return { color };
  });

  return (
    <Animated.View
      entering={FadeInDown.duration(500)}
      className={`p-6 rounded-3xl shadow-xl mb-6 border ${
        isDark
          ? 'bg-card-dark border-border-dark/30'
          : 'bg-card border-border/20'
      }`}
    >
      <View className="flex-row items-center mb-4">
        <View
          className={`w-24 h-24 rounded-full items-center justify-center mr-5 ${
            isDark ? 'bg-primary-dark/20' : 'bg-primary/10'
          }`}
        >
          <Text
            className="text-5xl font-bold"
            style={{ color: '#A3002B' }}
          >
            {getInitials(profile.name)}
          </Text>
        </View>
        <View className="flex-1">
          <Text
            className="text-3xl font-extrabold"
            style={{ color: themedColors.textPrimary }}
            numberOfLines={2}
          >
            {profile.name}
          </Text>
          <Text
            className="text-lg"
            style={{ color: themedColors.textSecondary }}
          >
            {profile.relationship || 'Connection'}
          </Text>
        </View>
      </View>
      <View className="my-3">
        <View className="flex-row justify-between items-center mb-1">
          <Text
            className="text-sm font-medium"
            style={{ color: themedColors.textSecondary }}
          >
            Profile Strength
          </Text>
          <Animated.Text
            style={strengthTextStyle}
            className="text-sm font-bold"
          >
            {profileStrength}%
          </Animated.Text>
        </View>
        <View
          className={`w-full h-2.5 rounded-full overflow-hidden ${
            isDark ? 'bg-border-dark' : 'bg-border'
          }`}
        >
          <Animated.View
            style={strengthIndicatorStyle}
            className="h-full rounded-full"
          />
        </View>
      </View>
      <View
        className={`flex-row justify-around pt-4 mt-4 border-t ${
          isDark ? 'border-border-dark' : 'border-border'
        }`}
      >
        {[
          {
            label: 'Birthday',
            date: profile.birthday,
            icon: 'gift' as keyof typeof Feather.glyphMap,
          },
          {
            label: 'Anniversary',
            date: profile.anniversary,
            icon: 'heart' as keyof typeof Feather.glyphMap,
          },
        ].map((item, idx) =>
          item.date ? (
            <View key={idx} className="items-center">
              <Feather
                name={item.icon}
                size={20}
                className="mb-1"
                color={'#A3002B'}
              />
              <Text
                className="text-xs font-medium"
                style={{ color: themedColors.textSecondary }}
              >
                {item.label}
              </Text>
              <Text
                className="text-sm"
                style={{ color: themedColors.textPrimary }}
              >
                {formatDate(item.date, 'monthDay')}
              </Text>
            </View>
          ) : null
        )}
      </View>
    </Animated.View>
  );
});

// Memoized ProfileField component
const ProfileField = memo(({
  label,
  value,
  iconName,
  delay = 0,
}: {
  label: string;
  value: React.ReactNode | string | number | boolean | null | undefined;
  iconName?: keyof typeof Feather.glyphMap;
  delay?: number;
}) => {
  const { themedColors } = useProfileContext();

  return (
    <Animated.View
      entering={FadeInDown.duration(400).delay(delay)}
      className="mb-4"
    >
      <View className="flex-row items-center mb-1">
        {iconName && (
          <Feather
            name={iconName}
            size={16}
            className="mr-2"
            color={themedColors.textSecondary}
          />
        )}
        <Text
          className="text-sm font-semibold"
          style={{ color: themedColors.textSecondary }}
        >
          {label}
        </Text>
      </View>
      {value && (
        <View className="ml-6">
          {typeof value === 'string' || typeof value === 'number' ? (
            <Text
              className="text-base"
              style={{ color: themedColors.textPrimary }}
            >
              {value}
            </Text>
          ) : (
            value
          )}
        </View>
      )}
    </Animated.View>
  );
});

// Memoized WelcomeCard component
const WelcomeCard = memo(({
  onDismiss,
  onStartAdding
}: {
  onDismiss: () => void;
  onStartAdding: () => void;
}) => {
  const { profile, themedColors, isDark } = useProfileContext();

  return (
    <Animated.View
      entering={FadeInDown.duration(500).delay(200)}
      exiting={FadeOutUp.duration(300)}
      className={`relative overflow-hidden rounded-2xl mb-6 ${
        isDark
          ? 'border bg-card-dark border-primary-dark/20'
          : 'border bg-card border-primary/15'
      }`}
      style={{
        shadowColor: themedColors.primary,
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: isDark ? 0.15 : 0.08,
        shadowRadius: 12,
        elevation: 6,
      }}
    >
      <View
        className="absolute top-0 right-0 left-0 h-1 rounded-t-2xl"
        style={{ backgroundColor: themedColors.primary }}
      />
      <View className="p-6">
        <View className="flex-row justify-between items-start mb-4">
          <View className="flex-row flex-1 items-center">
            <View
              className={`w-12 h-12 rounded-xl items-center justify-center mr-4 ${
                isDark ? 'bg-primary-dark/15' : 'bg-primary/10'
              }`}
            >
              <Text className="text-2xl">🎉</Text>
            </View>
            <View className="flex-1">
              <Text
                className="mb-1 text-lg font-bold"
                style={{ color: themedColors.textPrimary }}
              >
                Welcome to {profile.name}'s Hub!
              </Text>
              <Text
                className="text-sm font-medium"
                style={{ color: themedColors.primary }}
              >
                Profile successfully created
              </Text>
            </View>
          </View>
          <TouchableOpacity
            onPress={onDismiss}
            className={`w-8 h-8 rounded-full items-center justify-center ${
              isDark
                ? 'bg-background-dark/50 active:bg-background-dark/70'
                : 'bg-gray-100 active:bg-gray-200'
            }`}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          >
            <Feather
              name="x"
              size={14}
              color={themedColors.textSecondary}
            />
          </TouchableOpacity>
        </View>
        <View
          className={`p-4 rounded-xl mb-5 ${
            isDark ? 'bg-background-dark/30' : 'bg-gray-50'
          }`}
        >
          <Text
            className="mb-2 text-sm font-medium"
            style={{ color: themedColors.textPrimary }}
          >
            🎯 Ready for the next step?
          </Text>
          <Text
            className="text-sm leading-relaxed"
            style={{ color: themedColors.textSecondary }}
          >
            Add their interests, wishlist items, and preferences to
            unlock personalized gift recommendations that they'll
            actually love.
          </Text>
        </View>
        <View className="space-y-3">
          <Button
            title="Start Adding Details"
            onPress={onStartAdding}
            variant="primary"
            leftIcon={
              <Feather
                name="edit-3"
                size={16}
                color={themedColors.buttonPrimaryText}
              />
            }
          />
        </View>
      </View>
    </Animated.View>
  );
});

const AnimatedTouchableOpacity =
  Animated.createAnimatedComponent(TouchableOpacity);

const ProfileViewScreen = () => {
  const { profileId, isNew } = useLocalSearchParams<{
    profileId: string;
    isNew?: string;
  }>();
  const { user } = useAuth(); // Assuming useAuth might provide isAuthLoading
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const router = useRouter();

  // --- Themed Colors ---
  const themedColors = useMemo(
    () => ({
      primary: isDark ? AppColors.primaryDark : AppColors.primary,
      accent: isDark ? AppColors.accentDark : AppColors.accent,
      background: isDark ? AppColors.backgroundDark : AppColors.background,
      card: isDark ? AppColors.cardDark : AppColors.card,
      border: isDark ? AppColors.borderDark : AppColors.border,
      textPrimary: isDark ? AppColors.textPrimaryDark : AppColors.textPrimary,
      textSecondary: isDark
        ? AppColors.textSecondaryDark
        : AppColors.textSecondary,
      textDisabled: isDark
        ? AppColors.textDisabledDark
        : AppColors.textDisabled,
      error: isDark ? AppColors.errorDark : AppColors.error,
      success: isDark ? AppColors.successDark : AppColors.success,
      warning: isDark ? AppColors.warningDark : AppColors.warning,
      buttonPrimaryText: isDark ? AppColors.black : AppColors.white, // Example
      buttonSecondaryText: isDark ? AppColors.white : AppColors.black, // Example
      headerTitle: isDark ? AppColors.primaryDark : AppColors.primary,
      headerBackground: isDark
        ? AppColors.backgroundDark
        : AppColors.background,
    }),
    [isDark]
  );

  const [profile, setProfile] = useState<SignificantOtherProfile | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [feedbackList, setFeedbackList] = useState<FeedbackEntry[]>([]);
  // feedbackLoading and feedbackError are not used in provided snippet, keep if needed for future.
  // const [feedbackLoading, setFeedbackLoading] = useState<boolean>(false);
  // const [feedbackError, setFeedbackError] = useState<string | null>(null);
  const [isTipsModalVisible, setIsTipsModalVisible] = useState(false);
  const [showWelcomeCard, setShowWelcomeCard] = useState(isNew === 'true');
  const [lastFetchTimestamp, setLastFetchTimestamp] = useState<number>(0);

  const profileStrength = useMemo(
    () => calculateProfileStrength(profile),
    [profile]
  );
  const animatedStrength = useSharedValue(0);

  useEffect(() => {
    animatedStrength.value = withSpring(profileStrength, {
      damping: 15,
      stiffness: 100,
    });
  }, [profileStrength]); // LOW 1: Removed animatedStrength from deps

  const strengthIndicatorStyle = useAnimatedStyle(() => {
    const color = interpolateColor(
      animatedStrength.value,
      [0, 33, 67, 100],
      [
        themedColors.error,
        themedColors.warning,
        themedColors.success,
        AppColors.successDark,
      ] // Use themed colors
    );
    return { width: `${animatedStrength.value}%`, backgroundColor: color };
  });
  const strengthTextStyle = useAnimatedStyle(() => {
    const color = interpolateColor(
      animatedStrength.value,
      [0, 33, 67, 100],
      [
        themedColors.error,
        themedColors.warning,
        themedColors.success,
        AppColors.successDark,
      ]
    );
    return { color };
  });

  const isProfileSparse = useMemo(
    () => profile && profileStrength < 50,
    [profile, profileStrength]
  );
  const ideaSparkers = [
    'Tech Gadgets',
    'Experiences',
    'Books & Hobbies',
    'Fashion',
    'Home Decor',
    'Wellness',
  ];

  const fetchData = useCallback(async () => {
    if (!user?.uid || !profileId) {
      setIsLoading(false);
      setError(
        !profileId ? 'Profile ID is missing.' : 'User not authenticated.'
      );
      setProfile(null); // Clear profile if invalid params
      setFeedbackList([]); // Clear feedback if invalid params
      return;
    }
    setIsLoading(true);
    setError(null);
    setProfile(null);
    setFeedbackList([]);

    try {
      const [fetchedProfile, feedback] = await Promise.all([
        getSignificantOtherById(user.uid, profileId),
        getProfileFeedback(profileId), // Assuming this doesn't need user.uid
      ]);
      if (fetchedProfile) setProfile(fetchedProfile);
      else setError('Profile not found or access denied.');
      setFeedbackList(feedback);
      // Update the last fetch timestamp after successful fetch
      setLastFetchTimestamp(Date.now());
    } catch (err) {
      console.error('Error fetching data:', err);
      setError(
        err instanceof Error ? err.message : 'Failed to fetch profile details.'
      );
      // Ensure states are cleared on error
      setProfile(null);
      setFeedbackList([]);
    } finally {
      setIsLoading(false);
    }
  }, [profileId, user?.uid]); // Removed state setters from deps

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // Use useFocusEffect to check for profile updates and refresh if needed
  useFocusEffect(
    useCallback(() => {
      const checkForProfileUpdates = async () => {
        try {
          const lastUpdatedString = await AsyncStorage.getItem(
            PROFILES_LAST_UPDATED_KEY
          );
          const lastUpdatedTimestamp = lastUpdatedString
            ? parseInt(lastUpdatedString, 10)
            : 0;

          // If the profile was updated after our last fetch, refresh the data
          if (lastUpdatedTimestamp > lastFetchTimestamp) {
            console.log('Profile updates detected, refreshing profile data...');
            fetchData();
          }
        } catch (error) {
          console.error('Error checking for profile updates:', error);
        }
      };

      // Only check for updates if the profile has been loaded at least once
      if (lastFetchTimestamp > 0) {
        checkForProfileUpdates();
      }
    }, [lastFetchTimestamp, fetchData])
  );

  const handleDeleteFeedback = useCallback((feedbackId: string) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    Alert.alert(
      'Delete Feedback',
      'Are you sure you want to delete this feedback?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          // MEDIUM 4: Wrapped in try/catch
          onPress: async () => {
            try {
              const success = await deleteRecommendationFeedback(feedbackId);
              if (success) {
                setFeedbackList((prev) =>
                  prev.filter((f) => f.id !== feedbackId)
                );
                Haptics.notificationAsync(
                  Haptics.NotificationFeedbackType.Success
                );
              } else {
                // This case implies the service function returned false, not an error.
                Alert.alert(
                  'Error',
                  'Failed to delete feedback. The server indicated a problem.'
                );
                Haptics.notificationAsync(
                  Haptics.NotificationFeedbackType.Error
                );
              }
            } catch (deletionError) {
              console.error('Error deleting feedback:', deletionError);
              Alert.alert(
                'Error',
                deletionError instanceof Error
                  ? deletionError.message
                  : 'An unexpected error occurred during deletion.'
              );
              Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
            }
          },
        },
      ]
    );
  }, []);

  const handleDismissWelcomeCard = useCallback(() => {
    setShowWelcomeCard(false);
    // LOW 5: TODO: Implement persistence for welcome card dismissal
    // Example: AsyncStorage.setItem(`dismissedWelcome_${profileId}`, 'true');
  }, []);

  const handleStartAddingDetails = useCallback(() => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    router.push(`/(app)/profiles/${profileId}/edit`);
    handleDismissWelcomeCard();
  }, [profileId, router, handleDismissWelcomeCard]);

  const Section = ({
    title,
    iconName,
    children,
    initiallyOpen = true,
    cta,
    ctaOnPress,
    ctaVariant = 'primary',
    emptyTitle,
    emptyMessage,
    isEmpty,
  }: {
    title: string;
    iconName?: keyof typeof Feather.glyphMap;
    children: React.ReactNode;
    initiallyOpen?: boolean;
    cta?: string;
    ctaOnPress?: () => void;
    ctaVariant?: 'primary' | 'secondary';
    emptyTitle?: string;
    emptyMessage?: string;
    isEmpty?: boolean;
  }) => {
    const [isOpen, setIsOpen] = useState(initiallyOpen);
    // HIGH 1: Removed unused animatedHeight. Height animation is deferred.
    // The Layout.springify() on the parent Animated.View animates layout changes BETWEEN sections.
    // Content reveal is handled by FadeInDown on the content wrapper.
    const toggleOpen = () => {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      setIsOpen(!isOpen);
    };
    const cardBaseClass = `rounded-2xl shadow-lg mb-6 overflow-hidden border`;
    const themedCardClass = isDark
      ? `bg-card-dark border-border-dark/30`
      : `bg-card border-border/20`;
    const themedBorderClass = isDark
      ? `border-border-dark/50`
      : `border-border/30`;

    // LOW 4: Button icon color should ideally be handled by Button component or themed prop
    const ctaIconColor =
      ctaVariant === 'primary'
        ? themedColors.buttonPrimaryText
        : themedColors.buttonSecondaryText;

    return (
      <Animated.View
        layout={Layout.springify()}
        className={`${cardBaseClass} ${themedCardClass}`}
      >
        <TouchableOpacity
          activeOpacity={0.8}
          onPress={toggleOpen}
          className={`flex-row justify-between items-center p-5 border-b ${themedBorderClass}`}
        >
          <View className="flex-row items-center">
            {iconName && (
              <Feather
                name={iconName}
                size={22}
                className="mr-3"
                color={'#A3002B'}
              />
            )}
            <Text
              className="text-xl font-bold"
              style={{ color: themedColors.textPrimary }}
            >
              {title}
            </Text>
          </View>
          <Feather
            name={isOpen ? 'chevron-down' : 'chevron-up'}
            size={24}
            color={themedColors.textSecondary}
          />
        </TouchableOpacity>
        {isOpen && (
          <Animated.View
            entering={FadeInDown.duration(300).delay(100)}
            className="p-5"
          >
            {isEmpty ? (
              <View className="items-center py-6 text-center">
                <Feather
                  name={iconName || 'alert-circle'}
                  size={36}
                  className="mb-3"
                  color={themedColors.textDisabled}
                />
                <Text
                  className="mb-1 text-lg font-semibold"
                  style={{ color: themedColors.textPrimary }}
                >
                  {emptyTitle || 'Nothing here yet!'}
                </Text>
                <Text
                  className="mb-4"
                  style={{ color: themedColors.textSecondary }}
                >
                  {emptyMessage || 'Add some details to enrich this profile.'}
                </Text>
                {cta && ctaOnPress && (
                  <Button
                    title={cta}
                    onPress={ctaOnPress}
                    variant={ctaVariant || 'primary'}
                    leftIcon={
                      <Feather
                        name="plus-circle"
                        size={18}
                        color={ctaIconColor}
                      />
                    }
                  />
                )}
              </View>
            ) : (
              children
            )}
          </Animated.View>
        )}
      </Animated.View>
    );
  };

  const ProfileField = ({
    label,
    value,
    iconName,
    delay = 0,
  }: {
    label: string;
    value: React.ReactNode | string | number | boolean | null | undefined; // Expanded type
    iconName?: keyof typeof Feather.glyphMap;
    delay?: number;
  }) => (
    <Animated.View
      entering={FadeInDown.duration(400).delay(delay)}
      className="mb-4"
    >
      <View className="flex-row items-center mb-1">
        {iconName && (
          <Feather
            name={iconName}
            size={16}
            className="mr-2"
            color={themedColors.textSecondary}
          />
        )}
        <Text
          className="text-sm font-semibold"
          style={{ color: themedColors.textSecondary }}
        >
          {label}
        </Text>
      </View>

      {value !== null && value !== undefined && value !== '' ? (
        typeof value === 'string' ||
        typeof value === 'number' ||
        typeof value === 'boolean' ? (
          <Text
            className="text-base"
            style={{ color: themedColors.textPrimary }}
          >
            {String(value)}
          </Text>
        ) : (
          value
        )
      ) : (
        <Text
          className="text-base italic"
          style={{ color: themedColors.textDisabled }}
        >
          Not specified
        </Text>
      )}
    </Animated.View>
  );

  // MEDIUM 3: renderListItem key generation.
  // Ideal solution: Ensure WishlistItem, PastGiftGiven, GeneralNote types from Firestore
  // include a unique 'id' string property (e.g., Firestore document ID).
  // Then use `item.id` for the key.
  // For now, using a composite key with index as a fallback.
  const renderListItem = (
    item: WishlistItem | PastGiftGiven | GeneralNote,
    index: number,
    type: 'wish' | 'gift' | 'note'
  ) => {
    let title = '',
      description = '',
      dateText = '',
      icon: keyof typeof Feather.glyphMap = 'gift';
    // Attempt to create a more unique key if item has a name/note property, otherwise fallback to index.
    // This is still not ideal if names/notes are not unique.
    let itemIdentifier = (item as any).item || (item as any).note || index;
    let itemKey = `${type}-${itemIdentifier}-${index}`; // More specific key

    if ('item' in item && 'dateAdded' in item) {
      // WishlistItem
      icon = 'star';
      title = item.item;
      description = item.notes || '';
      dateText = `Added: ${formatDate(item.dateAdded, 'short')}`;
    } else if ('item' in item && 'date' in item && 'occasion' in item) {
      // PastGiftGiven
      icon = 'archive';
      title = item.item;
      description = item.occasion || '';
      dateText = `Given: ${formatDate(item.date, 'short')}`;
    } else if ('note' in item && 'date' in item) {
      // GeneralNote
      icon = 'file-text';
      title = item.note;
      dateText = `Created: ${formatDate(item.date, 'short')}`;
    }

    return (
      <Animated.View
        key={itemKey} // MEDIUM 3: Key generation improved but unique item IDs are best.
        entering={FadeInDown.duration(300).delay(index * 70)}
        layout={Layout.springify()}
        className={`p-4 mb-3 rounded-xl flex-row items-start space-x-3 border ${
          isDark
            ? 'bg-background-dark border-border-dark'
            : 'bg-background border-border'
        }`}
      >
        <Feather
          name={icon}
          size={20}
          className="mt-1"
          color={themedColors.accent}
        />
        <View className="flex-1">
          <Text
            className="text-base font-semibold"
            style={{ color: themedColors.textPrimary }}
          >
            {title || 'N/A'}
          </Text>
          {description && (
            <Text
              className="mt-0.5 text-sm"
              style={{ color: themedColors.textSecondary }}
            >
              {description}
            </Text>
          )}
          {dateText && (
            <Text
              className="mt-1 text-xs"
              style={{ color: themedColors.textDisabled }}
            >
              {dateText}
            </Text>
          )}
        </View>
      </Animated.View>
    );
  };

  const renderCustomDateItem = (item: CustomDate, index: number) => {
    // MEDIUM 3: Assuming CustomDate has a unique `item.id` from Firestore.
    const key = `custom-date-${item.id || index}`;
    const dateDisplay = item.date
      ? formatDate(item.date, 'monthDay')
      : item.customDateMonthDay || 'N/A';
    const itemColor = themedColors.primary; // LOW 6: Use themed default color

    return (
      <Animated.View
        key={key}
        entering={FadeInDown.duration(300).delay(index * 80)}
        className={`p-4 mb-3 rounded-xl flex-row items-center space-x-3 border ${
          isDark
            ? 'bg-background-dark border-border-dark'
            : 'bg-background border-border'
        } border-l-4`}
        style={{ borderLeftColor: itemColor }}
      >
        <Feather
          name="calendar"
          size={20}
          className="mt-0.5"
          color={themedColors.accent}
        />
        <View className="flex-1">
          <Text
            className="text-base font-semibold"
            style={{ color: themedColors.textPrimary }}
          >
            {item.name || 'Unnamed Date'}
          </Text>
          <Text
            className="mt-0.5 text-sm"
            style={{ color: themedColors.textSecondary }}
          >
            {dateDisplay}
          </Text>
        </View>
      </Animated.View>
    );
  };

  const renderFeedbackItem = (
    item: FeedbackEntry,
    index: number,
    onDelete: (id: string) => void
  ) => {
    const isLike = item.feedbackType === 'like';
    const iconColor = isLike ? themedColors.success : themedColors.error;
    const borderColorClass = isLike
      ? isDark
        ? 'border-success-dark'
        : 'border-success'
      : isDark
      ? 'border-error-dark'
      : 'border-error';
    const bgColorClass = isLike
      ? isDark
        ? 'bg-success-dark/10'
        : 'bg-success/10'
      : isDark
      ? 'bg-error-dark/10'
      : 'bg-error/10';

    return (
      <Animated.View
        key={`feedback-${item.id}-${index}`} // item.id should be unique
        entering={FadeInDown.duration(300).delay(index * 80)}
        layout={Layout.springify()}
        className={`flex-row items-center p-4 mb-3 space-x-3 rounded-xl border-l-4 ${bgColorClass} ${borderColorClass}`}
      >
        <Feather
          name={isLike ? 'thumbs-up' : 'thumbs-down'}
          size={20}
          style={{ color: iconColor }}
        />
        <View className="flex-1">
          <Text
            className="font-semibold"
            style={{ color: themedColors.textPrimary }}
          >
            {item.recommendationDetails?.name || 'Recommendation'}
          </Text>
          <Text
            className="mt-1 text-xs"
            style={{ color: themedColors.textDisabled }}
          >
            {formatFeedbackTimestamp(item.timestamp)}
          </Text>
        </View>
        <AnimatedTouchableOpacity
          onPress={() => onDelete(item.id)}
          accessibilityLabel="Delete feedback"
          className={`p-2 rounded-full ${
            isDark ? 'active:bg-error-dark/30' : 'active:bg-error/20'
          }`}
        >
          <Feather name="trash-2" size={18} color={themedColors.error} />
        </AnimatedTouchableOpacity>
      </Animated.View>
    );
  };

  if (isLoading) {
    return (
      <SafeAreaView
        className="flex-1 justify-center items-center"
        style={{ backgroundColor: themedColors.background }}
      >
        <LoadingIndicator color={'#A3002B'} size="large" />
        <Text
          className="mt-4 text-lg"
          style={{ color: themedColors.textSecondary }}
        >
          Loading Profile...
        </Text>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView
        className="flex-1 justify-center items-center p-6"
        style={{ backgroundColor: themedColors.background }}
      >
        <Feather name="alert-triangle" size={48} color={themedColors.error} />
        <Text
          className="mt-4 mb-2 text-xl font-semibold text-center"
          style={{ color: themedColors.textPrimary }}
        >
          Oops!
        </Text>
        <Text
          className="mb-6 text-center"
          style={{ color: themedColors.error }}
        >
          {error}
        </Text>
        <Button
          title="Try Again"
          onPress={fetchData}
          variant="primary"
          leftIcon={
            <Feather
              name="refresh-cw"
              size={18}
              color={themedColors.buttonPrimaryText}
            />
          }
        />
      </SafeAreaView>
    );
  }

  if (!profile) {
    return (
      <SafeAreaView
        className="flex-1 justify-center items-center p-6"
        style={{ backgroundColor: themedColors.background }}
      >
        <Feather name="user-x" size={48} color={themedColors.textDisabled} />
        <Text
          className="mt-4 text-lg"
          style={{ color: themedColors.textDisabled }}
        >
          Profile data unavailable.
        </Text>
      </SafeAreaView>
    );
  }

  // Context value for ProfileProvider
  const contextValue = useMemo(() => ({
    profile,
    profileStrength,
    themedColors,
    isDark,
    profileId: profileId as string,
    router,
  }), [profile, profileStrength, themedColors, isDark, profileId, router]);

  return (
    <ProfileContext.Provider value={contextValue}>
      <SafeAreaView
        edges={['bottom', 'left', 'right']}
        className="flex-1"
        style={{ backgroundColor: themedColors.background }}
      >
        <Stack.Screen
          options={{
            headerLargeTitle: true,
            headerLargeTitleStyle: { color: '#A3002B' },
            title: profile.name, // Keep dynamic title
            headerTitleStyle: { color: '#A3002B', fontWeight: '600' },
            headerStyle: { backgroundColor: themedColors.headerBackground },
            headerShadowVisible: false,
            headerRight: () => (
              <AnimatedTouchableOpacity
                onPress={() => {
                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
                  router.push(`/(app)/profiles/${profileId}/edit`);
                }}
                className={`flex-row items-center p-2 mr-1 rounded-lg ${
                  isDark ? 'active:bg-accent-dark/20' : 'active:bg-accent/10'
                }`}
                accessibilityRole="button"
                accessibilityLabel={`Edit ${profile.name}'s profile`}
              >
                <Feather name="edit-2" size={14} color={'#A3002B'} />
                <Text
                  className="ml-1.5 font-semibold"
                  style={{ color: '#A3002B' }}
                >
                  Edit
                </Text>
              </AnimatedTouchableOpacity>
            ),
          }}
        />

      <ScrollView
        contentContainerClassName="px-4 pt-2 pb-20"
        showsVerticalScrollIndicator={false}
      >
        <Animated.View className="space-y-1">
          <ProfileHeader />

          {showWelcomeCard && (
            <WelcomeCard
              onDismiss={handleDismissWelcomeCard}
              onStartAdding={handleStartAddingDetails}
            />
          )}

          <Section
            title="Tastes & Preferences"
            iconName="smile"
            isEmpty={
              (!profile.interests || profile.interests.length === 0) &&
              (!profile.dislikes || profile.dislikes.length === 0)
            }
            emptyTitle="Uncover Their Tastes"
            emptyMessage="What do they love? What should be avoided?"
            cta="Add Tastes"
            ctaOnPress={() =>
              router.push(
                `/(app)/profiles/${profileId}/edit?focusSection=interests`
              )
            }
          >
            <ProfileField
              label="Interests"
              iconName="heart"
              value={
                profile.interests && profile.interests.length > 0 ? (
                  <View className="flex-row flex-wrap gap-2 mt-1">
                    {profile.interests.map((interest, i) => (
                      <View
                        key={`int-${interest}-${i}`}
                        className={`px-3 py-1.5 rounded-full ${
                          isDark ? 'bg-accent-dark/20' : 'bg-accent/10'
                        }`}
                      >
                        <Text
                          className="text-sm font-medium"
                          style={{ color: '#A3002B' }}
                        >
                          {interest}
                        </Text>
                      </View>
                    ))}
                  </View>
                ) : null
              }
            />
            <ProfileField
              label="Dislikes"
              iconName="thumbs-down"
              value={
                profile.dislikes && profile.dislikes.length > 0 ? (
                  <View className="flex-row flex-wrap gap-2 mt-1">
                    {profile.dislikes.map((dislike, i) => (
                      <View
                        key={`dis-${dislike}-${i}`}
                        className={`px-3 py-1.5 rounded-full ${
                          isDark ? 'bg-error-dark/20' : 'bg-error/10'
                        }`}
                      >
                        <Text
                          className="text-sm font-medium"
                          style={{ color: themedColors.error }}
                        >
                          {dislike}
                        </Text>
                      </View>
                    ))}
                  </View>
                ) : null
              }
            />
          </Section>

          <Section
            title="Style Profile"
            iconName="shopping-bag"
            isEmpty={
              !profile.preferences?.favoriteColor &&
              !profile.preferences?.preferredStyle &&
              !profile.sizes?.clothing &&
              !profile.sizes?.shoe &&
              (!profile.preferences?.favoriteBrands ||
                profile.preferences.favoriteBrands.length === 0)
            }
            emptyTitle="Define Their Style"
            emptyMessage="Colors, clothing style, sizes – these clues unlock gifts."
            cta="Add Style Details"
            ctaOnPress={() =>
              router.push(
                `/(app)/profiles/${profileId}/edit?focusSection=preferences`
              )
            }
          >
            <View className="grid grid-cols-2 gap-x-4"></View>
            <ProfileField
              label="Budget Range"
              iconName="dollar-sign"
              value={
                profile.preferences?.budgetMin || profile.preferences?.budgetMax
                  ? `$${profile.preferences?.budgetMin || '0'} - $${
                      profile.preferences?.budgetMax || 'Any'
                    }`
                  : null
              }
            />
            <ProfileField
              label="Favorite Brands"
              iconName="award"
              value={
                profile.preferences?.favoriteBrands &&
                profile.preferences.favoriteBrands.length > 0 ? (
                  <View className="flex-row flex-wrap gap-2 mt-1">
                    {profile.preferences.favoriteBrands.map((brand, i) => (
                      <View
                        key={`brand-${brand}-${i}`}
                        className={`px-3 py-1.5 rounded-full ${
                          isDark ? 'bg-primary-dark/20' : 'bg-primary/10'
                        }`}
                      >
                        <Text
                          className="text-sm font-medium"
                          style={{ color: themedColors.primary }}
                        >
                          {brand}
                        </Text>
                      </View>
                    ))}
                  </View>
                ) : null
              }
            />
          </Section>

          <Section
            title="Wishlist"
            iconName="gift"
            isEmpty={
              !profile.wishlistItems || profile.wishlistItems.length === 0
            }
            emptyTitle="Curate Their Dreams"
            emptyMessage="What are they secretly hoping for?"
            cta="Add to Wishlist"
            ctaOnPress={() =>
              router.push(
                `/(app)/profiles/${profileId}/edit?focusSection=wishlist`
              )
            }
          >
            {profile.wishlistItems?.map((item, idx) =>
              renderListItem(item, idx, 'wish')
            )}
          </Section>

          <Section
            title="Past Gifts"
            iconName="archive"
            isEmpty={
              !profile.pastGiftsGiven || profile.pastGiftsGiven.length === 0
            }
            emptyTitle="Recall Past Triumphs"
            emptyMessage="Record gifts given to avoid repeats."
            cta="Log a Past Gift"
            ctaOnPress={() =>
              router.push(
                `/(app)/profiles/${profileId}/edit?focusSection=pastGifts`
              )
            }
          >
            {profile.pastGiftsGiven?.map((item, idx) =>
              renderListItem(item, idx, 'gift')
            )}
          </Section>

          <Section
            title="General Notes"
            iconName="file-text"
            isEmpty={!profile.generalNotes || profile.generalNotes.length === 0}
            emptyTitle="Jot Down Insights"
            emptyMessage="Any random thoughts or important details."
            cta="Add a Note"
            ctaOnPress={() =>
              router.push(
                `/(app)/profiles/${profileId}/edit?focusSection=generalNotes`
              )
            }
          >
            {profile.generalNotes?.map((item, idx) =>
              renderListItem(item, idx, 'note')
            )}
          </Section>

          <Section
            title="Custom Dates"
            iconName="calendar"
            isEmpty={!profile.customDates || profile.customDates.length === 0}
            emptyTitle="Mark Special Moments"
            emptyMessage="Other significant dates beyond basics."
            cta="Add Custom Date"
            ctaOnPress={() =>
              router.push(
                `/(app)/profiles/${profileId}/edit?focusSection=customDates`
              )
            }
          >
            {profile.customDates?.map(renderCustomDateItem)}
          </Section>

          {isProfileSparse && (
            <Animated.View entering={FadeIn.duration(500).delay(300)}>
              <Section
                title="Need Inspiration?"
                iconName="zap"
                initiallyOpen={true}
              >
                <Text
                  className="mb-4 text-base"
                  style={{ color: themedColors.textSecondary }}
                >
                  This profile is still blossoming! Adding more details unlocks
                  tailored recommendations. Spark your imagination:
                </Text>
                <View className="flex-row flex-wrap gap-3 justify-center mb-5">
                  {ideaSparkers.map((sparker, index) => (
                    <View
                      key={`sparker-${index}`}
                      className={`px-3.5 py-2 rounded-lg ${
                        isDark ? 'bg-primary-dark/15' : 'bg-primary/10'
                      }`}
                    >
                      <Text
                        className="font-medium"
                        style={{ color: themedColors.primary }}
                      >
                        {sparker}
                      </Text>
                    </View>
                  ))}
                </View>
                <Button
                  title="Enhance Profile Now"
                  onPress={() => {
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                    router.push(`/(app)/profiles/${profileId}/edit`);
                  }}
                  variant="primary"
                  leftIcon={
                    <Feather
                      name="edit"
                      size={18}
                      color={themedColors.buttonPrimaryText}
                    />
                  }
                />
                <Button
                  title="View General Gift Tips"
                  onPress={() => {
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                    setIsTipsModalVisible(true);
                  }}
                  variant="secondary"
                  className="mt-3"
                  leftIcon={
                    <Feather
                      name="compass"
                      size={18}
                      color={themedColors.buttonSecondaryText}
                    />
                  }
                />
              </Section>
            </Animated.View>
          )}

          <Section
            title="Recommendation Feedback"
            iconName="message-circle"
            initiallyOpen={false}
            isEmpty={!feedbackList || feedbackList.length === 0}
            emptyTitle="No Feedback Yet"
            emptyMessage={`Your likes/dislikes on suggestions help Giftmi learn what ${profile.name} might love!`}
          >
            {(!feedbackList || feedbackList.length === 0) &&
            !error /* && !feedbackLoading -- if exists */ ? (
              <View className="items-center py-6 text-center">
                <Feather
                  name="message-square"
                  size={36}
                  className="mb-3"
                  color={themedColors.textDisabled}
                />
                <Text
                  className="mb-1 text-lg font-semibold"
                  style={{ color: themedColors.textPrimary }}
                >
                  No feedback recorded.
                </Text>
                <Text
                  className="mb-4"
                  style={{ color: themedColors.textSecondary }}
                >
                  Feedback on gift ideas will appear here.
                </Text>
              </View>
            ) : (
              feedbackList?.map((item, idx) =>
                renderFeedbackItem(item, idx, handleDeleteFeedback)
              )
            )}
          </Section>
          <View className="w-full h-full">
            <GiftTipsModal
              isVisible={isTipsModalVisible}
              onClose={() => setIsTipsModalVisible(false)}
            />
          </View>
        </Animated.View>
      </ScrollView>
    </SafeAreaView>
    </ProfileContext.Provider>
  );
};

export default ProfileViewScreen;
