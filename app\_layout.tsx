import React, { useState, useEffect } from 'react';
import 'react-native-get-random-values';
import { Stack, useRouter, SplashScreen } from "expo-router";
import { useFonts } from 'expo-font';
import { SafeAreaProvider } from "react-native-safe-area-context";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import { AuthProvider, useAuth } from "../contexts/AuthContext";
import LoadingIndicator from "../components/ui/LoadingIndicator";
import "../global.css";
import AsyncStorage from '@react-native-async-storage/async-storage';
import { STORAGE_KEYS } from '../constants/storageKeys';

// Prevent the splash screen from auto-hiding before asset loading is complete.
SplashScreen.preventAutoHideAsync();

function RootLayoutNav() {
  const { user, isLoading: authLoading } = useAuth();
  const router = useRouter();
  const [onboardingLoading, setOnboardingLoading] = useState(true);
  const [hasOnboarded, setHasOnboarded] = useState(false);

  useEffect(() => {
    const checkOnboardingStatus = async () => {
      try {
        const value = await AsyncStorage.getItem(STORAGE_KEYS.HAS_COMPLETED_ONBOARDING);
        if (value !== null) {
          setHasOnboarded(true);
        }
      } catch (e) {
        console.error('Failed to load onboarding status', e);
      } finally {
        setOnboardingLoading(false);
      }
    };

    checkOnboardingStatus();
  }, []);

  useEffect(() => {
    const isLoading = authLoading || onboardingLoading;
    if (isLoading) {
      return; // Don't navigate until everything is loaded
    }
    
    SplashScreen.hideAsync(); // Hide the splash screen now that we're ready

    if (!hasOnboarded) {
      router.replace('/(onboarding)/' as any);
    } else if (user) {
      router.replace('/(app)/home');
    } else {
      router.replace('/(auth)/login');
    }
  }, [user, authLoading, onboardingLoading, hasOnboarded, router]);
  
  // This component must return a navigator. The useEffect above will handle the redirection.
  return (
    <Stack screenOptions={{ headerShown: false }}>
      <Stack.Screen name="(app)" options={{ headerShown: false }}/>
      <Stack.Screen name="(auth)" options={{ headerShown: false }}/>
      <Stack.Screen name="(onboarding)" options={{ headerShown: false }}/>
      <Stack.Screen name="index" options={{ headerShown: false }}/>
    </Stack>
  );
}

export default function RootLayout() {
  const [loaded, error] = useFonts({
    'SpaceMono': require('../assets/fonts/SpaceMono-Regular.ttf'),
  });

  useEffect(() => {
    if (error) throw error;
  }, [error]);

  if (!loaded) {
    return null;
  }
  
  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
        <SafeAreaProvider>
            <AuthProvider>
                <RootLayoutNav />
            </AuthProvider>
        </SafeAreaProvider>
    </GestureHandlerRootView>
  );
}
