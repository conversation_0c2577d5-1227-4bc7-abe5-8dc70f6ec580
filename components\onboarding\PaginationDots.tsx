import React from "react";
import { View, StyleSheet, Animated } from "react-native";
import { useColorScheme } from "nativewind";
import { colors } from "../../constants/Colors";

interface PaginationDotsProps {
  data: any[];
  scrollX: Animated.Value;
  screenWidth: number;
}

const PaginationDots: React.FC<PaginationDotsProps> = ({ data, scrollX, screenWidth }) => {
    const { colorScheme } = useColorScheme();
    const isDark = colorScheme === 'dark';

    const themedColors = {
        active: isDark ? colors.dark.primary : colors.light.primary,
        inactive: isDark ? colors.dark.disabled : colors.light.disabled,
    };

  return (
    <View style={styles.container}>
      {data.map((_, index) => {
        const inputRange = [(index - 1) * screenWidth, index * screenWidth, (index + 1) * screenWidth];

        const dotColor = scrollX.interpolate({
          inputRange,
          outputRange: [themedColors.inactive, themedColors.active, themedColors.inactive],
          extrapolate: "clamp",
        });

        const scale = scrollX.interpolate({
            inputRange,
            outputRange: [0.8, 1.2, 0.8],
            extrapolate: 'clamp',
        });

        return (
          <Animated.View
            key={index.toString()}
            style={[styles.dot, { transform: [{ scale }] , backgroundColor: dotColor}]}
          />
        );
      })}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    position: "absolute",
    bottom: 30,
    alignSelf: 'center',
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginHorizontal: 4,
  },
});

export default PaginationDots; 