import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  View,
  Text,
  ScrollView,
  Switch,
  Platform,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { Stack } from 'expo-router'; // For screen title
import { useThemeManager } from '@/hooks/useThemeManager';
import Card from '@/components/ui/Card';
import { useAuth } from '@/contexts/AuthContext';
import {
  getUserSettings,
  updateUserSettings,
} from '@/services/firestoreService';
import { UserSettings } from '@/types/firestore';
import { serverTimestamp } from 'firebase/firestore';

// Import resolveConfig and your Tailwind config
import resolveConfig from 'tailwindcss/resolveConfig';
// --- IMPORTANT: Adjust this path if necessary. ---
// From /c:/github/Giftmi/app/(app)/settings/notifications.tsx
// To   /c:/github/Giftmi/tailwind.config.js
// This should be:
import tailwindConfig from '../../../tailwind.config.js'; // Adjusted path

const fullConfig = resolveConfig(tailwindConfig);

// --- TypeScript type definitions for your custom theme colors ---
type ColorValue = string;

// For colors with DEFAULT, dark, and possibly numeric shades (e.g., primary, accent)
interface ColorPalette {
  DEFAULT: ColorValue;
  500?: ColorValue; // Optional, as not all palettes might have them
  600?: ColorValue; // Optional
  dark: ColorValue;
}

// For colors with just DEFAULT and dark variants (e.g., background, text-primary)
interface SimpleColorPalette {
  DEFAULT: ColorValue;
  dark: ColorValue;
}

// Define the shape of your extended colors
interface PomegranateExtendedColors {
  primary: ColorPalette;
  accent: ColorPalette;
  background: SimpleColorPalette;
  card: SimpleColorPalette;
  'text-primary': SimpleColorPalette;
  'text-secondary': SimpleColorPalette;
  border: SimpleColorPalette;
  'input-background': SimpleColorPalette;
  disabled: SimpleColorPalette;
  error: SimpleColorPalette;
  success: SimpleColorPalette;
  holiday: SimpleColorPalette;
  birthday: SimpleColorPalette;
  anniversary: SimpleColorPalette;
  customDate: SimpleColorPalette;
  // You can also include standard Tailwind colors if you need to type them explicitly,
  // but DefaultColors (from @types/tailwindcss) usually covers them.
  // For this purpose, we only need to define our extended keys.
}

// Assert the type of twColors. This tells TypeScript about your custom color structure.
// `fullConfig.theme.colors` will also include default Tailwind colors.
// We cast to `unknown` first for a safer assertion to a complex custom type.
const twColors = fullConfig.theme.colors as unknown as PomegranateExtendedColors & { [key: string]: any };
// The `& { [key: string]: any }` part acknowledges that other (default Tailwind) colors might exist.

// SettingsRow Component
interface SettingsRowProps {
  label: string;
  value: boolean;
  onValueChange: (newValue: boolean) => void;
  trackColor: { false: string; true: string };
  thumbColor: string;
  ios_backgroundColor: string;
  isLast?: boolean;
}

const SettingsRow: React.FC<SettingsRowProps> = ({
  label,
  value,
  onValueChange,
  trackColor,
  thumbColor,
  ios_backgroundColor,
  isLast = false,
}) => {
  return (
    <View
      className={`flex-row items-center justify-between p-4 ${
        !isLast ? 'border-b border-border dark:border-border-dark' : ''
      }`}
    >
      <Text className="flex-1 mr-4 text-base text-text-primary dark:text-text-primary-dark">
        {label}
      </Text>
      <Switch
        trackColor={trackColor}
        thumbColor={thumbColor}
        ios_backgroundColor={ios_backgroundColor}
        onValueChange={onValueChange}
        value={value}
        accessibilityLabel={label}
      />
    </View>
  );
};

export default function NotificationSettingsScreen() {
  const { user } = useAuth();
  const [settings, setSettings] = useState<UserSettings['reminders'] | null>(
    null
  );
  const [isLoading, setIsLoading] = useState(true);
  const [isInitial, setIsInitial] = useState(false); // Track if settings are loaded for the first time
  const { colorScheme } = useThemeManager();

  const switchColors = useMemo(() => {
    const isDark = colorScheme === 'dark';

    const activeTrackColorLight = twColors.accent.DEFAULT;
    const activeTrackColorDark = twColors.accent.dark;

    const inactiveTrackColorLight = twColors.disabled.DEFAULT;
    const inactiveTrackColorDark = twColors.disabled.dark;

    const activeThumbColor = twColors['text-primary'].dark; // Light pinkish off-white

    const inactiveThumbColorLight = twColors.background.DEFAULT;
    const inactiveThumbColorDark = twColors['text-secondary'].dark;

    const trackColor = {
      false: isDark ? inactiveTrackColorDark : inactiveTrackColorLight,
      true: isDark ? activeTrackColorDark : activeTrackColorLight,
    };

    const getSwitchThumbColor = (isActive: boolean) => {
      if (Platform.OS === 'ios') {
        return isActive ? activeThumbColor : twColors.disabled.DEFAULT; // Using disabled.DEFAULT for inactive iOS thumb
      }
      if (isActive) {
        return activeThumbColor;
      }
      return isDark ? inactiveThumbColorDark : inactiveThumbColorLight;
    };

    const ios_off_track_color = isDark ? inactiveTrackColorDark : inactiveTrackColorLight;

    return {
      trackColor,
      ios_off_track_color,
      getSwitchThumbColor,
    };
  }, [colorScheme]);

  const loadSettings = useCallback(async () => {
    if (!user?.uid) {
      setIsLoading(false);
      setSettings({
        birthdays: true,
        anniversaries: true,
        holidays: false,
        customDates: true,
      });
      return;
    }
    setIsLoading(true);
    try {
      const loadedSettings = await getUserSettings(user.uid);
      if (loadedSettings && loadedSettings.reminders) {
        setSettings(loadedSettings.reminders);
        setIsInitial(false); // Settings already exist
      } else {
        setSettings({
          birthdays: true,
          anniversaries: true,
          holidays: false,
          customDates: true,
        });
        setIsInitial(true); // These are default settings for a new user
      }
    } catch (error) {
      console.error('Failed to load user settings:', error);
      Alert.alert('Error', 'Failed to load notification settings.');
      setSettings({
        birthdays: true,
        anniversaries: true,
        holidays: false,
        customDates: true,
      });
    } finally {
      setIsLoading(false);
    }
  }, [user?.uid]);


  useEffect(() => {
    loadSettings();
  }, [loadSettings]);

  const handleSettingChange = async (
    key: 'birthdays' | 'anniversaries' | 'holidays' | 'customDates',
    value: boolean
  ) => {
    if (!user?.uid || !settings) {
      Alert.alert('Error', 'User not authenticated or settings not loaded.');
      return;
    }

    const oldSettings = { ...settings };

    // Create the new state for the reminders object
    const newRemindersState = {
      ...settings,
      [key]: value,
    };

    // Update local state immediately for a responsive UI
    setSettings(newRemindersState);

    try {
      // Prepare the payload for Firestore.
      // We send the entire reminders object to be merged.
      const updatePayload: { [key: string]: any } = {
        userId: user.uid, // Required for create rule validation
        reminders: newRemindersState,
        updatedAt: serverTimestamp(),
      };

      // If it's the first time saving settings, add the createdAt timestamp
      if (isInitial) {
        updatePayload.createdAt = serverTimestamp();
      }

      await updateUserSettings(user.uid, updatePayload);

      // If the write was successful and it was the initial one, update the state
      if (isInitial) {
        setIsInitial(false);
      }
    } catch (error) {
      console.error(`Failed to update ${key} setting:`, error);
      Alert.alert('Error', `Failed to save setting for ${key}. Your changes have been reverted.`);
      setSettings(oldSettings); // Revert on failure
    }
  };

  const settingOptions: Array<{
    key: 'birthdays' | 'anniversaries' | 'holidays' | 'customDates';
    label: string;
    defaultValue: boolean;
  }> = [
    { key: 'birthdays', label: 'Upcoming Birthday Reminders', defaultValue: true },
    { key: 'anniversaries', label: 'Upcoming Anniversary Reminders', defaultValue: true },
    { key: 'holidays', label: 'General Holiday Reminders', defaultValue: false },
    { key: 'customDates', label: 'Custom Date Reminders', defaultValue: true },
  ];

  if (isLoading) {
    return (
      <View className="items-center justify-center flex-1 p-4 bg-background dark:bg-background-dark">
        <ActivityIndicator size="large" color={twColors.primary.DEFAULT} />
        <Text className="mt-3 text-text-secondary dark:text-text-secondary-dark">
          Loading settings...
        </Text>
      </View>
    );
  }

  return (
    <>
      <Stack.Screen options={{ title: 'Notification Settings' }} />
      <ScrollView
        className="flex-1 bg-background dark:bg-background-dark"
        contentContainerClassName="p-4"
        showsVerticalScrollIndicator={false}
      >
        <Card className="p-0">
          {settingOptions.map((option, index) => (
            <SettingsRow
              key={option.key}
              label={option.label}
              value={settings?.[option.key] ?? option.defaultValue}
              onValueChange={(value) => handleSettingChange(option.key, value)}
              trackColor={switchColors.trackColor}
              thumbColor={switchColors.getSwitchThumbColor(
                settings?.[option.key] ?? option.defaultValue
              )}
              ios_backgroundColor={switchColors.ios_off_track_color}
              isLast={index === settingOptions.length - 1}
            />
          ))}
        </Card>

        <Text className="px-2 mt-6 text-sm text-text-secondary dark:text-text-secondary-dark">
          Reminders are typically sent 7 days before the event. You can manage
          specific event dates and their reminders on the Calendar screen.
        </Text>
      </ScrollView>
    </>
  );
}