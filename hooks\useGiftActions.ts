// hooks/useGiftActions.ts
import { useState, useCallback } from 'react';
import { Alert, Share } from 'react-native';
import * as Haptics from 'expo-haptics';
import { GiftRecommendation } from '../services/recommendationService';

interface UseGiftActionsProps {
  onSave?: (item: GiftRecommendation) => void;
  onViewDetails?: (item: GiftRecommendation) => void;
}

export const useGiftActions = ({ onSave, onViewDetails }: UseGiftActionsProps = {}) => {
  const [savedItems, setSavedItems] = useState<Set<string>>(new Set());

  const handleSave = useCallback(async (item: GiftRecommendation) => {
    try {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      
      const newSavedItems = new Set(savedItems);
      if (savedItems.has(item.id)) {
        newSavedItems.delete(item.id);
        Alert.alert('Removed from saved', `${item.name} has been removed from your saved gifts.`);
      } else {
        newSavedItems.add(item.id);
        Alert.alert('Saved!', `${item.name} has been added to your saved gifts.`);
      }
      
      setSavedItems(newSavedItems);
      onSave?.(item);
    } catch (error) {
      Alert.alert('Error', 'Failed to save gift. Please try again.');
    }
  }, [savedItems, onSave]);

  const handleShare = useCallback(async (item: GiftRecommendation) => {
    try {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      
      const shareContent = {
        title: `Gift Idea: ${item.name}`,
        message: `I found this perfect gift idea: ${item.name}\n\n${item.description}\n\nPrice: ${item.priceRange}\n\nWhy it's perfect: ${item.reasoning.personalizedMatch}`,
      };

      const result = await Share.share(shareContent);
      
      if (result.action === Share.sharedAction) {
        // Successfully shared
        console.log('Gift shared successfully');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to share gift. Please try again.');
    }
  }, []);

  const handleViewDetails = useCallback((item: GiftRecommendation) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    
    if (onViewDetails) {
      onViewDetails(item);
    } else {
      // Default behavior: show detailed alert
      Alert.alert(
        item.name,
        `${item.description}\n\n💰 Price: ${item.priceRange}\n\n🎯 AI Confidence: ${item.reasoning.confidence}%\n\n💡 Why this gift: ${item.reasoning.personalizedMatch}\n\n🎁 Presentation tip: ${item.practical.presentationIdea}`,
        [
          { text: 'Close', style: 'cancel' },
          { text: 'Share', onPress: () => handleShare(item) },
        ]
      );
    }
  }, [onViewDetails, handleShare]);

  const isItemSaved = useCallback((itemId: string) => {
    return savedItems.has(itemId);
  }, [savedItems]);

  return {
    handleSave,
    handleShare,
    handleViewDetails,
    isItemSaved,
    savedItems,
  };
}; 