# Audit Issues Implementation Plan - REVISED FOR EARLY STAGE

## Overview
This plan addresses the **CRITICAL** security, performance, and code quality issues identified in the audit report. **REVISED** for early-stage app with private repository - focusing on issues that actually impact user experience and core functionality.

---

## **DEFERRED ISSUES (Low Risk for Early Stage)**
*These can be implemented later when scaling up or going public*

### ❌ **CQA-001: API Keys in Git (DEFERRED)**
**Rationale:** Private repository that will never be public = zero exposure risk  
**When to revisit:** If repo becomes public or team grows significantly

### ❌ **SEC-002: Content Sanitization (DEFERRED)** 
**Rationale:** Early stage with trusted users = extremely low content spoofing risk  
**When to revisit:** When user base grows beyond trusted circle or opens public registration

---

## **TIER 1: CRITICAL ISSUES (Must Fix)** 
*Core functionality problems that affect user experience*

### 1. SEC-001: Fix IDOR Vulnerability (Critical Security) 🚨
**Effort:** 6 hours  
**Risk:** High (major architectural change)  
**Priority:** **HIGHEST** - Users can create profiles under other user IDs
**Files:** `functions/src/index.ts`, `services/firestoreService.ts`, client code

**Why Critical:** Data corruption and unauthorized access regardless of user count

**Implementation:**
```typescript
// functions/src/index.ts
export const createSignificantOther = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }
  
  const userId = context.auth.uid; // Server-side derived, not client-provided
  const profileData = data as AddProfileData;
  
  const docRef = await addDoc(collection(db, 'significant_others'), {
    ...profileData,
    userId, // Secure server-side assignment
    createdAt: Timestamp.now(),
    updatedAt: Timestamp.now(),
  });
  
  return docRef.id;
});
```

---

### 2. BUG-001: Fix AsyncStorage Race Condition (High Bug) 🚨
**Effort:** 4 hours  
**Risk:** Medium (auth flow changes)  
**Priority:** **HIGH** - Breaks onboarding flow
**Files:** `contexts/AuthContext.tsx`

**Why Critical:** Bad user experience with auth/onboarding regardless of user count

**Implementation:**
```typescript
useEffect(() => {
  const unsubscribe = onAuthStateChanged(auth, async (currentUser) => {
    setUser(currentUser);
    
    if (currentUser) {
      // Only update onboarding status after auth confirmation
      try {
        await AsyncStorage.setItem(STORAGE_KEYS.HAS_COMPLETED_ONBOARDING, 'true');
      } catch (error) {
        console.error('Failed to update onboarding status:', error);
      }
    }
    
    setInitialLoading(false);
    setActionLoading(false);
    setGoogleLoading(false);
  });

  return unsubscribe;
}, []);
```

---

### 3. CQA-003/BUG-002: Refactor Calendar Hook (High Quality/Bug) 🚨
**Effort:** 8 hours  
**Risk:** High (major refactoring)  
**Priority:** **HIGH** - 500-line god object affecting maintainability
**Files:** `hooks/useCalendarData.ts` → Multiple focused hooks

**Why Critical:** Maintainability nightmare that will slow down all future development

---

### 4. CQA-002: Add Comprehensive Error Handling (Critical Quality) 🚨
**Effort:** 8 hours  
**Risk:** Medium (widespread changes)  
**Priority:** **HIGH** - App crashes affect user experience
**Files:** All hooks, services, components

**Why Critical:** App stability regardless of user count

---

## **TIER 2: PERFORMANCE ISSUES (High Impact)**
*These affect user experience even with few users*

### 5. PERF-001: Implement Profile Pagination (High Performance)
**Effort:** 6 hours  
**Risk:** High (data flow changes)  
**Files:** `hooks/useCalendarData.ts`, `services/profileService.ts`

**Why Important:** Slow app = bad user experience even with 10 users

---

### 6. PERF-002: Optimize Date Processing (Medium Performance)  
**Effort:** 2 hours  
**Risk:** Low (performance improvement)  
**Files:** `hooks/useCalendarData.ts`

**Why Important:** Calendar sluggishness affects core feature

---

## **TIER 3: EASY SECURITY WINS**
*Low effort, good security practices*

### 7. SEC-004: Fix Users Collection Rule (Medium Security)
**Effort:** 1 hour  
**Risk:** Low (straightforward rule change)  
**Files:** `firestore.rules`

**Implementation:**
```javascript
// BEFORE:
allow create: if request.auth != null;

// AFTER:
allow create: if request.auth != null && request.auth.uid == userId;
```

---

### 8. SEC-003: Fix Profile Enumeration (High Security)
**Effort:** 4 hours  
**Risk:** Medium (query structure changes)  
**Files:** `services/profileService.ts`, Firestore rules

---

## **REVISED IMPLEMENTATION SEQUENCE**

**Week 1: Core Functionality (Tier 1)** 🎯
- Day 1-3: SEC-001 (IDOR fix) - **CRITICAL**
- Day 4-5: BUG-001 (Race condition) - **CRITICAL**

**Week 2: Maintainability (Tier 1)**  
- Day 1-5: CQA-003 (Calendar hook refactor) - **CRITICAL**

**Week 3: Stability & Performance (Tier 1 & 2)**
- Day 1-4: CQA-002 (Error handling) - **CRITICAL**
- Day 5: PERF-002 (Date processing) - **HIGH**

**Week 4: Performance & Security (Tier 2 & 3)**
- Day 1-3: PERF-001 (Pagination) - **HIGH**
- Day 4: SEC-004 (Firestore rules) - **EASY WIN**
- Day 5: SEC-003 (Profile enumeration) - **MEDIUM**

---

## **SUCCESS METRICS (Revised)**

**Functionality:** Zero critical user experience bugs  
**Performance:** <3s initial load, smooth calendar navigation  
**Maintainability:** Calendar hook split into focused components  
**Security:** Core IDOR vulnerability eliminated  

**Total Effort:** ~33 hours (down from 60+ hours)  
**Timeline:** 4 weeks (down from 5 weeks)

---

## **DEFERRED ITEMS TRACKING**

**Deferred for Later:**
- CQA-001 (API Keys) - Revisit if repo goes public
- SEC-002 (Content Sanitization) - Revisit at 100+ users

**Reasoning:** Focus on what actually matters for early-stage success 