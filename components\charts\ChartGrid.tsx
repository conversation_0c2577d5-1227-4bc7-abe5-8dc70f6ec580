import React, { useCallback } from 'react';
import { View, Text, ScrollView, GestureResponderEvent } from 'react-native';
import { GiftContributionsData, DailyGiftData, getIntensityLevel } from '../../utils/chartDataUtils';
import ContributionSquare from './ContributionSquare';
import { CHART_CONSTANTS, MONTH_LABELS } from '../../constants/chartConstants';

interface ChartGridProps {
  chartData: GiftContributionsData;
  squareSize: number;
  squareSpacing: number;
  getIntensityColor: (intensity: number) => string;
  onSquarePress: (dayData: DailyGiftData, event: GestureResponderEvent) => void;
}

const ChartGrid: React.FC<ChartGridProps> = ({
  chartData,
  squareSize,
  squareSpacing,
  getIntensityColor,
  onSquarePress,
}) => {
  // Memoized square rendering for performance
  const renderSquare = useCallback((day: DailyGiftData) => {
    const intensity = getIntensityLevel(day.giftCount, day.successScore);
    
    return (
      <ContributionSquare
        key={day.date}
        dayData={day}
        size={squareSize}
        spacing={squareSpacing}
        color={getIntensityColor(intensity)}
        onPress={(event: GestureResponderEvent) => onSquarePress(day, event)}
        style={{ marginBottom: squareSpacing }}
      />
    );
  }, [squareSize, squareSpacing, getIntensityColor, onSquarePress]);

  return (
    <ScrollView horizontal showsHorizontalScrollIndicator={false}>
      <View>
        {/* Month labels */}
        <View className="flex-row justify-between mb-2">
          {MONTH_LABELS.map((month) => (
            <Text
              key={month}
              className="text-xs text-text-secondary dark:text-text-secondary-dark"
              style={{
                width: (squareSize + squareSpacing) * CHART_CONSTANTS.MONTH_LABEL_SPACING,
                marginRight: squareSpacing,
              }}
            >
              {month}
            </Text>
          ))}
        </View>

        {/* Chart grid */}
        <View className="flex-row">
          {/* Weeks grid */}
          <View className="flex-row">
            {chartData.weeks.map((week) => (
              <View key={`week-${week.weekNumber}-${week.year}`} className="mr-1">
                {week.days.map((day) => renderSquare(day))}
              </View>
            ))}
          </View>
        </View>
      </View>
    </ScrollView>
  );
};

export default React.memo(ChartGrid); 