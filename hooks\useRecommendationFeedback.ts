import { useState, useCallback, useRef, useEffect } from 'react';
import { Alert } from 'react-native';
import * as Haptics from 'expo-haptics';
import 'react-native-get-random-values';
import { v4 as uuidv4 } from 'uuid';
import {
  getProfileFeedback,
  processRecommendationFeedback,
  FeedbackEntry,
  GiftRecommendation,
} from '@/services/recommendationService';
import { useAuth } from '@/contexts/AuthContext';

export const useRecommendationFeedback = (profileId: string | null) => {
  const { user } = useAuth();
  const [currentFeedbackMap, setCurrentFeedbackMap] = useState<Map<string, FeedbackEntry>>(new Map());
  const [feedbackError, setFeedbackError] = useState<string | null>(null);
  const debounceTimers = useRef(new Map<string, NodeJS.Timeout>());

  const fetchProfileFeedback = useCallback(async () => {
    if (!profileId) {
      setCurrentFeedbackMap(new Map());
      return;
    }
    setFeedbackError(null);
    try {
      const feedback = await getProfileFeedback(profileId);
      const feedbackMap = new Map<string, FeedbackEntry>();
      feedback.forEach((entry) => {
        if (entry.recommendationId) {
          feedbackMap.set(entry.recommendationId, entry);
        }
      });
      setCurrentFeedbackMap(feedbackMap);
    } catch (error) {
      console.error('Failed to fetch feedback:', error);
      setFeedbackError('Could not load feedback data.');
      setCurrentFeedbackMap(new Map());
    }
  }, [profileId]);

  useEffect(() => {
    fetchProfileFeedback();
  }, [fetchProfileFeedback]);

  const handleFeedback = async (
    item: GiftRecommendation,
    feedbackType: 'like' | 'dislike'
  ) => {
    if (!user?.uid || !profileId || !item.id) {
      Alert.alert('Error', 'Cannot save feedback. Please ensure you are logged in and have selected a profile.');
      return;
    }

    const originalFeedback = currentFeedbackMap.get(item.id);
    const newFeedbackMap = new Map(currentFeedbackMap);

    // Optimistic UI update
    if (originalFeedback?.feedbackType === feedbackType) {
      newFeedbackMap.delete(item.id);
    } else {
      const tempFeedback: FeedbackEntry = {
        id: originalFeedback?.id || uuidv4(),
        userId: user.uid,
        profileId: profileId,
        recommendationId: item.id,
        feedbackType,
        recommendationDetails: { name: item.name, description: item.description },
        timestamp: new Date(),
      };
      newFeedbackMap.set(item.id, tempFeedback);
    }
    setCurrentFeedbackMap(newFeedbackMap);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

    // Clear any existing debounce timer for this item
    const existingTimer = debounceTimers.current.get(item.id);
    if (existingTimer) {
      clearTimeout(existingTimer);
    }

    // Set a new timer to process the feedback after a delay
    const newTimer = setTimeout(() => {
      processRecommendationFeedback({
        userId: user.uid,
        profileId: profileId,
        recommendationId: item.id,
        feedbackType,
        recommendationDetails: { name: item.name, description: item.description },
      }).catch((err) => {
        console.error('[Feedback] Debounced feedback processing failed:', err);
        // On failure, revert the UI to its state before the optimistic update.
        const revertedMap = new Map(currentFeedbackMap);
        if (originalFeedback) {
          revertedMap.set(item.id, originalFeedback);
        } else {
          revertedMap.delete(item.id);
        }
        setCurrentFeedbackMap(revertedMap);
      });
    }, 1000); // 1-second debounce delay

    debounceTimers.current.set(item.id, newTimer);
  };

  return {
    currentFeedbackMap,
    feedbackError,
    handleFeedback,
    fetchProfileFeedback,
  };
}; 