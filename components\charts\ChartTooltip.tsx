import React from 'react';
import { View, Text, TouchableOpacity, Modal, Dimensions } from 'react-native';
import { Feather } from '@expo/vector-icons';
import Animated, { FadeIn, SlideInDown } from 'react-native-reanimated';
import { useColorScheme } from 'nativewind';
import { format } from 'date-fns';
import { DailyGiftData } from '../../utils/chartDataUtils';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface ChartTooltipProps {
  dayData: DailyGiftData;
  position: { x: number; y: number };
  onClose: () => void;
  onAddGift?: () => void;
}

const ChartTooltip: React.FC<ChartTooltipProps> = ({
  dayData,
  position,
  onClose,
  onAddGift
}) => {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  // Calculate tooltip position - full width
  const tooltipWidth = screenWidth - 20; // Full width with 10px margins
  const tooltipHeight = Math.min(300, screenHeight * 0.4);
  
  // Center horizontally, adjust vertically
  let adjustedX = 10; // Always start from left margin
  let adjustedY = position.y - tooltipHeight - 10;
  
  // If tooltip would go above screen, show it below the touch point
  if (adjustedY < 50) adjustedY = position.y + 20;

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'EEEE, MMMM d, yyyy');
    } catch {
      return dateString;
    }
  };

  const getSuccessColor = (score: number): string => {
    if (score >= 80) return isDark ? '#C70039' : '#A3002B'; // High success - app's primary colors
    if (score >= 60) return isDark ? '#FF507B' : '#E5355F'; // Good success - app's accent colors  
    if (score >= 40) return isDark ? '#786A6E' : '#7A3E4F'; // Mixed - app's secondary colors
    return isDark ? '#4D2A36' : '#D1C5C9'; // Needs improvement - app's muted colors
  };

  const getSuccessLabel = (score: number): string => {
    if (score >= 80) return 'Great success!';
    if (score >= 60) return 'Good response';
    if (score >= 40) return 'Mixed results';
    return 'Needs improvement';
  };

  return (
    <Modal transparent visible animationType="none">
      <TouchableOpacity 
        style={{ flex: 1 }} 
        activeOpacity={1} 
        onPress={onClose}
      >
        <Animated.View
          entering={FadeIn.duration(200)}
          style={{
            position: 'absolute',
            left: adjustedX,
            top: adjustedY,
            width: tooltipWidth,
            maxHeight: tooltipHeight,
          }}
        >
          <Animated.View
            entering={SlideInDown.duration(300)}
            className="overflow-hidden rounded-lg border shadow-lg bg-card dark:bg-card-dark border-border dark:border-border-dark"
          >
            {/* Header */}
            <View className="p-4 border-b border-border dark:border-border-dark">
              <View className="flex-row justify-between items-center">
                <Text className="text-base font-semibold text-text-primary dark:text-text-primary-dark">
                  {formatDate(dayData.date)}
                </Text>
                <TouchableOpacity onPress={onClose}>
                  <Feather 
                    name="x" 
                    size={20} 
                    color={isDark ? '#9ca3af' : '#6b7280'} 
                  />
                </TouchableOpacity>
              </View>
              
              <View className="flex-row items-center mt-2">
                <Text className="mr-2 text-lg font-bold text-primary dark:text-primary-dark">
                  {dayData.giftCount}
                </Text>
                <Text className="text-sm text-text-secondary dark:text-text-secondary-dark">
                  {dayData.giftCount === 1 ? 'gift given' : 'gifts given'}
                </Text>
              </View>

              {dayData.giftCount > 0 && dayData.successScore > 0 && (
                <View className="flex-row items-center mt-2">
                  <View 
                    className="mr-2 w-3 h-3 rounded-full"
                    style={{ backgroundColor: getSuccessColor(dayData.successScore) }}
                  />
                  <Text 
                    className="text-sm font-medium"
                    style={{ color: getSuccessColor(dayData.successScore) }}
                  >
                    {Math.round(dayData.successScore)}% • {getSuccessLabel(dayData.successScore)}
                  </Text>
                </View>
              )}
            </View>

            {/* Gift list */}
            {dayData.giftCount > 0 ? (
              <View className="max-h-40">
                {dayData.gifts.map((gift, index) => (
                  <View 
                    key={`${gift.item}-${index}`}
                    className="p-3 border-b border-border/50 dark:border-border-dark/50"
                  >
                    <Text className="font-medium text-text-primary dark:text-text-primary-dark">
                      🎁 {gift.item}
                    </Text>
                    {gift.occasion && (
                      <Text className="mt-1 text-sm text-text-secondary dark:text-text-secondary-dark">
                        For {gift.occasion}
                      </Text>
                    )}
                    {gift.reaction && (
                      <Text className="mt-1 text-sm text-text-secondary dark:text-text-secondary-dark">
                        💭 {gift.reaction.length > 50 ? `${gift.reaction.substring(0, 50)}...` : gift.reaction}
                      </Text>
                    )}
                    {gift.rating && (
                      <View className="flex-row items-center mt-1">
                        <Text className="mr-1 text-sm text-text-secondary dark:text-text-secondary-dark">
                          Rating:
                        </Text>
                        {[...Array(5)].map((_, i) => (
                          <Text key={i} className="text-sm">
                            {i < gift.rating! ? '⭐' : '☆'}
                          </Text>
                        ))}
                      </View>
                    )}
                  </View>
                ))}
              </View>
            ) : (
              <View className="p-4">
                <Text className="mb-3 text-center text-text-secondary dark:text-text-secondary-dark">
                  No gifts recorded for this day
                </Text>
              </View>
            )}

            {/* Actions */}
            <View className="p-3 bg-accent/5 dark:bg-accent-dark/5">
              {onAddGift && (
                <TouchableOpacity
                  onPress={onAddGift}
                  className="flex-row justify-center items-center p-3 rounded-lg bg-primary dark:bg-primary-dark"
                >
                  <Feather name="plus" size={16} color="white" />
                  <Text className="ml-2 text-sm font-medium text-white">
                    {dayData.giftCount > 0 ? 'Add Another Gift' : 'Add Gift for This Day'}
                  </Text>
                </TouchableOpacity>
              )}
            </View>
          </Animated.View>
        </Animated.View>
      </TouchableOpacity>
    </Modal>
  );
};

export default ChartTooltip; 