import React from 'react';
import { View, TouchableOpacity, Text } from 'react-native';
// 1. Import Animated and Keyframe
import Animated, { Keyframe } from 'react-native-reanimated';
// 2. Import chosen icon set (e.g., Feather)
import { Feather } from '@expo/vector-icons'; // Or MaterialCommunityIcons, etc.
import { useColorScheme } from 'nativewind';

interface ActionMenuProps {
  onClose: () => void;
  onAddProfile: () => void;
  onAddNote: () => void;
  onAddDate: () => void;
  onAddGift: () => void;
  onEditProfile: () => void;
  className?: string;
}

const AnimatedTouchableOpacity =
  Animated.createAnimatedComponent(TouchableOpacity);

// 3. Define menu items with labels AND icons
const menuItems = [
  {
    label: 'Add Note',
    iconName: 'file-plus', // Feather icon name
    action: (props: ActionMenuProps) => {
      props.onAddNote();
      props.onClose();
    },
  },
  {
    label: 'Add Important Date',
    iconName: 'calendar-plus', // Feather icon name
    action: (props: ActionMenuProps) => {
      props.onAddDate();
      props.onClose();
    },
  },
  {
    label: 'Add Past Gift',
    iconName: 'gift', // Feather icon name
    action: (props: ActionMenuProps) => {
      props.onAddGift();
      props.onClose();
    },
  },
  {
    label: 'Add New Profile',
    iconName: 'user-plus', // Feather icon name
    action: (props: ActionMenuProps) => {
      props.onAddProfile();
      props.onClose();
    },
  },
  {
    label: 'Edit Current Profile',
    iconName: 'edit-2', // Feather icon name
    action: (props: ActionMenuProps) => {
      props.onEditProfile();
      props.onClose();
    },
  },
];

// Define icon size once
const ICON_SIZE = 20;

const ActionMenu: React.FC<ActionMenuProps> = (props) => {
  const { className } = props;
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  
  const staggerDelay = 60;
  const animationDuration = 300;
  const initialOffsetY = -15; // Offset for entering/exiting animation

  // Theme-aware colors matching app design
  const primaryColor = isDark ? '#C70039' : '#A3002B';
  const textPrimaryColor = isDark ? '#F9FAFB' : '#1F2937';
  const cardBgColor = isDark ? '#1F2937' : '#FFFFFF';
  const borderColor = isDark ? '#374151' : '#E5E7EB';

  return (
    <View
      // Updated styling to match app design system
      className={`absolute z-40 ${className}`}
      style={{
        backgroundColor: cardBgColor,
        borderRadius: 12, // rounded-xl
        borderWidth: 1,
        borderColor: borderColor,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.15,
        shadowRadius: 12,
        elevation: 8,
        minWidth: 200, // Ensure consistent width
      }}
    >
      {menuItems.map((item, index) => {
        // Define the Keyframe animation for ENTERING
        const enteringAnimation = new Keyframe({
          0: {
            opacity: 0,
            transform: [{ translateY: initialOffsetY }],
          },
          100: {
            opacity: 1,
            transform: [{ translateY: 0 }],
          },
        })
          .delay(index * staggerDelay) // Apply stagger delay based on index
          .duration(animationDuration); // Set animation duration

        // Define the Keyframe animation for EXITING
        const exitingAnimation = new Keyframe({
          0: {
            // Start state (fully visible)
            opacity: 1,
            transform: [{ translateY: 0 }],
          },
          100: {
            // End state (faded out and moved up/down)
            opacity: 0,
            transform: [{ translateY: initialOffsetY }], // Move back to initial offset
          },
        })
          // Apply REVERSE stagger delay for exiting
          .delay((menuItems.length - 1 - index) * staggerDelay)
          .duration(animationDuration); // Set animation duration

        const isLastItem = index === menuItems.length - 1;

        return (
          <AnimatedTouchableOpacity
            key={item.label}
            entering={enteringAnimation}
            exiting={exitingAnimation}
            onPress={() => item.action(props)}
            activeOpacity={0.7}
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              padding: 12, // Consistent with ProfileDropdown
              borderBottomWidth: isLastItem ? 0 : 1,
              borderBottomColor: borderColor,
            }}
            className="active:bg-primary/10 dark:active:bg-primary-dark/10"
          >
            <View style={{ 
              width: 32, // Fixed width for icon container
              marginRight: 12,
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <Feather
                name={item.iconName as any}
                size={ICON_SIZE}
                color={primaryColor}
              />
            </View>

            <Text 
              style={{
                color: textPrimaryColor,
                fontSize: 16,
                fontWeight: '500',
                flex: 1,
              }}
            >
              {item.label}
            </Text>
          </AnimatedTouchableOpacity>
        );
      })}
    </View>
  );
};

export default ActionMenu;
