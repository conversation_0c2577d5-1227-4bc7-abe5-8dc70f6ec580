import React, { useEffect } from 'react';
import {
  Modal,
  View,
  Text,
  Image,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
} from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withDelay,
  withSequence,
  withSpring,
  Easing,
} from 'react-native-reanimated';
import Button from './Button';

// --- Style Constants ---
const ICON_SIZE = Dimensions.get('window').width * 0.3;

// --- Color Theme ---
const colorTheme = {
  modalBackdrop: 'rgba(0,0,0,0.5)',
  cardBackground: '#FFFFFF',
  textPrimary: '#000000',
  textSecondary: '#555555',
};

interface CongratulationsModalProps {
  isVisible: boolean;
  onContinue: () => void;
  profileName?: string;
}

const CongratulationsModal: React.FC<CongratulationsModalProps> = ({
  isVisible,
  onContinue,
  profileName,
}) => {
  // Animation values for the main content
  const iconScale = useSharedValue(0.8);
  const iconRotation = useSharedValue(0);
  const textOpacity = useSharedValue(0);
  const textTranslateY = useSharedValue(30);
  const buttonOpacity = useSharedValue(0);
  const buttonScale = useSharedValue(0.8);

  const resetAnimations = () => {
    'worklet';
    iconScale.value = 0.8;
    iconRotation.value = 0;
    textOpacity.value = 0;
    textTranslateY.value = 30;
    buttonOpacity.value = 0;
    buttonScale.value = 0.8;
  };

  const startCelebrationAnimation = () => {
    'worklet';
    
    // Icon animation - bouncy entrance with rotation
    iconScale.value = withSequence(
      withTiming(1.3, { duration: 400, easing: Easing.out(Easing.back(2)) }),
      withTiming(1, { duration: 200 })
    );
    
    iconRotation.value = withSequence(
      withTiming(-20, { duration: 200 }),
      withTiming(20, { duration: 300 }),
      withTiming(0, { duration: 200 })
    );

    // Text entrance
    textOpacity.value = withDelay(400, withTiming(1, { duration: 600 }));
    textTranslateY.value = withDelay(400, withSpring(0, { damping: 12, stiffness: 100 }));

    // Button entrance
    buttonOpacity.value = withDelay(900, withTiming(1, { duration: 400 }));
    buttonScale.value = withDelay(900, withSpring(1, { damping: 8, stiffness: 100 }));
  };

  useEffect(() => {
    if (isVisible) {
      resetAnimations();
      setTimeout(() => {
        startCelebrationAnimation();
      }, 300);
    }
  }, [isVisible]);

  // Animated styles
  const iconAnimatedStyle = useAnimatedStyle(() => ({
    transform: [
      { scale: iconScale.value },
      { rotate: `${iconRotation.value}deg` }
    ],
  }));

  const textAnimatedStyle = useAnimatedStyle(() => ({
    opacity: textOpacity.value,
    transform: [{ translateY: textTranslateY.value }],
  }));

  const buttonAnimatedStyle = useAnimatedStyle(() => ({
    opacity: buttonOpacity.value,
    transform: [{ scale: buttonScale.value }],
  }));

  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={isVisible}
      onRequestClose={onContinue}
    >
      <TouchableOpacity
        style={[
          styles.modalBackdrop,
          { backgroundColor: colorTheme.modalBackdrop },
        ]}
        activeOpacity={1}
        onPress={onContinue}
      >
        <View
          style={[
            styles.modalContent,
            { backgroundColor: colorTheme.cardBackground },
          ]}
        >
          {/* Profile Plus Icon */}
          <Animated.Image
            source={require('../../assets/images/profileplus.png')}
            style={[styles.iconImage, iconAnimatedStyle]}
            resizeMode="contain"
          />

          {/* Text Content */}
          <Animated.View style={[styles.textContainer, textAnimatedStyle]}>
            <Text
              style={[styles.titleText, { color: colorTheme.textPrimary }]}
            >
              Congratulations{profileName ? `, ${profileName}` : ''}!
            </Text>
            <Text
              style={[styles.subtitleText, { color: colorTheme.textSecondary }]}
            >
              You've successfully set up a new profile. 🎉
            </Text>
          </Animated.View>

          {/* Button */}
          <Animated.View style={[styles.buttonWrapper, buttonAnimatedStyle]}>
            <Button
              title="Continue"
              onPress={onContinue}
            />
          </Animated.View>
        </View>
      </TouchableOpacity>
    </Modal>
  );
};

// --- Styles ---
const styles = StyleSheet.create({
  modalBackdrop: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    alignItems: 'center',
    padding: 30,
    borderRadius: 16,
    width: '90%',
    maxWidth: 400,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  iconImage: {
    width: ICON_SIZE,
    height: ICON_SIZE,
    marginBottom: 20,
  },
  textContainer: {
    alignItems: 'center',
    marginBottom: 25,
  },
  titleText: {
    fontSize: 22,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitleText: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 22,
  },
  buttonWrapper: {
    width: '100%',
  },
});

export default CongratulationsModal;
