// components/home/<USER>
import React, { MutableRefObject, useState, useRef } from 'react';
import { View, Text, FlatList, Pressable, Dimensions } from 'react-native';
import Animated, { FadeIn, SlideInRight } from 'react-native-reanimated';
import { Feather } from '@expo/vector-icons';
import Card from '../ui/Card';
import LoadingIndicator from '../ui/LoadingIndicator';
import Button from '../ui/Button';
import { useColorScheme } from 'nativewind';
import {
  GiftRecommendation,
  FeedbackEntry,
} from '@/services/recommendationService';
import GiftRecommendationCard from './GiftGallery/GiftRecommendationCard';
import SmartHeader from './GiftGallery/SmartHeader';
import PaginationDots from './GiftGallery/PaginationDots';

const AnimatedCard = Animated.createAnimatedComponent(Card);
const { width: screenWidth } = Dimensions.get('window');

interface GiftGalleryProps {
  recommendations: GiftRecommendation[] | null;
  isLoading: boolean;
  error: string | null;
  currentFeedbackMap: Map<string, FeedbackEntry>;
  onFeedback: (
    item: GiftRecommendation,
    feedbackType: 'like' | 'dislike'
  ) => Promise<void>;
  onGenerateNewIdeas: () => void;
  emptyStateMessage?: string;
  showGenerateButton?: boolean;
  profileName?: string;
  totalFeedback?: number;
}

const GiftGallery: React.FC<GiftGalleryProps> = ({
  recommendations,
  isLoading,
  error,
  currentFeedbackMap,
  onFeedback,
  onGenerateNewIdeas,
  emptyStateMessage = 'No recommendations yet. Generate some gift ideas!',
  showGenerateButton = true,
  profileName,
  totalFeedback = 0,
}) => {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const [currentIndex, setCurrentIndex] = useState(0);
  const flatListRef = useRef<FlatList>(null);

  const renderRecommendationCard = ({
    item,
    index,
  }: {
    item: GiftRecommendation;
    index: number;
  }) => {
    const feedback = currentFeedbackMap.get(item.id);

    return (
      <GiftRecommendationCard
        item={item}
        index={index}
        feedback={feedback}
        onFeedback={onFeedback}
        screenWidth={screenWidth}
      />
    );
  };

  const renderSkeletonCard = ({ index }: { index: number }) => {
    const cardWidth = screenWidth - 40; // Leave 20px padding on each side

    return (
      <View
        style={{
          width: screenWidth,
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <AnimatedCard
          entering={SlideInRight.delay(index * 150).springify()}
          className="overflow-hidden mb-4 rounded-xl border shadow-sm bg-card dark:bg-card-dark border-border dark:border-border-dark"
          style={{ width: cardWidth, minHeight: 520 }}
        >
          <View className="p-6 animate-pulse">
            <View className="flex-row justify-between items-start mb-4">
              <View className="flex-row flex-1 items-center">
                <View className="mr-4 w-12 h-12 rounded-lg bg-primary/10 dark:bg-primary-dark/10" />
                <View className="flex-1">
                  <View className="mb-2 h-6 rounded bg-primary/10 dark:bg-primary-dark/10" />
                  <View className="w-24 h-4 rounded bg-primary/10 dark:bg-primary-dark/10" />
                </View>
              </View>
            </View>

            <View className="p-4 mb-4 rounded-lg bg-primary/5 dark:bg-primary-dark/5">
              <View className="mb-2 h-4 rounded bg-primary/10 dark:bg-primary-dark/10" />
              <View className="w-3/4 h-3 rounded bg-primary/10 dark:bg-primary-dark/10" />
            </View>

            <View className="mb-4 h-4 rounded bg-primary/10 dark:bg-primary-dark/10" />
            <View className="mb-2 h-3 rounded bg-primary/10 dark:bg-primary-dark/10" />
            <View className="mb-4 w-5/6 h-3 rounded bg-primary/10 dark:bg-primary-dark/10" />

            <View className="flex-row gap-3">
              <View className="flex-1 h-12 rounded-lg bg-primary/20 dark:bg-primary-dark/20" />
              <View className="flex-1 h-12 rounded-lg bg-error/20 dark:bg-error-dark/20" />
            </View>
          </View>
        </AnimatedCard>
      </View>
    );
  };

  const handleDotPress = (index: number) => {
    setCurrentIndex(index);
    flatListRef.current?.scrollToIndex({
      index,
      animated: true,
    });
  };

  const handleScrollEnd = (event: any) => {
    const contentOffset = event.nativeEvent.contentOffset;
    const viewSize = event.nativeEvent.layoutMeasurement;

    // Calculate the current index based on scroll position
    // Since we're using snapToInterval with screenWidth, we should calculate based on that
    const newIndex = Math.round(contentOffset.x / screenWidth);

    // Ensure index is within bounds
    const clampedIndex = Math.max(
      0,
      Math.min(newIndex, (recommendations?.length || 1) - 1)
    );



    setCurrentIndex(clampedIndex);
  };

  // Only show full loading indicator for initial load, not during regeneration
  const isInitialLoad = isLoading && (!recommendations || recommendations.length === 0) && totalFeedback === 0;
  if (isInitialLoad) {
    return (
      <View className="justify-center items-center py-16">
        <LoadingIndicator color={isDark ? '#C70039' : '#A3002B'} size="large" />
        <Text className="mt-6 text-base font-medium text-text-secondary dark:text-text-secondary-dark">
          Finding perfect gifts...
        </Text>
      </View>
    );
  }

  if (error) {
    return (
      <Animated.View
        entering={FadeIn.duration(300)}
        className="justify-center items-center py-16"
      >
        <View className="justify-center items-center mb-6 w-24 h-24 rounded-full bg-error/10 dark:bg-error-dark/10">
          <Text className="mb-2 text-3xl">🤖</Text>
          <Feather
            name="alert-circle"
            size={24}
            color={isDark ? '#B20021' : '#D90429'}
          />
        </View>
        <Text className="mb-2 text-xl font-bold text-center text-text-primary dark:text-text-primary-dark">
          Oops! I had a brain fog moment
        </Text>
        <Text className="mb-6 max-w-sm text-base text-center text-text-secondary dark:text-text-secondary-dark">
          {error}
        </Text>
        <Text className="mb-6 text-sm text-center text-text-secondary dark:text-text-secondary-dark">
          💡 Don't worry, I'm still learning! Let's try again.
        </Text>
        <View className="flex-row gap-3">
          <Button
            title="Try Again"
            variant="primary"
            className="px-6"
            onPress={onGenerateNewIdeas}
            leftIcon={<Feather name="refresh-cw" size={16} color="white" />}
          />
          <Button
            title="Check Connection"
            variant="secondary"
            className="px-6"
            onPress={() => console.log('Check connection')}
            leftIcon={
              <Feather
                name="wifi"
                size={16}
                color={isDark ? '#FF507B' : '#E5355F'}
              />
            }
          />
        </View>
      </Animated.View>
    );
  }

  if (!recommendations || recommendations.length === 0) {
    return (
      <Animated.View
        entering={FadeIn.duration(400)}
        className="justify-center items-center py-16"
      >
        <View className="justify-center items-center p-6 mb-6 w-48 h-48 rounded-full bg-primary/10 dark:bg-primary-dark/10">
          <Text className="text-3xl">✨</Text>
        </View>
        <Text className="mb-3 text-xl font-bold text-center text-text-primary dark:text-text-primary-dark">
          Ready to find amazing gifts?
        </Text>
        <Text className="mb-6 max-w-sm text-base text-center text-text-secondary dark:text-text-secondary-dark">
          {profileName
            ? `Tell me more about ${profileName} and I'll find perfect gifts!`
            : emptyStateMessage}
        </Text>

        {profileName && (
          <View className="p-4 mb-6 max-w-sm rounded-lg bg-primary/5 dark:bg-primary-dark/5">
            <Text className="text-sm text-center text-text-secondary dark:text-text-secondary-dark">
              🎯 The more I know about {profileName}, the better gift
              recommendations I can provide!
            </Text>
          </View>
        )}

        {showGenerateButton && (
          <View className="flex-row gap-3">
            <Button
              title={isLoading ? 'Finding Gifts...' : 'Generate Ideas'}
              variant="primary"
              className="px-6"
              onPress={onGenerateNewIdeas}
              disabled={isLoading}
              isLoading={isLoading}
              leftIcon={
                !isLoading ? (
                  <Feather name="zap" size={18} color="white" />
                ) : undefined
              }
            />
          </View>
        )}
      </Animated.View>
    );
  }



  return (
    <View className="mt-6">
      <SmartHeader
        profileName={profileName}
        isLoading={isLoading}
        totalFeedback={totalFeedback}
      />
      <FlatList
        ref={flatListRef}
        data={(() => {
          const data = isLoading
            ? [
                ...(recommendations || []),
                ...Array(2).fill({ id: 'skeleton', isLoading: true }),
              ]
            : recommendations;
          return data;
        })()}
        renderItem={({ item, index }) => {
          if (item.isLoading) {
            return renderSkeletonCard({ index });
          }
          return renderRecommendationCard({ item, index });
        }}
        keyExtractor={(item, index) =>
          item.isLoading ? `skeleton-${index}` : item.id
        }
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={{ alignItems: 'center' }}
        decelerationRate="fast"
        snapToInterval={screenWidth} // Snap to full screen width for pagination
        snapToAlignment="center"
        pagingEnabled
        onMomentumScrollEnd={handleScrollEnd}
        getItemLayout={(data, index) => {
          return {
            length: screenWidth,
            offset: screenWidth * index,
            index,
          };
        }}
      />

      <PaginationDots
        data={recommendations || []}
        currentIndex={currentIndex}
        onDotPress={handleDotPress}
        isLoading={isLoading}
      />

      {showGenerateButton && (
        <Button
          title={isLoading ? 'Finding Gifts...' : 'Generate New Ideas'}
          variant="primary"
          className="mt-5 w-full"
          onPress={onGenerateNewIdeas}
          disabled={isLoading}
          isLoading={isLoading}
        />
      )}
    </View>
  );
};

export default GiftGallery;
