export const interestSuggestions: string[] = [
  'Reading',
  'Hiking',
  'Cooking',
  'Photography',
  'Gaming',
  'Traveling',
  'Painting',
  'Music',
  'Sports',
  'Gardening',
  'Writing',
  'Coding',
  'Yoga',
  'Meditation',
  'Fishing',
  'Hunting',
  'Dancing',
  'Theater',
  'Movies',
  'Collecting',
  'Running',
  'Cycling',
  'Swimming',
  'Skiing',
  'Snowboarding',
  'Surfing',
  'Climbing',
  'Camping',
  'Backpacking',
  'Astronomy',
  'Birdwatching',
  'Volunteering',
  'Chess',
  'Poker',
  'Board Games',
  'Video Games',
  'Podcasts',
  'Learning Languages',
  'History',
  'Philosophy',
  'Psychology',
  'Science',
  'Technology',
  'Art History',
  'Fashion',
  'Interior Design',
  'Architecture',
  'Sculpting',
  'Drawing',
  'Knitting',
  'Crocheting',
  'Sewing',
  'Woodworking',
  'Metalworking',
  'Electronics',
  'Robotics',
  'Cars',
  'Motorcycles',
  'Airplanes',
  'Boating',
  'Sailing',
  'Kayaking',
  'Canoeing',
  'Archery',
  'Shooting',
  'Martial Arts',
  'Boxing',
  'Wrestling',
  'Gymnastics',
  'Weightlifting',
  'CrossFit',
  'Zumba',
  'Pilates',
  'Tai Chi',
  '<PERSON> Gong',
  'Cooking Classes',
  'Baking',
  'Wine Tasting',
  'Beer Tasting',
  'Coffee Tasting',
  'Tea Tasting',
  'Volunteering at Animal Shelters',
  'Environmentalism',
  'Conservation',
  'Human Rights',
  'Social Justice',
  'Investing',
  'Stock Market',
  'Real Estate',
  'Entrepreneurship',
  'Marketing',
  'Sales',
  'Management',
  'Public Speaking',
  'Debate',
  'Writing Fiction',
  'Writing Non-Fiction',
  'Poetry',
  'Screenwriting',
  'Acting',
  'Directing',
  'Producing',
  'Film Editing',
  'Sound Design',
  'Visual Effects',
  'Animation',
  'Web Development',
  'Mobile Development',
  'Data Science',
  'Machine Learning',
  'Artificial Intelligence',
  'Cybersecurity',
  'Network Administration',
  'Cloud Computing',
  'DevOps',
  'Technical Writing',
  'Photography Gear',
  'Art Supplies',
  'Books',
  'Board Games',
  'Video Games',
  'Outdoor Gear',
  'Camping Gear',
  'Hiking Boots',
  'Running Shoes',
  'Yoga Mat',
  'Meditation Cushion',
  'Fishing Rod',
  'Hunting Rifle',
  'Dance Shoes',
  'Theater Tickets',
  'Movie Tickets',
  'Collectibles',
  'Musical Instruments',
  'Vinyl Records',
  'Concert Tickets',
  'Sports Equipment',
  'Gardening Tools',
  'Writing Journal',
  'Coding Books',
  'Language Learning Software',
  'History Books',
  'Philosophy Books',
  'Psychology Books',
  'Science Books',
  'Technology Gadgets',
  'Art History Books',
  'Fashion Accessories',
  'Interior Design Books',
  'Architectural Models',
  'Sculpting Tools',
  'Drawing Supplies',
  'Knitting Supplies',
  'Crocheting Supplies',
  'Sewing Machine',
  'Woodworking Tools',
  'Metalworking Tools',
  'Electronics Kits',
  'Robotics Kits',
  'Car Accessories',
  'Motorcycle Gear',
  'Airplane Models',
  'Boat Accessories',
  'Sailing Gear',
  'Kayaking Gear',
  'Canoeing Gear',
  'Archery Equipment',
  'Shooting Gear',
  'Martial Arts Uniform',
  'Boxing Gloves',
  'Wrestling Shoes',
  'Gymnastics Leotard',
  'Weightlifting Belt',
  'CrossFit Gear',
  'Zumba Apparel',
  'Pilates Equipment',
  'Tai Chi Uniform',
  'Qi Gong Books',
  'Cookbooks',
  'Baking Supplies',
  'Wine Accessories',
  'Beer Brewing Kit',
  'Coffee Maker',
  'Tea Set',
  'Pet Supplies',
  'Gardening Books',
  'Environmental Books',
  'Human Rights Books',
  'Social Justice Books',
  'Investing Books',
  'Stock Market Books',
  'Real Estate Books',
  'Entrepreneurship Books',
  'Marketing Books',
  'Sales Books',
  'Management Books',
  'Public Speaking Books',
  'Debate Books',
  'Fiction Books',
  'Non-Fiction Books',
  'Poetry Books',
  'Screenwriting Software',
  'Acting Classes',
  'Directing Books',
  'Producing Books',
  'Film Editing Software',
  'Sound Design Software',
  'Visual Effects Software',
  'Animation Software',
  'Web Development Courses',
  'Mobile Development Courses',
  'Data Science Courses',
  'Machine Learning Courses',
  'AI Courses',
  'Cybersecurity Courses',
  'Network Administration Courses',
  'Cloud Computing Courses',
  'DevOps Tools',
  'Technical Writing Courses',
  'Gourmet Food',
  'Craft Beer',
  'Fine Wine',
  'Specialty Coffee',
  'Artisan Tea',
  'Handmade Crafts',
  'Vintage Items',
  'Antiques',
  'Rare Books',
  'First Editions',
  'Signed Copies',
  'Limited Edition Items',
  'Personalized Gifts',
  'Experiences (e.g., hot air balloon ride, spa day)',
  'Classes/Workshops (e.g., pottery class, cooking class)',
  'Tickets to Events (e.g., concerts, sports games, theater)',
  'Donations to Charity',
  'Eco-friendly Products',
  'Sustainable Goods',
  'Fair Trade Products',
  'Local Artisan Products',
  'Handcrafted Items',
  'Bespoke Items',
  'Custom Made Gifts',
  'Cats',
  'Dogs',
  'Pets',
  'Animals',
  'Birdwatching',
  'Aquariums',
  'Reptiles',
  'Italian Food',
  'Mexican Food',
  'Japanese Food',
  'Chinese Food',
  'Indian Food',
  'French Cuisine',
  'Baking',
  'Grilling',
  'BBQ',
  'Wine Tasting',
  'Cocktails',
  'Coffee',
  'Tea',
  'Craft Beer',
  'Chocolate',
  'Desserts',
  'Ice Cream',
  'Cheese',
  'Sushi',
  'Pizza',
  'Burgers',
  'Steak',
  'Vegan Food',
  'Vegetarian Food',
  'Gluten-Free Food',
  'Keto Diet',
  'Paleo Diet',
  'Mediterranean Diet',
  'Healthy Eating',
  'Organic Food',
  'Farmers Markets',
  'Cooking Shows',
  'Food Blogs',
  'Restaurant Reviews',
  'Food Photography',
  'Food Travel',
  'Culinary Arts',
  'Mixology',
  'Home Brewing',
  'Wine Collecting',
  'Coffee Roasting',
  'Tea Ceremonies',
  'Chocolate Making',
  'Cheese Making',
  'Fermentation',
  'Pickling',
  'Canning',
  'Preserving',
  'Gardening',
  'Houseplants',
  'Succulents',
  'Bonsai',
  'Flower Arranging',
  'Herb Gardening',
  'Vegetable Gardening',
  'Fruit Trees',
  'Composting',
  'Sustainable Living',
  'Zero Waste',
  'Minimalism',
  'Decluttering',
  'Organization',
  'Home Improvement',
  'DIY Projects',
  'Woodworking',
  'Metalworking',
  'Pottery',
  'Ceramics',
  'Glassblowing',
  'Jewelry Making',
  'Blacksmithing',
  'Leatherworking',
  'Weaving',
  'Spinning',
  'Dyeing',
  'Printmaking',
  'Calligraphy',
  'Bookbinding',
  'Paper Crafts',
  'Origami',
  'Quilling',
  'Scrapbooking',
  'Model Building',
  'RC Vehicles',
  'Drones',
  'Robotics',
  'Electronics',
  '3D Printing',
  'Virtual Reality',
  'Augmented Reality',
  'Gaming',
  'Board Games',
  'Card Games',
  'Puzzles',
  'Chess',
  'Poker',
  'Bridge',
  'Backgammon',
  'Mahjong',
  'Roleplaying Games',
  'Escape Rooms',
  'Live Music',
  'Concerts',
  'Festivals',
  'Theater',
  'Musicals',
  'Opera',
  'Ballet',
  'Comedy Shows',
  'Improv',
  'Stand-up Comedy',
  'Open Mics',
  'Karaoke',
  'Dancing',
  'Salsa',
  'Tango',
  'Swing',
  'Ballroom',
  'Hip Hop',
  'Breakdancing',
  'Tap',
  'Ballet',
  'Contemporary',
  'Jazz',
  'Modern',
  'Folk',
  'Belly Dancing',
  'Pole Dancing',
  'Aerial Arts',
  'Circus Arts',
  'Juggling',
  'Magic',
  'Ventriloquism',
  'Puppetry',
  'Storytelling',
  'Poetry Slams',
  'Spoken Word',
  'Book Clubs',
  'Writing Groups',
  'Reading Challenges',
  'Literary Fiction',
  'Science Fiction',
  'Fantasy',
  'Mystery',
  'Thriller',
  'Horror',
  'Romance',
  'Historical Fiction',
  'Biographies',
  'Memoirs',
  'Self-Help',
  'Psychology',
  'Philosophy',
  'Spirituality',
  'Religion',
  'Meditation',
  'Yoga',
  'Tai Chi',
  'Qi Gong',
  'Pilates',
  'Barre',
  'CrossFit',
  'Weightlifting',
  'Powerlifting',
  'Bodybuilding',
  'Calisthenics',
  'Gymnastics',
  'Parkour',
  'Martial Arts',
  'Boxing',
  'Wrestling',
  'Fencing',
  'Archery',
  'Shooting',
  'Hunting',
  'Fishing',
  'Camping',
  'Hiking',
  'Backpacking',
  'Rock Climbing',
  'Mountaineering',
  'Caving',
  'Kayaking',
  'Canoeing',
  'Rafting',
  'Sailing',
  'Surfing',
  'Scuba Diving',
  'Snorkeling',
  'Swimming',
  'Triathlons',
  'Marathons',
  'Running',
  'Cycling',
  'Mountain Biking',
  'BMX',
  'Skateboarding',
  'Rollerblading',
  'Ice Skating',
  'Hockey',
  'Skiing',
  'Snowboarding',
  'Snowshoeing',
  'Ice Climbing',
  'Dog Sledding',
  'Horseback Riding',
  'Polo',
  'Equestrian Sports',
  'Falconry',
  'Birdwatching',
  'Astronomy',
  'Stargazing',
  'Meteorology',
  'Geology',
  'Paleontology',
  'Archaeology',
  'History',
  'Genealogy',
  'Anthropology',
  'Sociology',
  'Political Science',
  'Economics',
  'Finance',
  'Investing',
  'Real Estate',
  'Entrepreneurship',
  'Marketing',
  'Sales',
  'Management',
  'Leadership',
  'Public Speaking',
  'Debate',
  'Negotiation',
  'Conflict Resolution',
  'Mediation',
  'Counseling',
  'Coaching',
  'Mentoring',
  'Teaching',
  'Tutoring',
  'Childcare',
  'Elder Care',
  'Pet Care',
  'Animal Rescue',
  'Volunteering',
  'Activism',
  'Social Justice',
  'Human Rights',
  'Environmental Conservation',
  'Sustainability',
  'Renewable Energy',
  'Green Living',
  'Eco-Tourism',
  'Community Building',
  'Neighborhood Associations',
];

export const brandSuggestions: string[] = [
  'Nike',
  'Adidas',
  'Apple',
  'Samsung',
  'Sony',
  'Microsoft',
  'Google',
  'Amazon',
  'Patagonia',
  'The North Face',
  'Zara',
  'H&M',
  'Gucci',
  'Louis Vuitton',
  'Chanel',
  'Dior',
  'Hermès',
  'Rolex',
  'Starbucks',
  "McDonald's",
  'Burger King',
  'Subway',
  'KFC',
  'Pizza Hut',
  "Domino's",
  'Nestlé',
  'Unilever',
  'Procter & Gamble',
  'Johnson & Johnson',
  'Amazon',
  'eBay',
  'Walmart',
  'Target',
  'Costco',
  'Google',
  'Microsoft',
  'Apple',
  'LVMH Moët Hennessy Louis Vuitton',
  'Kering',
  'Richemont',
  'Estée Lauder Companies',
  "L'Oréal",
  'Sephora',
  'Ulta Beauty',
  'MAC Cosmetics',
  'Fenty Beauty',
  'Rare Beauty',
  'Kylie Cosmetics',
  'Goop',
  'Dyson',
  'Peloton',
  'Nintendo',
  'PlayStation',
  'Xbox',
  'Steam',
  'Netflix',
  'Spotify',
  'Apple Music',
  'Amazon Prime Video',
  'Disney+',
  'HBO Max',
  'Hulu',
  'YouTube Premium',
  'MasterClass',
  'Skillshare',
  'Coursera',
  'Udemy',
  'edX',
  'Khan Academy',
  'Fender',
  'Gibson',
  'Taylor Guitars',
  'Martin Guitars',
  'Yamaha',
  'Steinway & Sons',
  'LEGO',
  'Hasbro',
  'Mattel',
  'Nintendo',
  'PlayStation',
  'Xbox',
  'Canon',
  'Nikon',
  'Sony',
  'Fujifilm',
  'Panasonic',
  'DJI',
  'GoPro',
  'Fitbit',
  'Garmin',
  'Apple Watch',
  'Samsung Galaxy Watch',
  'Kindle',
  'Kobo',
  'Nook',
  'IKEA',
  'West Elm',
  'Pottery Barn',
  'Crate & Barrel',
  'Williams Sonoma',
  'Sur La Table',
  'Le Creuset',
  'KitchenAid',
  'Dyson',
  'SharkNinja',
  'iRobot',
  'Lululemon',
  'Athleta',
  'Gymshark',
  'Nike',
  'Adidas',
  'Under Armour',
  'REI',
  'Backcountry',
  "Cabela's",
  'Bass Pro Shops',
  'Tiffany & Co.',
  'Cartier',
  'Pandora',
  'Swarovski',
  'Warby Parker',
  'Ray-Ban',
  'Oakley',
  'Sephora',
  'Ulta Beauty',
  'MAC Cosmetics',
  'Fenty Beauty',
  'Rare Beauty',
  'Kylie Cosmetics',
  'Goop',
  'Airbnb',
  'Booking.com',
  'Expedia',
  'Tripadvisor',
  'Southwest Airlines',
  'Delta Air Lines',
  'American Airlines',
  'United Airlines',
  'Marriott Bonvoy',
  'Hilton Honors',
  'Hyatt World of Hyatt',
  'Uber',
  'Lyft',
  'DoorDash',
  'Uber Eats',
  'Grubhub',
  'Instacart',
  'Shipt',
  'Chewy',
  'PetSmart',
  'Petco',
  'Home Depot',
  "Lowe's",
  'Best Buy',
  'Apple Store',
  'Microsoft Store',
  'Barnes & Noble',
  'Books-A-Million',
  'GameStop',
  'AMC Theatres',
  'Regal Cinemas',
  'Live Nation',
  'Ticketmaster',
  'StubHub',
  'SeatGeek',
  'Etsy',
  'Shopify',
  'Patreon',
  'Substack',
  'Kickstarter',
  'Indiegogo',
  'GoFundMe',
  'Charity: Water',
  'Doctors Without Borders',
  'Red Cross',
  'Patagonia',
  'REI',
  'Allbirds',
  'Everlane',
  'Toms',
  'Lush',
  'The Body Shop',
  "Kiehl's",
  'Aesop',
  'LEGO',
  'Hasbro',
  'Mattel',
  'Nintendo',
  'PlayStation',
  'Xbox',
  'Spotify',
  'Apple Music',
  'YouTube Music',
  'Netflix',
  'Hulu',
  'HBO Max',
  'Disney+',
  'Amazon Prime Video',
  'MasterClass',
  'Skillshare',
  'Audible',
  'LibriVox',
  'Duolingo',
  'Babbel',
  'Coursera',
  'Udemy',
  'edX',
  'Fender',
  'Gibson',
  'Yamaha',
  'Canon',
  'Nikon',
  'Sony',
  'GoPro',
  'DJI',
  'Fitbit',
  'Garmin',
  'Kindle',
  'Kobo',
  'IKEA',
  'West Elm',
  'Pottery Barn',
  'Le Creuset',
  'KitchenAid',
  'Lululemon',
  'Nike',
  'Adidas',
  'REI',
  'Patagonia',
  'Tiffany & Co.',
  'Cartier',
  'Warby Parker',
  'Ray-Ban',
  'Sephora',
  'Ulta Beauty',
  'Airbnb',
  'Booking.com',
  'Southwest Airlines',
  'Delta Air Lines',
  'Marriott Bonvoy',
  'Hilton Honors',
  'Uber',
  'Lyft',
  'DoorDash',
  'Uber Eats',
  'Instacart',
  'Chewy',
  'PetSmart',
  'Home Depot',
  "Lowe's",
  'Best Buy',
  'Barnes & Noble',
  'GameStop',
  'AMC Theatres',
  'Live Nation',
  'StubHub',
  'Etsy',
  'Patreon',
  'Kickstarter',
  'GoFundMe',
  'Charity: Water',
];

// Enhanced motivational messages with categories
export interface MotivationalMessage {
  text: string;
  profileVariant?: string; // NEW: Profile-specific version with {profileName} placeholder
  category: 'discovery' | 'connection' | 'planning' | 'achievement' | 'seasonal';
  icon: string; // Feather icon name
  priority: number; // Higher number = higher priority for contextual selection
  allowsProfilePersonalization: boolean; // NEW: Whether this message can be personalized
}

export const enhancedMotivationalMessages: MotivationalMessage[] = [
  // Discovery Category (Gift hunting and exploration) - 9/10 with profile variants
  {
    text: "The perfect gift is waiting to be discovered",
    profileVariant: "The perfect gift for {profileName} is waiting to be discovered",
    category: 'discovery',
    icon: 'search',
    priority: 3,
    allowsProfilePersonalization: true
  },
  {
    text: "Every person has that one special thing they've been wanting",
    profileVariant: "{profileName} has that one special thing they've been wanting",
    category: 'discovery',
    icon: 'gift',
    priority: 2,
    allowsProfilePersonalization: true
  },
  {
    text: "Great gifts often hide in unexpected places",
    category: 'discovery',
    icon: 'eye',
    priority: 2,
    allowsProfilePersonalization: true
  },
  {
    text: "Listen closely - the best gift ideas come from everyday conversations",
    profileVariant: "Listen to {profileName} closely - the best gift ideas come from everyday conversations",
    category: 'discovery',
    icon: 'message-circle',
    priority: 4,
    allowsProfilePersonalization: true
  },
  {
    text: "Explore their interests - you'll find gift gold there",
    profileVariant: "Explore {profileName}'s interests - you'll find gift gold there",
    category: 'discovery',
    icon: 'compass',
    priority: 3,
    allowsProfilePersonalization: true
  },
  {
    text: "The right gift feels like magic to the receiver",
    profileVariant: "The right gift will feel like magic to {profileName}",
    category: 'discovery',
    icon: 'sparkles',
    priority: 2,
    allowsProfilePersonalization: true
  },
  {
    text: "Sometimes the simplest ideas make the most meaningful gifts",
    profileVariant: "Sometimes the simplest ideas make the most meaningful gifts for {profileName}",
    category: 'discovery',
    icon: 'lightbulb',
    priority: 3,
    allowsProfilePersonalization: true
  },
  {
    text: "Pay attention to what makes their eyes light up",
    profileVariant: "Pay attention to what makes {profileName}'s eyes light up",
    category: 'discovery',
    icon: 'eye',
    priority: 4,
    allowsProfilePersonalization: true
  },
  {
    text: "The best gifts solve little problems they didn't know they had",
    profileVariant: "The best gifts solve little problems {profileName} didn't know they had",
    category: 'discovery',
    icon: 'tool',
    priority: 3,
    allowsProfilePersonalization: true
  },
  {
    text: "Think about experiences, not just things",
    category: 'discovery',
    icon: 'map-pin',
    priority: 2,
    allowsProfilePersonalization: true
  },

  // Connection Category (Relationship and thoughtfulness) - 7/10 with profile variants
  {
    text: "Thoughtful giving strengthens the bonds that matter most",
    profileVariant: "Thoughtful gifts will strengthen your bond with {profileName}",
    category: 'connection',
    icon: 'heart',
    priority: 4,
    allowsProfilePersonalization: true
  },
  {
    text: "A gift is a way of saying 'I was thinking of you'",
    profileVariant: "A gift is a way of telling {profileName} 'I was thinking of you'",
    category: 'connection',
    icon: 'message-circle',
    priority: 3,
    allowsProfilePersonalization: true
  },
  {
    text: "The best gifts show how well you know someone",
    profileVariant: "The best gifts will show how well you know {profileName}",
    category: 'connection',
    icon: 'users',
    priority: 4,
    allowsProfilePersonalization: true
  },
  {
    text: "Love is in the details - and the thought behind them",
    category: 'connection',
    icon: 'heart',
    priority: 3,
    allowsProfilePersonalization: true
  },
  {
    text: "Every gift tells a story about your relationship",
    profileVariant: "Every gift tells a story about your relationship with {profileName}",
    category: 'connection',
    icon: 'book-open',
    priority: 2,
    allowsProfilePersonalization: true
  },
  {
    text: "Small surprises create the biggest smiles",
    profileVariant: "Small surprises will create the biggest smiles on {profileName}'s face",
    category: 'connection',
    icon: 'smile',
    priority: 3,
    allowsProfilePersonalization: true
  },
  {
    text: "Meaningful gifts speak louder than words",
    category: 'connection',
    icon: 'volume-2',
    priority: 2,
    allowsProfilePersonalization: true
  },
  {
    text: "The joy of giving is the greatest gift of all",
    category: 'connection',
    icon: 'gift',
    priority: 2,
    allowsProfilePersonalization: true
  },
  {
    text: "Making someone feel special is a gift in itself",
    profileVariant: "Making {profileName} feel special is a gift in itself",
    category: 'connection',
    icon: 'star',
    priority: 4,
    allowsProfilePersonalization: true
  },
  {
    text: "Your thoughtfulness today becomes their treasured memory tomorrow",
    profileVariant: "Your thoughtfulness today becomes {profileName}'s treasured memory tomorrow",
    category: 'connection',
    icon: 'clock',
    priority: 3,
    allowsProfilePersonalization: true
  },

  // Planning Category (Organization and preparation) - 8/8 with profile variants
  {
    text: "Great gifts start with great planning",
    profileVariant: "Finding the perfect gift for {profileName} starts with great planning",
    category: 'planning',
    icon: 'calendar',
    priority: 5,
    allowsProfilePersonalization: true
  },
  {
    text: "The early gift-giver gets the perfect present",
    profileVariant: "Planning ahead means you'll find the perfect gift for {profileName}",
    category: 'planning',
    icon: 'clock',
    priority: 4,
    allowsProfilePersonalization: true
  },
  {
    text: "A little preparation goes a long way in gift-giving",
    profileVariant: "A little preparation goes a long way in finding gifts for {profileName}",
    category: 'planning',
    icon: 'check-square',
    priority: 3,
    allowsProfilePersonalization: true
  },
  {
    text: "Keep track of the little things they mention wanting",
    profileVariant: "Keep track of the little things {profileName} mentions wanting",
    category: 'planning',
    icon: 'list',
    priority: 4,
    allowsProfilePersonalization: true
  },
  {
    text: "Planning ahead means stress-free celebrations",
    profileVariant: "Planning ahead for {profileName}'s special days means stress-free celebrations",
    category: 'planning',
    icon: 'shield-check',
    priority: 3,
    allowsProfilePersonalization: true
  },
  {
    text: "Your future self will thank you for preparing now",
    profileVariant: "Your future self will thank you for planning {profileName}'s gifts now",
    category: 'planning',
    icon: 'arrow-right',
    priority: 2,
    allowsProfilePersonalization: true
  },
  {
    text: "Organization is the secret to memorable gift-giving",
    profileVariant: "Organization is the secret to memorable gifts for {profileName}",
    category: 'planning',
    icon: 'folder',
    priority: 2,
    allowsProfilePersonalization: true
  },
  {
    text: "Every important date deserves advance preparation",
    profileVariant: "Every important date with {profileName} deserves advance preparation",
    category: 'planning',
    icon: 'target',
    priority: 4,
    allowsProfilePersonalization: true
  },

  // Achievement Category (Celebrating success) - 6/7 with profile variants
  {
    text: "You're becoming a gift-giving expert!",
    profileVariant: "You're becoming an expert at choosing gifts for {profileName}!",
    category: 'achievement',
    icon: 'award',
    priority: 3,
    allowsProfilePersonalization: true
  },
  {
    text: "Every profile you complete makes you a better gift-giver",
    category: 'achievement',
    icon: 'trending-up',
    priority: 4,
    allowsProfilePersonalization: true
  },
  {
    text: "Look how organized you're becoming with gift planning!",
    profileVariant: "Look how organized you're becoming with planning gifts for {profileName}!",
    category: 'achievement',
    icon: 'check-circle',
    priority: 3,
    allowsProfilePersonalization: true
  },
  {
    text: "Your attention to detail makes all the difference",
    profileVariant: "Your attention to {profileName}'s preferences makes all the difference",
    category: 'achievement',
    icon: 'star',
    priority: 2,
    allowsProfilePersonalization: true
  },
  {
    text: "Great gift-givers are made, not born - and you're proving it",
    profileVariant: "Great gift-givers are made, not born - and you're proving it with {profileName}",
    category: 'achievement',
    icon: 'trophy',
    priority: 3,
    allowsProfilePersonalization: true
  },
  {
    text: "Every gift idea you save is a future moment of joy",
    profileVariant: "Every gift idea you save for {profileName} is a future moment of joy",
    category: 'achievement',
    icon: 'bookmark',
    priority: 2,
    allowsProfilePersonalization: true
  },
  {
    text: "You're building something beautiful - a legacy of thoughtfulness",
    category: 'achievement',
    icon: 'heart',
    priority: 4,
    allowsProfilePersonalization: true
  },

  // Seasonal Category (Time-appropriate themes) - 1/5 with profile variants
  {
    text: "Every season brings new opportunities to show you care",
    category: 'seasonal',
    icon: 'sun',
    priority: 2,
    allowsProfilePersonalization: true
  },
  {
    text: "Holiday magic starts with thoughtful preparation",
    category: 'seasonal',
    icon: 'sparkles',
    priority: 4,
    allowsProfilePersonalization: true
  },
  {
    text: "This time of year is perfect for creating special memories",
    profileVariant: "This time of year is perfect for creating special memories with {profileName}",
    category: 'seasonal',
    icon: 'calendar',
    priority: 3,
    allowsProfilePersonalization: true
  },
  {
    text: "Seasonal celebrations are made special by the gifts we give",
    category: 'seasonal',
    icon: 'gift',
    priority: 3,
    allowsProfilePersonalization: true
  },
  {
    text: "Some gifts are perfect for any time of year, others are made for moments like this",
    category: 'seasonal',
    icon: 'clock',
    priority: 2,
    allowsProfilePersonalization: true
  }
];

// Function to get contextual messages based on user state
export const getContextualMessage = (context: {
  hasUpcomingDates?: boolean;
  profileCompletionCount?: number;
  totalProfiles?: number;
  timeOfYear?: 'holiday' | 'summer' | 'back-to-school' | 'spring' | 'regular';
  dayOfWeek?: 'weekend' | 'weekday';
  selectedProfileName?: string; // NEW: Profile name for personalization
}): MotivationalMessage => {
  let eligibleMessages = [...enhancedMotivationalMessages];
  
  // Filter by context
  if (context.hasUpcomingDates) {
    // Prioritize planning messages when they have upcoming dates
    eligibleMessages = eligibleMessages.filter(msg => 
      msg.category === 'planning' || msg.category === 'discovery'
    );
  }
  
  if (context.profileCompletionCount !== undefined && context.totalProfiles !== undefined) {
    const completionRate = context.profileCompletionCount / context.totalProfiles;
    if (completionRate > 0.7) {
      // High completion rate - show achievement messages
      eligibleMessages = eligibleMessages.filter(msg => 
        msg.category === 'achievement' || msg.category === 'connection'
      );
    } else if (completionRate < 0.3) {
      // Low completion rate - encourage discovery and planning
      eligibleMessages = eligibleMessages.filter(msg => 
        msg.category === 'discovery' || msg.category === 'planning'
      );
    }
  }
  
  if (context.timeOfYear === 'holiday') {
    // During holiday season, prioritize seasonal messages
    eligibleMessages = eligibleMessages.filter(msg => 
      msg.category === 'seasonal' || msg.category === 'planning'
    );
  }
  
  // If no context filters apply or result in empty array, use all messages
  if (eligibleMessages.length === 0) {
    eligibleMessages = enhancedMotivationalMessages;
  }
  
  // NEW: Profile personalization logic
  if (context.selectedProfileName && context.selectedProfileName.trim()) {
    // Filter messages that can be personalized
    const personalizable = eligibleMessages.filter(msg => 
      msg.allowsProfilePersonalization && msg.profileVariant
    );
    
    // 60% chance to use personalized version when available
    if (personalizable.length > 0 && Math.random() < 0.6) {
      // Sort personalizable messages by priority
      personalizable.sort((a, b) => b.priority - a.priority);
      const highPriorityPersonalizable = personalizable.filter(msg => 
        msg.priority >= Math.max(3, Math.max(...personalizable.map(m => m.priority)) - 1)
      );
      
      const selectedMessages = highPriorityPersonalizable.length > 0 ? highPriorityPersonalizable : personalizable;
      const selectedMessage = selectedMessages[Math.floor(Math.random() * selectedMessages.length)];
      
      // Create personalized version with profile name
      const truncatedName = context.selectedProfileName.length > 15 
        ? context.selectedProfileName.substring(0, 15) + '...' 
        : context.selectedProfileName;
      
      return {
        ...selectedMessage,
        text: selectedMessage.profileVariant!.replace('{profileName}', truncatedName)
      };
    }
  }
  
  // Fallback to existing general message selection logic
  eligibleMessages.sort((a, b) => b.priority - a.priority);
  const highPriorityMessages = eligibleMessages.filter(msg => 
    msg.priority >= Math.max(3, Math.max(...eligibleMessages.map(m => m.priority)) - 1)
  );
  
  const selectedMessages = highPriorityMessages.length > 0 ? highPriorityMessages : eligibleMessages;
  return selectedMessages[Math.floor(Math.random() * selectedMessages.length)];
};

// Fallback to legacy messages for compatibility
export const legacyMotivationalMessages = [
  "Making your loved ones happy starts with small surprises",
  "Every gift tells a story",
  "The best gifts come from the heart",
  "Thoughtful giving strengthens relationships",
  "A perfect gift shows how much you care",
  "Great gifts create lasting memories",
  "The joy of giving is the greatest gift of all",
  "Small gestures, big impact",
  "Love is in the details",
  "Meaningful gifts speak louder than words"
];
