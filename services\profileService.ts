import {
  collection,
  getDocs,
  doc,
  getDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  documentId,
  Timestamp,
  serverTimestamp,
  QueryDocumentSnapshot,
  orderBy,
  limit,
  startAfter,
} from 'firebase/firestore';
import { getFunctions, httpsCallable } from 'firebase/functions';
import { db } from '../firebaseConfig'; // Assuming db is exported from firebaseConfig.ts
import { SignificantOtherProfile } from '../functions/src/types/firestore'; // Import the main profile type

// Define the collection name as a constant for consistency
const COLLECTION_NAME = 'significant_others';

// Type for data needed when adding a new profile
// Excludes fields managed internally (userId, profileId, timestamps, arrays initialized empty)
export type AddProfileData = Omit<SignificantOtherProfile,
  'userId' |
  'profileId' |
  'createdAt' |
  'updatedAt'
> & {
  wishlistItems?: Array<{ // Include wishlistItems as optional
    item: string;
    link?: string;
    notes?: string;
    price?: number;
    priority?: 'low' | 'medium' | 'high';
    isPurchased?: boolean;
  }>;
  pastGiftsGiven?: Array<{
    item: string;
    occasion?: string;
    date?: Timestamp | null;
    reaction?: string;
  }>;
  generalNotes?: Array<{
    note: string;
    date?: Timestamp | null;
  }>;
};

/**
 * Adds a new Significant Other profile using the secure Cloud Function.
 * @param profileData - The profile data to add (matching AddProfileData type).
 * @returns The ID of the newly created Firestore document.
 */
export const addSignificantOther = async (
  profileData: AddProfileData
): Promise<string> => {
  console.log('Adding profile using Cloud Function');
  try {
    const functions = getFunctions();
    const createSignificantOther = httpsCallable(functions, 'createSignificantOther');
    const result = await createSignificantOther(profileData);
    
    // The Cloud Function returns an object with a data property containing the new document ID
    const { data } = result.data as { data: { id: string } };
    const newDocId = data.id;
    
    console.log(`Profile added successfully with ID: ${newDocId}`);
    return newDocId;
  } catch (error) {
    console.error('Error adding significant other profile via Cloud Function:', error);
    throw new Error('Failed to add significant other profile.');
  }
};

/**
 * Fetches a paginated list of Significant Other profiles for a user.
 * @param userId - The ID of the user whose profiles to fetch.
 * @param lastVisible - The last visible document snapshot for pagination.
 * @returns An object containing the profiles and the last visible document.
 */
export const getSignificantOthers = async (
  userId: string,
  lastVisible: QueryDocumentSnapshot | null = null
): Promise<{
  profiles: SignificantOtherProfile[];
  lastVisible: QueryDocumentSnapshot | null;
}> => {
  console.log(`Fetching profiles for user: ${userId}`);
  try {
    let q = query(
      collection(db, COLLECTION_NAME),
      where('userId', '==', userId),
      orderBy('createdAt', 'desc'),
      limit(20)
    );

    if (lastVisible) {
      q = query(q, startAfter(lastVisible));
    }

    const querySnapshot = await getDocs(q);
    const profiles = querySnapshot.docs.map((doc) => ({
      profileId: doc.id,
      ...(doc.data() as Omit<SignificantOtherProfile, 'profileId'>),
    }));

    const newLastVisible = querySnapshot.docs[querySnapshot.docs.length - 1] || null;

    console.log(`Found ${profiles.length} profiles for user ${userId}`);
    return { profiles, lastVisible: newLastVisible };
  } catch (error) {
    console.error('Error fetching significant other profiles:', error);
    throw new Error('Failed to fetch significant other profiles.');
  }
};

/**
 * Fetches a single Significant Other profile by its ID, ensuring it belongs to the user.
 * @param userId - The ID of the user requesting the profile.
 * @param profileId - The ID of the profile document to fetch.
 * @returns The profile data or null if not found or not owned by the user.
 */
export const getSignificantOtherById = async (
  userId: string,
  profileId: string
): Promise<SignificantOtherProfile | null> => {
  console.log(`Fetching profile ID: ${profileId} for user: ${userId}`);
  try {
    const collectionRef = collection(db, COLLECTION_NAME);
    const q = query(
      collectionRef,
      where(documentId(), '==', profileId),
      where('userId', '==', userId)
    );
    
    const querySnapshot = await getDocs(q);
    
    if (!querySnapshot.empty) {
      const doc = querySnapshot.docs[0];
      const data = doc.data() as Omit<SignificantOtherProfile, 'profileId'>;
      console.log(`Profile ${profileId} found and verified for user ${userId}`);
      return { profileId: doc.id, ...data };
    } else {
      console.log(`Profile with ID: ${profileId} not found for user: ${userId}`);
      return null; // Document does not exist or not owned by the user
    }
  } catch (error) {
    console.error(`Error fetching significant other profile ${profileId}:`, error);
    throw new Error('Failed to fetch significant other profile.');
  }
};

/**
 * Updates an existing Significant Other profile, ensuring ownership.
 * @param userId - The ID of the user updating the profile.
 * @param profileId - The ID of the profile document to update.
 * @param updatedData - An object containing the fields to update (Partial<SignificantOtherProfile>).
 */
export const updateSignificantOther = async (
  userId: string,
  profileId: string,
  updatedData: Partial<Omit<SignificantOtherProfile, 'userId' | 'profileId' | 'createdAt'>> // Exclude fields that shouldn't be directly updated
): Promise<void> => {
  console.log(`Updating profile ID: ${profileId} for user: ${userId}`);
  const docRef = doc(db, COLLECTION_NAME, profileId);

  try {
    // Firestore security rules will enforce ownership - no need for client-side check
    await updateDoc(docRef, {
      ...updatedData, // Use the original updatedData
      updatedAt: Timestamp.now(), // Update the timestamp
    });
    console.log(`Profile ${profileId} updated successfully by user ${userId}`);
  } catch (error) {
    console.error(`Error updating significant other profile ${profileId}:`, error);
    // Firestore will throw permission errors if user doesn't own the document
    if (error instanceof Error && error.message.includes('permission-denied')) {
      throw new Error('Permission denied: You can only update your own profiles.');
    }
    throw new Error('Failed to update significant other profile.');
  }
};

/**
 * Deletes a Significant Other profile, ensuring ownership.
 * @param userId - The ID of the user deleting the profile.
 * @param profileId - The ID of the profile document to delete.
 */
export const deleteSignificantOther = async (
  userId: string,
  profileId: string
): Promise<void> => {
  console.log(`Attempting to delete profile ID: ${profileId} by user: ${userId}`);
  const docRef = doc(db, COLLECTION_NAME, profileId);

  try {
    // Firestore security rules will enforce ownership - no need for client-side check
    await deleteDoc(docRef);
    console.log(`Profile ${profileId} deleted successfully by user ${userId}`);
  } catch (error) {
    console.error(`Error deleting significant other profile ${profileId}:`, error);
    // Firestore will throw permission errors if user doesn't own the document
    if (error instanceof Error && error.message.includes('permission-denied')) {
      throw new Error('Permission denied: You can only delete your own profiles.');
    }
    throw new Error('Failed to delete significant other profile.');
  }
};