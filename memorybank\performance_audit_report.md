### **Performance Audit Report**

This report details critical performance issues found in the Giftmi application, along with recommendations for fixes that maintain functionality. The issues are categorized by area of concern.

---

### 1. **Component Rendering & State Management**

#### **Issue 1.1: Unmemoized Components and Prop Drilling**

*   **Location:** Widespread across the application, especially in `app/(app)/profiles/[profileId]/index.tsx` and screens under `app/(app)/profiles/[profileId]/`.
*   **Problem:** Many components are re-rendered unnecessarily because they are not wrapped in `React.memo`, and props are passed down through multiple layers of components (prop drilling). This causes a cascading re-render effect, even when the child components' props haven't changed. For example, the `ProfileViewScreen` in `index.tsx` has many sections, and a state change at the top level will re-render all of them.
*   **Fix:**
    1.  Wrap components that receive props and don't have their own internal state with `React.memo`. This is especially important for list items and section components (e.g., `Section`, `ProfileField`, `ProfileListItem`).
    2.  Use context or a state management library (like Zustand or Jotai, if you want to keep it simple) to avoid prop drilling for deeply nested components. This is particularly relevant for user and profile data that is used across many screens.

#### **Issue 1.2: Expensive Computations in Render Cycle**

*   **Location:** `app/(app)/profiles/[profileId]/index.tsx`
*   **Problem:** The `calculateProfileStrength` function is called on every render of the `ProfileViewScreen`. This is an expensive computation that iterates through the profile data.
*   **Fix:** Memoize the result of `calculateProfileStrength` using `useMemo`. The calculation should only re-run when the `profile` data changes.

    ```javascript
    const profileStrength = useMemo(() => calculateProfileStrength(profile), [profile]);
    ```

#### **Issue 1.3: Overuse of `useState` and `useEffect`**

*   **Location:** `app/(app)/home.tsx`, `app/(app)/calendar.tsx`
*   **Problem:** These screens have multiple `useState` and `useEffect` hooks that are tightly coupled, leading to complex and sometimes unpredictable render cycles. For example, in `HomeScreen`, `useEffect` hooks are used to sync state between the `useCalendarData` hook and the local state, which can cause redundant re-renders.
*   **Fix:**
    1.  Consolidate related state into a single `useReducer` or a custom hook. This can simplify state transitions and reduce the number of re-renders.
    2.  Instead of syncing state with `useEffect`, derive state from props or other state variables where possible. For example, instead of having a separate `selectedProfile` state, derive it from the `profiles` and `selectedProfileId`.

---

### 2. **Data Fetching and Management**

#### **Issue 2.1: Redundant Data Fetching and Lack of Centralized State**

*   **Location:** `hooks/useCalendarData.ts`, `app/(app)/home.tsx`, `app/(app)/search.tsx`
*   **Problem:** The `useCalendarData` hook is a "god hook" that fetches and manages all profile and calendar data. This makes it difficult to reuse and optimize. Other screens, like `HomeScreen`, also fetch their own data, leading to redundant network requests and a lack of a single source of truth.
*   **Fix:**
    1.  Break down `useCalendarData` into smaller, more focused hooks. For example, a `useProfiles` hook for fetching and managing profiles, and a `useCalendarEvents` hook for processing calendar data.
    2.  Implement a centralized data fetching and caching layer. This could be done with a context provider or a library like React Query or SWR. This will ensure that data is fetched only once and is available to all components that need it.

#### **Issue 2.2: Inefficient Data Processing**

*   **Location:** `hooks/useCalendarData.ts` (`processDates` function)
*   **Problem:** The `processDates` function iterates through all profiles and all their dates every time it's called. This is inefficient, especially as the number of profiles and dates grows.
*   **Fix:**
    1.  Optimize the `processDates` function to only process the data that has changed.
    2.  Consider moving some of the data processing to a web worker to avoid blocking the main thread.
    3.  If possible, pre-process some of the data on the server and send it to the client in a more optimized format.

---

### 3. **Lists and Animations**

#### **Issue 3.1: `FlatList` Performance**

*   **Location:** `app/(app)/profiles/[profileId]/dates.tsx`, `notes.tsx`, `past-gifts.tsx`, `feedback.tsx`
*   **Problem:** The `FlatList` components in these screens are not fully optimized. They are missing props like `getItemLayout`, and `windowSize` could be tuned.
*   **Fix:**
    1.  Implement `getItemLayout` to avoid the cost of measuring content dynamically. This is especially important for lists with fixed-height items.
    2.  Experiment with the `windowSize` and `maxToRenderPerBatch` props to find the optimal balance between memory usage and responsiveness.
    3.  Ensure that the `keyExtractor` is using a unique and stable key for each item.

#### **Issue 3.2: Animation Performance**

*   **Location:** Widespread
*   **Problem:** Some animations are not using the native driver, which can lead to performance issues, especially on older devices.
*   **Fix:** Use the `useNativeDriver: true` option for all animations that don't affect the layout (e.g., opacity, transform). For layout animations, use the `LayoutAnimation` API or a library like `react-native-reanimated`'s Layout Animations.

---

### 4. **Cloud Functions & Backend**

#### **Issue 4.1: Inefficient Firestore Queries**

*   **Location:** `functions/src/index.ts` (`sendEventReminders` function)
*   **Problem:** The `sendEventReminders` function queries the entire `significant_others` collection to find users with upcoming events. This is inefficient and will not scale.
*   **Fix:** Create a separate collection for events or reminders that can be queried more efficiently. For example, you could have a top-level `events` collection where each document represents an event and contains the `userId` of the user to be notified.

