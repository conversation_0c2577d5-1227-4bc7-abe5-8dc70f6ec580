import React, { useState } from 'react';
import { Modal, View, Text, TouchableOpacity, Platform } from 'react-native';
import DateTimePicker from '@react-native-community/datetimepicker';
import Input from '../ui/Input';
import Button from '../ui/Button';
import { CustomDate } from '../../functions/src/types/firestore';
import { format } from 'date-fns'; // Assuming date-fns is used for formatting

interface AddCustomDateModalProps {
  isVisible: boolean;
  onClose: () => void;
  profileId: string | null; // Add profileId prop
  onAddItem: (
    newItem: Omit<CustomDate, 'id' | 'date'> & {
      date: Date | null;
      profileId: string | null;
    }
  ) => void; // Update onAddItem type
}

const AddCustomDateModal: React.FC<AddCustomDateModalProps> = ({
  isVisible,
  onClose,
  onAddItem,
  profileId,
}) => {
  const [name, setName] = useState('');
  const [date, setDate] = useState<Date | null>(null);
  const [isDatePickerVisible, setDatePickerVisible] = useState(false);
  const [nameError, setNameError] = useState('');
  const [dateError, setDateError] = useState('');

  const showDatePicker = () => {
    setDatePickerVisible(true);
  };

  const handleDateConfirm = (event: any, selectedDate?: Date) => {
    const currentDate = selectedDate || date;
    setDatePickerVisible(Platform.OS === 'ios'); // Close picker on iOS, Android handles it
    setDate(currentDate);
    setDateError(''); // Clear date error on selection
  };

  const validateInputs = () => {
    let isValid = true;
    if (!name.trim()) {
      setNameError('Date name is required.');
      isValid = false;
    } else {
      setNameError('');
    }

    if (!date) {
      setDateError('Date is required.');
      isValid = false;
    } else {
      setDateError('');
    }

    return isValid;
  };

  const handleSave = () => {
    if (validateInputs()) {
      onAddItem({ name, date, profileId }); // Include profileId
      // Clear state and close modal
      setName('');
      setDate(null);
      setNameError('');
      setDateError('');
      onClose();
    }
  };

  const handleCancel = () => {
    // Clear state and close modal
    setName('');
    setDate(null);
    setNameError('');
    setDateError('');
    onClose();
  };

  console.log('AddCustomDateModal rendering');
  return (
    <Modal
      visible={isVisible}
      animationType="slide"
      transparent={true}
      onRequestClose={handleCancel}
    >
      <View
        style={{ backgroundColor: 'rgba(0, 0, 0, 0.5)' }}
        className="flex-1 justify-center items-center"
      >
        <View className="p-6 w-full max-w-sm rounded-lg border border-border bg-card">
          <Text className="mb-4 text-xl font-bold text-text-primary">
            Add Custom Date
          </Text>

          <Input
            label="Date Name"
            placeholder="e.g., Second Date"
            value={name}
            onChangeText={setName}
            className="mb-2 rounded-md"
            error={nameError}
          />

          <TouchableOpacity onPress={showDatePicker} className="mb-4">
            <View
              className={`border rounded-md p-3 ${
                dateError ? 'border-error' : 'border-border'
              }`}
            >
              <Text
                className={`text-text-primary ${
                  date ? '':'text-text-secondary'}`}
              >
                {date ? format(date, 'PPP') : 'Select Date'}
              </Text>
            </View>
          </TouchableOpacity>
          {dateError ? (
            <Text className="mt-1 text-sm text-error">{dateError}</Text>
          ) : null}

          {isDatePickerVisible && (
            <DateTimePicker
              testID="dateTimePicker"
              value={date || new Date()} // Use current date if date is null
              mode="date"
              display="default"
              onChange={handleDateConfirm}
            />
          )}

          <View className="flex flex-row gap-2 w-full">
            <Button
              title="Cancel"
              onPress={handleCancel}
              className="w-1/2 h-1/2"
            />
            <Button
              title="Save Date"
              onPress={handleSave}
              className="w-1/2 h-1/2"
            />
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default AddCustomDateModal;
